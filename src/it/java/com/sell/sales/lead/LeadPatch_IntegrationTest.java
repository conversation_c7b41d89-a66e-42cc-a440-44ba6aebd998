package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.lead.LeadIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
      "client.config.basePath=http://localhost:9090",
      "client.search.basePath=http://localhost:9090",
      "client.entity.basePath=http://localhost:9090",
      "client.deal.basePath=http://localhost:9090",
      "client.iam.basePath=http://localhost:9090",
      "client.product.basePath=http://localhost:9090",
      "newElasticsearch.host=localhost",
      "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
public class LeadPatch_IntegrationTest {
  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(9090).jettyHeaderBufferSize(15000));

  @Autowired private ConnectionFactory connectionFactory;
  @Autowired private AmqpAdmin rabbitAdmin;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_OWNER_UPDATED_QUEUE = "q.lead.owner_updated";
  private static final String LEAD_EXCHANGE = "ex.sales";
  private static final String LEAD_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER = "sales.lead.idName.delete.search";
  private static final String TENANT_USAGE_COMMAND_QUEUE = "q.tenant.usage.collected";
  private static final String LEAD_UPDATED_QUEUE_NEW = "q.sales.updated.new";
  private static final String LEAD_BULK_UPDATED_QUEUE_V1 = "q.lead.bulk.reassign_v1";
  private static final String LEAD_BULK_UPDATED_QUEUE_V2 = "q.lead.bulk.reassign_v2";

  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private Environment environment;

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true).delete(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 1000, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
    ;
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }


  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenLeadOwnerWithUpdatePermission_tryToSetOwner_shouldUpdate() throws Exception {
    // given
    Action action = new Action();
    action.read(true).delete(true).readAll(true).update(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 1000, allowedPermissions);

    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.LeadPatch_IntegrationTest.1", LEAD_EXCHANGE, "sales.lead.updated", leadCreatedEventListener);

    MockMqListener leadUpdatedV2Listener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedV2ListenerContainer =
        initializeRabbitMqListener(
            "q.LeadPatch_IntegrationTest.2",
            LEAD_EXCHANGE,
            "sales.lead.updated.v2",
            leadUpdatedV2Listener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/UPDATE"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString(
                        "classpath:contracts/config/lead-custom-field-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString(
                        "classpath:contracts/config/lead-custom-field-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
            urlEqualTo(
                "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/products?id=99&id=55"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/product/product-response-for-id-55-and-99.json"))));

    MvcResult result =
        mockMvc
            .perform(
                patch("/v1/leads/bulk-patch/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-with-patch-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        "{\"id\":91}",
        result.getResponse().getContentAsString(), JSONCompareMode.LENIENT);
    leadCreatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-with-91-patch-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadCreatedEventContainer.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-update-v2-payload-for-patch-request.json"),
        leadUpdatedV2Listener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]",(actual, expected)->true)));

    leadUpdatedV2ListenerContainer.stop();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(
      String queue,
      String exchange,
      String eventName,
      MockMqListener mockMockMqListener) {
    Queue listenerQueue = new Queue(queue);
    rabbitAdmin.declareQueue(listenerQueue);

    Binding binding =
        BindingBuilder.bind(listenerQueue).to(new TopicExchange(exchange)).with(eventName);
    rabbitAdmin.declareBinding(binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;
    List<String> actualMessages = new LinkedList<>();

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
      actualMessages.add(this.actualMessage);
    }
  }
  boolean isValidUTCDateFormat(Object dateString){
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    try {
      return sdf.parse(dateString.toString()) != null;
    } catch (ParseException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
  }



  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(LEAD_EXCHANGE, "topic")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer =
        new PostgreSQLContainer("postgres:10.11")
            .withDatabaseName("product")
            .withUsername("test-user")
            .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
