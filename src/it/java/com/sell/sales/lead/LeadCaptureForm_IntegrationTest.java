package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.AnonymousAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CreateBucketRequest;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.utils.TestEntity;
import io.findify.s3mock.S3Mock;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@AutoConfigureEmbeddedDatabase
@TestPropertySource(properties = {
    "s3.endpoint=http://0.0.0.0:8614",
    "s3.region=sg1",
    "s3.key=some key",
    "s3.secret=some secret",
    "s3.lead-capture-form-folder=lcf",
    "s3.assets-bucket-name=kylas-assets",
    "client.iam.basePath=http://localhost:9200",
    "client.entity.basePath=http://localhost:9200"
})
@AutoConfigureMockMvc
public class LeadCaptureForm_IntegrationTest {

  public static final int port = 8614;
  public static AmazonS3 s3Client;
  private static S3Mock api = new S3Mock.Builder().withPort(port).withInMemoryBackend().build();

  public static final String TEST_BUCKET = "kylas-assets";
  public static final String TEST_REGION = "sg1";

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9200);


  @Autowired
  ResourceLoader resourceLoader;
  @Autowired
  private MockMvc mockMvc;
  private String authToken;


  @BeforeClass
  public static void setup() {
    api.start();
    EndpointConfiguration endpointConfig =
        new EndpointConfiguration("http://localhost:" + port, TEST_REGION);

    s3Client =
        AmazonS3ClientBuilder.standard()
            .withPathStyleAccessEnabled(true)
            .withEndpointConfiguration(endpointConfig)
            .withCredentials(new AWSStaticCredentialsProvider(new AnonymousAWSCredentials()))
            .build();
    s3Client.createBucket(new CreateBucketRequest(TEST_BUCKET, TEST_REGION));
  }

  @After
  public void tearDownTest() {
    ListObjectsRequest listObjectsRequest = new ListObjectsRequest().withBucketName(TEST_BUCKET);
    ObjectListing objectListing = s3Client.listObjects(listObjectsRequest);
    List<S3ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
    objectSummaries.stream()
        .filter(summary -> s3Client.doesObjectExist(TEST_BUCKET, summary.getKey()))
        .forEach(summary -> s3Client.deleteObject(
            new DeleteObjectRequest(TEST_BUCKET, summary.getKey())));
  }


  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true);
    PermissionDTO leadCaptureForm = new PermissionDTO();
    leadCaptureForm.setName("lead-capture-forms");
    leadCaptureForm.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(leadCaptureForm).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);
  }

  @Test
  public void givenLeadCaptureForm_shouldCreateAndUploadJsonFile() throws Exception {
    // given

    stubFor(WireMock.get(urlMatching("/v1/api-keys/lead-capture-form/.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(String.format("{\"apiKey\":\"abc-xyz-pqr-wer\"}"))
        )
    );

    stubFor(WireMock.head(urlEqualTo("/"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );
    stubFor(WireMock.post(urlMatching("/_mget"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );
    stubFor(WireMock.post(urlMatching("/\\..*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/lead/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/config/lead-fields.json"))));


    stubFor(WireMock.get(urlMatching("/_.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    String createFormRequestPayload =
        getResourceAsString(
            "classpath:contracts/webforms/requests/create-webForm-with-fields-request.json");
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/lead-capture-forms")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", "Bearer " + authToken)
                    .content(createFormRequestPayload))
            .andExpect(status().isCreated())
            .andReturn();
    //then
    JSONObject jsonResponse = new JSONObject(result.getResponse().getContentAsString());
    String expectedKey = "lcf/" + jsonResponse.getString("id") + ".json";

    ListObjectsRequest listObjectsRequest = new ListObjectsRequest().withBucketName(TEST_BUCKET);
    ObjectListing objectListing = s3Client.listObjects(listObjectsRequest);
    List<S3ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
    Optional<S3ObjectSummary> bucketObject = objectSummaries.stream()
        .filter(summary -> s3Client.doesObjectExist(TEST_BUCKET, summary.getKey()))
        .filter(summary -> expectedKey.equalsIgnoreCase(summary.getKey())).findFirst();

    Assertions.assertThat(bucketObject.isPresent()).isTrue();

  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}
