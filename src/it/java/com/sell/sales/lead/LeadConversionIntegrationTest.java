package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Fault;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.domain.Contact;
import com.sell.sales.lead.LeadConversionIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadConversionIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.repository.ContactRepository;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.criteria.Predicate;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONObject;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.iam.basePath=http://localhost:9090",
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.company.basePath=http://localhost:9090",
        "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
@Sql(scripts = {
    "/test-scripts/deal_contact_associations.sql",
    "/test-scripts/R__stub-lead.sql",
    "/test-scripts/R__stub-pipeline.sql",
    "/test-scripts/stub_contact.sql"
})
public class LeadConversionIntegrationTest {

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9090);

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_QUEUE = "q.sales";
  private static final String LEAD_EXCHANGE = "ex.sales";
  public static final String EVENT_NAME = "sales.lead.updated";
  private static final String LEAD_CONVERSION_EVENT_V2 = "sales.lead.conversion.v2";

  @Autowired
  private ContactRepository contactRepository;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private Environment environment;

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);

    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setName("contact");
    contactPermission.setAction(action);

    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission, contactPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
    ;
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void shouldConvertALead() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.1", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.2", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    MockMqListener leadUpdatedEventV2Listener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventV2ListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.15",
        LEAD_EXCHANGE,
        "sales.lead.updated.v2",
        leadUpdatedEventV2Listener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/1/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-conversion-response.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    MvcResult pipelineResult =
        mockMvc
            .perform(
                get("/v1/leads/1/pipeline")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andReturn();

    JSONAssert.assertEquals(
        pipelineResult.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-conversion-get-pipeline-response.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("stages[*].startedAt", (o, t1) -> true),
            new Customization("stages[*].completedAt", (o, t1) -> true)
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-updated-event-response.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        )
    );
    JSONAssert.assertEquals(
        leadConvertedEventListener.actualMessage,
        getResourceAsString("classpath:lead-converted-event-request.json"),
        JSONCompareMode.STRICT
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-updated-event-v2-request.json"),
        leadUpdatedEventV2Listener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.convertedAt", (actual, expected) -> actual == expected),
            new Customization("oldEntity.actualClosureDate", (actual, expected) -> expected == actual),
            new Customization("oldEntity.latestActivityCreatedAt", (actual, expected) -> expected == actual)

        )
    );

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
    leadUpdatedEventV2ListenerContainer.stop();
  }

  @Test
  public void givenNewDealDetails_shouldConvertALead() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.3", LEAD_EXCHANGE, "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.4", LEAD_EXCHANGE, EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );


    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/v1/deals"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"1\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String dealConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/2/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-with-conversion-details.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails[0].convertedAt", (actual, expected) -> true)
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-with-conversion-association-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)))
    );

    JSONObject jsonObject = new JSONObject(leadConvertedEventListener.actualMessage);
    assertThat(jsonObject.get("id")).isEqualTo(2);

    String createDealRequest = getResourceAsString("classpath:convert-lead-to-new-deal-request.json");
    verify(postRequestedFor(urlEqualTo("/v1/deals"))
        .withRequestBody(WireMock.equalToJson(createDealRequest,true,false))
    );

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenExistingDealId_shouldConvertALead() throws Exception {
    // given
    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.5", LEAD_EXCHANGE, "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.6", LEAD_EXCHANGE, EVENT_NAME,
        leadUpdateEventListener);

    MockMqListener leadConversionEventListener = new MockMqListener();
    SimpleMessageListenerContainer  leadConversionListenerContainer= initializeRabbitMqListener("q.LeadConversionIntegrationTest.23", LEAD_EXCHANGE, LEAD_CONVERSION_EVENT_V2,
        leadConversionEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/deals/1?mode=lead-conversion"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"1\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String dealConversionRequest =
        getResourceAsString("classpath:lead-conversion-existing-deal-request.json");
stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/3/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-with-conversion-details.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails[0].convertedAt", (actual, expected) -> true)
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-with-conversion-with-existing-deal-association-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))

        )
    );
    leadConversionEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-conversion-v2-event-with-existing-deal.json"),
        leadConversionEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.convertedAt", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    JSONObject jsonObject = new JSONObject(leadConvertedEventListener.actualMessage);
    assertThat(jsonObject.get("id")).isEqualTo(3);

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
    leadConversionListenerContainer.stop();
  }


  @Test
  public void givenLeadToConvert_tryToConvertToNewDealAndNewContact_shouldConvert() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.7", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.8", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    String accessContact =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessContact)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/v1/deals"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"1\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String dealConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-deal-and-contact.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/4/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-conversion-to-deal-and-contact-response.json"),
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails", (actual, expected) -> actual.toString().contains("CONTACT") && actual.toString().contains("DEAL")),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead4-with-conversion-association-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionAssociation.contactId", (actual, expected) -> Long.parseLong(actual.toString()) > 0),
            new Customization("conversionAssociation.dealId", (actual, expected) -> Long.parseLong(actual.toString()) > 0)
        )
    );

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenLeadToConvert_tryToConvertToExistingContactAndNewCompany_shouldConvert() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.7", LEAD_EXCHANGE, "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.8", LEAD_EXCHANGE, EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    String accessContact =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessContact)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );


    stubFor(WireMock.post(urlEqualTo("/v1/companies"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"101\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/companies/101"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"101\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String dealConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-newCompany-and-existingContact.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/4/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-conversion-to-newCompany-and-existingContact-response.json"),
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails", (actual, expected) -> actual.toString().contains("CONTACT") && actual.toString().contains("COMPANY")),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-conversion-to-newCompany-and-existingContact-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionAssociation.contactId", (actual, expected) -> Long.parseLong(actual.toString()) == 5001L),
            new Customization("conversionAssociation.companyId", (actual, expected) -> Long.parseLong(actual.toString()) > 0)
        )
    );

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenNewContactDetails_shouldConvertALead() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.16", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.17", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    MockMqListener contactCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer contactCreatedEventContainerV2 =
        initializeRabbitMqListener("q.sales_1",
            LEAD_EXCHANGE, "sales.contact.created.v2",
            contactCreatedEventListenerV2);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    String accessContact =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessContact)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String contactConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-contact.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/6/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(contactConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-conversion-to-contact-response.json"),
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails", (actual, expected) -> actual.toString().contains("CONTACT")),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-convert-to-contact-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("createdAt", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> true),
            new Customization("convertedAt", (actual, expected) -> true),
            new Customization("actualClosureDate", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> true),
            new Customization("conversionAssociation.contactId", (actual, expected) -> Long.parseLong(actual.toString()) > 0)
        )
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-convert-to-contact-createdV2-Event.json"),
        contactCreatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("entity.createdAt", (actual, expected) -> true),
            new Customization("entity.updatedAt", (actual, expected) -> true),
            new Customization("convertedAt", (actual, expected) -> true)
        ));

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
    contactCreatedEventContainerV2.stop();
  }


  @Test
  public void givenLeadToConvert_tryToConvertToNewContactAndDealThrowError_shouldRollbackContact() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.9", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.10", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );


    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/v1/deals"))
        .willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER)));

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/100/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String dealConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-deal-and-contact.json");
stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/4/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    leadConvertedEventListener.latch.await(2, TimeUnit.SECONDS);
    List<Contact> all = contactRepository.findAll((root, query, cb) -> {
      Predicate firstName = cb.equal(root.get("firstName"), "tony");
      Predicate tenantId = cb.equal(root.get("tenantId"), 100L);
      Predicate lastName = cb.equal(root.get("lastName"), "stark");
      return cb.and(firstName,lastName,tenantId);
    });
    assertThat(all).isEmpty();

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenNewCompanyDetails_shouldConvertALead() throws Exception {
    // given

    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.11", LEAD_EXCHANGE, "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.12", LEAD_EXCHANGE, EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );


    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/v1/companies"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"101\",\"name\":\"Company 1\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));

    String dealConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-new-company.json");
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/5/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(dealConversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-convert-to-new-company-response.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails[0].convertedAt", (actual, expected) -> true)
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-convert-to-new-company-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        )
    );

    JSONObject jsonObject = new JSONObject(leadConvertedEventListener.actualMessage);
    assertThat(jsonObject.get("id")).isEqualTo(5);

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenExistingCompany_shouldConvertALead() throws Exception {
    // given
    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.13", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.14", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(okForContentType(MediaType.APPLICATION_JSON_VALUE, accessDto)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(okForContentType(MediaType.APPLICATION_JSON_VALUE, "[]")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(okForContentType(MediaType.APPLICATION_JSON_VALUE, "[]")));


    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse().withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String companyResponse = getResourceAsString("classpath:contracts/company/response/company-by-id.json");
    stubFor(WireMock.get(urlEqualTo("/v1/companies/200"))
        .willReturn(okForContentType(MediaType.APPLICATION_JSON_VALUE, companyResponse)));

    String leadConversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-with-existing-company.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));
    String jwtToken = "eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B-LagaQfYW4ipny4x_QZWovE529xKQEPFSfO243QB6k";
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/50/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + jwtToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(leadConversionRequest))
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-convert-to-existing-company-response.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails[0].convertedAt", (actual, expected) -> true)
        ));

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-convert-to-existing-company-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));
    JSONObject jsonObject = new JSONObject(leadConvertedEventListener.actualMessage);
    assertThat(jsonObject.get("id")).isEqualTo(50);

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
  }

  @Test
  public void givenLeadToConvert_tryToConvertToNewDealAndExistingContactAndNewCompany_shouldConvert() throws Exception {
    // given
    MockMqListener leadConversionEventListener = new MockMqListener();
    SimpleMessageListenerContainer  leadConversionListenerContainer= initializeRabbitMqListener("q.LeadConversionIntegrationTest.22", LEAD_EXCHANGE, LEAD_CONVERSION_EVENT_V2,
        leadConversionEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    String accessContact =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessContact)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/v1/deals"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"1\"}"))
    );

    stubFor(WireMock.post(urlEqualTo("/v1/companies"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"7878\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/companies/7878"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"7878\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String conversionRequest =
        getResourceAsString("classpath:contracts/lead/requests/lead-conversion-to-deal-contact-company-request.json");
    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/7/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(conversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        result.getResponse().getContentAsString(),
        getResourceAsString("classpath:lead-convert-to-deal-contact-company-response.json"),
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails", (actual, expected) -> actual.toString().contains("CONTACT") && actual.toString().contains("DEAL")),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadConversionEventListener.latch.await(3,TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-conversion-v2-event.json"),
        leadConversionEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.convertedAt", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadConversionListenerContainer.stop();
  }

  @Test
  public void givenAlreadyConvertedLead_shouldReConvert() throws Exception {
    MockMqListener leadConvertedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConvertedListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.5", LEAD_EXCHANGE,
        "lead.converted",
        leadConvertedEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdateListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.6", LEAD_EXCHANGE,
        EVENT_NAME,
        leadUpdateEventListener);

    MockMqListener leadConversionEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadConversionListenerContainer = initializeRabbitMqListener("q.LeadConversionIntegrationTest.23", LEAD_EXCHANGE,
        LEAD_CONVERSION_EVENT_V2,
        leadConversionEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.new.2", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );
    String accessContact =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessContact)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/deals/110?mode=lead-conversion"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"110\"}"))
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(WireMock.put(urlEqualTo("/v1/deals/110"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"id\": \"110\"}"))
    );

    String conversionRequest =
        getResourceAsString("classpath:lead-reconversion-existing-deal-new-contact-request.json");
stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/10/convert")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(conversionRequest)
            )
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-with-reconversion-details-of-existing-deal-new-company.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails", (actual, expected) -> actual.toString().contains("CONTACT") && actual.toString().contains("DEAL"))
        ));
    MvcResult response =
        mockMvc
            .perform(
                get("/v1/leads/10")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    leadConversionEventListener.latch.await(1, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-response-with-conversion-details.json"),
        response.getResponse().getContentAsString(),
        new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("conversionDetails",
                (actual, expected) -> actual.toString().contains("CONTACT") && actual.toString().contains("DEAL") && actual.toString()
                    .contains("COMPANY"))));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-reconversion-with-existing-deal-new-contact-association-updated-event.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        )
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:lead-conversion-v2-event-with-existing-deal-new-contact.json"),
        leadConversionEventListener.actualMessage, JSONCompareMode.LENIENT);

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-v2-event-for-converted-lead.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.conversionAssociations[*].convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.conversionAssociations[*].convertedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.conversionAssociation.contactId", (actual, expected) -> true)
        ));

    JSONObject jsonObject = new JSONObject(leadConvertedEventListener.actualMessage);
    assertThat(jsonObject.get("id")).isEqualTo(10);

    leadUpdateListenerContainer.stop();
    leadConvertedListenerContainer.stop();
    leadConversionListenerContainer.stop();
  }

  boolean isValidUTCDateFormat(Object dateString) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    try {
      return sdf.parse(dateString.toString()) != null;
    } catch (ParseException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queueName, String exchange, String eventName,
      LeadConversionIntegrationTest.MockMqListener mockMockMqListener) {
    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);
    Binding binding = BindingBuilder.bind(queue)
        .to(new TopicExchange(exchange))
        .with(eventName);

    rabbitAdmin.declareBinding(
        binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.withExchange(LEAD_EXCHANGE, "topic").withQueue(LEAD_QUEUE).start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("product")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
