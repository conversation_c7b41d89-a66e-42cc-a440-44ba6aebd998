package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.sell.sales.core.domain.EntityType.EMAIL;
import static com.sell.sales.core.domain.EntityType.MEETING;
import static com.sell.sales.core.domain.EntityType.NOTE;
import static com.sell.sales.core.domain.EntityType.TASK;
import static com.sell.sales.infra.mq.event.Metadata.Action.CREATED;
import static java.util.Collections.emptySet;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.CoreAuthentication;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.MetaInfo;
import com.sell.sales.infra.mq.event.Metadata;
import com.sell.sales.infra.mq.event.UpdateLeadMetaInfoCommand;
import com.sell.sales.lead.UpdateLeadMetaInfoIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.UpdateLeadMetaInfoIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.pipeline.service.LeadPipelineService;
import com.sell.sales.service.lead.LeadService;
import com.sell.sales.service.lead.conversion.LeadConversionService;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
    })
@AutoConfigureMockMvc
public class UpdateLeadMetaInfoIntegrationTest {

  @Autowired
  private LeadService leadService;
  @Autowired
  private LeadPipelineService leadPipelineService;
  @Autowired
  private LeadConversionService leadConversionService;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(9090).jettyHeaderBufferSize(15000));

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_QUEUE = "q.sales";
  private static final String LEAD_EXCHANGE = "ex.sales";
  private static final String LEAD_METAINFO_UPDATE_COMMAND = "sales.lead.metainfo.updated";
  private static final String PRODUCTIVITY_EXCHANGE = "ex.productivity";
  private static final String EMAIL_EXCHANGE = "ex.email";
  private static final String MEETING_EXCHANGE = "ex.meeting";
  private static final String TASK_UPDATE_QUEUE = "q.task.update.lead.metaInfo.productivity";
  public static final String TASK_UPDATE_COMMAND = "task.update.lead.metaInfo";
  private static final String NOTE_QUEUE = "q.note.update.lead.metaInfo.productivity";
  public static final String NOTE_UPDATE_COMMAND = "note.update.lead.metaInfo";
  private static final String MEETING_QUEUE = "q.meeting.update.lead.metaInfo.productivity";
  private static final String EMAIl_QUEUE = "q.email.update.lead.metaInfo.productivity";
  public static final String MEETING_UPDATE_COMMAND = "meeting.update.lead.metaInfo";
  public static final String EMAIL_UPDATE_COMMAND = "email.update.lead.metaInfo";
  private static final String LEAD_METAINFO_UPDATED_EVENT_V3 = "sales.lead.metainfo.updated.v3";

  private static final String LEAD_METAINFO_UPDATED_EVENT_V3_QUEUE = "q.sales.1";



  @Before
  public void setUp() {
    Action action = new Action();
    action.read(true).readAll(true).updateAll(true).write(true).update(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    PermissionDTO configPermission = new PermissionDTO();
    configPermission.setName("config");
    configPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    String authToken = TestEntity.getJwt(10, 1000, allowedPermissions);

    SecurityContextHolder.getContext()
        .setAuthentication(
            new CoreAuthentication("10", String.valueOf(1000), allowedPermissions, authToken));

  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
  }

  @Test
  public void givenLead_withMetaInfo_shouldCreateIt() throws IOException {
    //given
    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlMatching("/v1/entities/LEAD/fields.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(WireMock.post(urlEqualTo("/1000-id-name/store?timeout=1m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    //when
    Lead createdLead = leadService.create(lead);

    //then
    assertThat(createdLead).isNotNull();
    MetaInfo metaInfo = createdLead.getMetaInfo();
    assertThat(metaInfo).isNotNull();
    assertThat(createdLead.getId()).isEqualTo(metaInfo.getLead().getId());
    assertThat(metaInfo.isNew()).isTrue();
    assertThat(metaInfo.getLatestActivityCreatedAt()).isNull();
    assertThat(metaInfo.getMeetingScheduledOn()).isNull();
    assertThat(metaInfo.getTaskDueOn()).isNull();
  }

  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenLead_withMetaInfo_shouldUpdateIt() throws Exception {
    // given
    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    Lead lead = leadService.get(91L);
    lead.setOwnerId(null);
    lead.setFirstName("John");
    lead.setOwnerId(12L);
    Lead oldLead = new Lead();
    Map<String, Object> metaData = new HashMap<>();
    metaData.put("idNameStore",new HashMap<>());
    oldLead.setMetaData(metaData);
    oldLead.setId(12L);
    oldLead.setTenantId(12L);
    oldLead.setOwnerId(12L);
    oldLead.setVersion(12);
    Lead updatedLead = leadService.update(lead, null, oldLead);

    //then
    MetaInfo metaInfo = updatedLead.getMetaInfo();
    assertThat(updatedLead.getId()).isEqualTo(metaInfo.getLead().getId());
    assertThat(metaInfo.isNew()).isFalse();
    assertThat(metaInfo.getLatestActivityCreatedAt()).isNotNull();
    assertThat(metaInfo.getLatestActivityCreatedAt()).isBeforeOrEqualTo(new Date());
    assertThat(metaInfo.getMeetingScheduledOn()).isNull();
    assertThat(metaInfo.getTaskDueOn()).isNull();

  }

  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenUpdatedMetaInfo_withTaskAddedOnLead_shouldUpdateItAndRaiseUpdatedEvent() throws Exception {

    // given

    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.1", PRODUCTIVITY_EXCHANGE,
        TASK_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.2", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(new Date(), new Date(), 91L, 1000L, 2L, null, TASK, null);
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    //then
     JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> o1 != null && o2 != null),
            new Customization("meetingScheduledOn", (o1, o2) -> true)
        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
  }


  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenLeadWithTask_andUniquenessStrategySet_shouldUpdateMetaInfo_andRaiseUpdatedEvent() throws Exception {

    // given

    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.3", PRODUCTIVITY_EXCHANGE,
        TASK_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.4", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);


    MockMqListener leadMetaInfoUpdateV3EventListener = new MockMqListener();
    SimpleMessageListenerContainer leadMetaInfoUpdateV3EventContainer = initializeRabbitMqListener("lead.metainfo.update.5", LEAD_EXCHANGE, LEAD_METAINFO_UPDATED_EVENT_V3,
        leadMetaInfoUpdateV3EventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"PHONE\"}")
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );
    Metadata metadata = new Metadata(1000L, 2L, TASK, 91L, CREATED, null, emptySet(), null);

    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(new Date(), new Date(), 91L, 1000L, 2L, null, TASK, metadata);
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, TASK_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);
    leadMetaInfoUpdateV3EventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> o1 != null && o2 != null),
            new Customization("meetingScheduledOn", (o1, o2) -> true)
        ));

//    JSONAssert.assertEquals(
//        getResourceAsString("classpath:contracts/lead/responses/lead-update-v3-event-response-via-task.json"),
//        leadMetaInfoUpdateV3EventListener.actualMessage,
//        new CustomComparator(
//            JSONCompareMode.STRICT,
//            new Customization("entity.createdAt", (o1, o2) -> true),
//            new Customization("entity.updatedAt", (o1, o2) -> true),
//            new Customization("entity.actualClosureDate", (o1, o2) -> true),
//            new Customization("entity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("entity.taskDueOn", (o1, o2) -> true),
//            new Customization("entity.meetingScheduledOn", (o1, o2) -> true),
//            new Customization("oldEntity.createdAt", (o1, o2) -> true),
//            new Customization("oldEntity.updatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.actualClosureDate", (o1, o2) -> true),
//            new Customization("oldEntity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.taskDueOn", (o1, o2) -> true),
//            new Customization("oldEntity.meetingScheduledOn", (o1, o2) -> true)
//        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
    leadMetaInfoUpdateV3EventContainer.stop();
  }


  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenUpdatedMetaInfo_withNoteAddedOnLead_shouldUpdateItAndRaiseUpdatedEvent() throws Exception {

    // given

    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.6", PRODUCTIVITY_EXCHANGE,
        NOTE_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.7", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(null, new Date(), 91L, 1000L, 2L, null, NOTE, null);
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, NOTE_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> true),
            new Customization("meetingScheduledOn", (o1, o2) -> true)
        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenUpdatedMetaInfo_withEmailAddedOnLead_shouldUpdateItAndRaiseUpdatedEvent() throws Exception {

    // given

    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.8", EMAIL_EXCHANGE,
        EMAIL_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.9", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(null, new Date(), 91L, 1000L, 2L, null, EMAIL, null);
    rabbitTemplate.convertAndSend(EMAIL_EXCHANGE, EMAIL_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> true),
            new Customization("meetingScheduledOn", (o1, o2) -> true)
        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/test_lead_with_meta_info.sql")
  public void givenUpdatedMetaInfo_withMeetingScheduledOnLead_shouldUpdateItAndRaiseUpdatedEvent() throws Exception {

    // given

    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.10", MEETING_EXCHANGE,
        MEETING_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.11", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);

    MockMqListener leadMetaInfoUpdateV3EventListener = new MockMqListener();
    SimpleMessageListenerContainer leadMetaInfoUpdateV3EventContainer = initializeRabbitMqListener("lead.metainfo.update.12", LEAD_EXCHANGE, LEAD_METAINFO_UPDATED_EVENT_V3,
        leadMetaInfoUpdateV3EventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(null, new Date(), 91L, 1000L, 2L, new Date(), MEETING, null);
    rabbitTemplate.convertAndSend(MEETING_EXCHANGE, MEETING_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);
    leadMetaInfoUpdateV3EventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> true),
            new Customization("meetingScheduledOn", (o1, o2) -> o1 != null && o2 != null)
        ));

//    JSONAssert.assertEquals(
//        getResourceAsString("classpath:contracts/lead/responses/lead-update-v3-event-response-via-meeting.json"),
//        leadMetaInfoUpdateV3EventListener.actualMessage,
//        new CustomComparator(
//            JSONCompareMode.STRICT,
//            new Customization("entity.createdAt", (o1, o2) -> true),
//            new Customization("entity.updatedAt", (o1, o2) -> true),
//            new Customization("entity.actualClosureDate", (o1, o2) -> true),
//            new Customization("entity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("entity.taskDueOn", (o1, o2) -> true),
//            new Customization("entity.meetingScheduledOn", (o1, o2) -> true),
//            new Customization("oldEntity.createdAt", (o1, o2) -> true),
//            new Customization("oldEntity.updatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.actualClosureDate", (o1, o2) -> true),
//            new Customization("oldEntity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.taskDueOn", (o1, o2) -> true),
//            new Customization("oldEntity.meetingScheduledOn", (o1, o2) -> true)
//        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
    leadMetaInfoUpdateV3EventContainer.stop();
  }


  @Test
  @Sql("/test-migrations/lead-pipeline/create-test-data-for-lead-pipeline-with-meta-info.sql")
  public void givenStageToActivate_shouldUpdateMetaInfo() throws Exception {
    // given
    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    // when
    leadPipelineService.setActiveStage(1L, 1L, 3L, "Closed");

    // then
    Lead lead = leadService.get(1L);
    MetaInfo metaInfo = lead.getMetaInfo();
    assertThat(lead.getId()).isEqualTo(metaInfo.getLead().getId());
    assertThat(metaInfo.isNew()).isFalse();
    assertThat(metaInfo.getLatestActivityCreatedAt()).isBeforeOrEqualTo(new Date());
    assertThat(metaInfo.getMeetingScheduledOn()).isNull();
    assertThat(metaInfo.getTaskDueOn()).isNull();
  }

  @Test
  @Sql("/test-migrations/lead/lead_with_meta_info_and_products.sql")
  public void givenUpdatedMetaInfoWithProducts_withNoteAddedOnLead_shouldUpdateItAndRaiseUpdatedEvent() throws Exception {

    // given
    MockMqListener leadUpdateMetaInfoEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedMetaInfoEventContainer = initializeRabbitMqListener("lead.metainfo.update.13", PRODUCTIVITY_EXCHANGE,
        NOTE_UPDATE_COMMAND,
        leadUpdateMetaInfoEventListener);

    MockMqListener leadUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("lead.metainfo.update.14", LEAD_EXCHANGE,
        LEAD_METAINFO_UPDATE_COMMAND,
        leadUpdateEventListener);

    MockMqListener leadMetaInfoUpdateV3EventListener = new MockMqListener();
    SimpleMessageListenerContainer leadMetaInfoUpdateV3EventContainer = initializeRabbitMqListener("lead.metainfo.update.15", LEAD_EXCHANGE, LEAD_METAINFO_UPDATED_EVENT_V3,
        leadMetaInfoUpdateV3EventListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    String idNameResponse = getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(WireMock.post(urlMatching(
        "/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.post(urlMatching("/1000-id-name.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );
    
    //when
    UpdateLeadMetaInfoCommand event = new UpdateLeadMetaInfoCommand(null, new Date(), 91L, 1000L, 2L, null, NOTE, null);
    rabbitTemplate.convertAndSend(PRODUCTIVITY_EXCHANGE, NOTE_UPDATE_COMMAND, event);

    leadUpdateEventListener.latch.await(3, TimeUnit.SECONDS);
    leadMetaInfoUpdateV3EventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/lead-update-jms-response-with-meta-info-and-products.json"),
        leadUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("actualClosureDate", (o1, o2) -> true),
            new Customization("latestActivityCreatedAt", (o1, o2) -> o1 != null),
            new Customization("taskDueOn", (o1, o2) -> true),
            new Customization("meetingScheduledOn", (o1, o2) -> true)
        ));
    leadUpdatedEventContainer.stop();

//    JSONAssert.assertEquals(
//        getResourceAsString("classpath:contracts/lead/responses/lead-update-v3-event-response-via-note.json"),
//        leadMetaInfoUpdateV3EventListener.actualMessage,
//        new CustomComparator(
//            JSONCompareMode.STRICT,
//            new Customization("entity.createdAt", (o1, o2) -> true),
//            new Customization("entity.updatedAt", (o1, o2) -> true),
//            new Customization("entity.actualClosureDate", (o1, o2) -> true),
//            new Customization("entity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("entity.taskDueOn", (o1, o2) -> true),
//            new Customization("entity.meetingScheduledOn", (o1, o2) -> true),
//            new Customization("oldEntity.createdAt", (o1, o2) -> true),
//            new Customization("oldEntity.updatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.actualClosureDate", (o1, o2) -> true),
//            new Customization("oldEntity.latestActivityCreatedAt", (o1, o2) -> true),
//            new Customization("oldEntity.taskDueOn", (o1, o2) -> true),
//            new Customization("oldEntity.meetingScheduledOn", (o1, o2) -> true)
//        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedMetaInfoEventContainer.stop();
    leadMetaInfoUpdateV3EventContainer.stop();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queueName, String exchange, String eventName, MockMqListener mockMockMqListener) {
    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);

    Binding binding = BindingBuilder.bind(queue)
        .to(new TopicExchange(exchange))
        .with(eventName);

    rabbitAdmin.declareBinding(
        binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(LEAD_EXCHANGE, "topic")
          .withExchange(PRODUCTIVITY_EXCHANGE, "topic")
          .withExchange(MEETING_EXCHANGE, "topic")
          .withExchange(EMAIL_EXCHANGE, "topic")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("product")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
