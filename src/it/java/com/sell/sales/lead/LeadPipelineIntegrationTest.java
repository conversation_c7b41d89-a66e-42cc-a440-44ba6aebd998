package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.lead.LeadPipelineIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadPipelineIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
      "client.config.basePath=http://localhost:9090",
      "client.search.basePath=http://localhost:9090",
      "client.entity.basePath=http://localhost:9090",
      "client.deal.basePath=http://localhost:9090",
      "client.iam.basePath=http://localhost:9090",
      "newElasticsearch.host=localhost",
      "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
public class LeadPipelineIntegrationTest {

  @Rule public WireMockRule wireMockRule = new WireMockRule(9090);

  @Autowired private ConnectionFactory connectionFactory;
  @Autowired private AmqpAdmin rabbitAdmin;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_QUEUE = "sales.lead.updated.v2";
  private static final String LEAD_UPDATED_QUEUE_LISTENER = "sales.lead.updated.v2.workflow";
  private static final String LEAD_EXCHANGE = "ex.sales";

  @Autowired private MockMvc mockMvc;
  @Autowired private ResourceLoader resourceLoader;
  @Autowired private Environment environment;

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(1, 1, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
    ;
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql("/pipeline-lead/V1.3.2__create-test-data-for-lead-pipeline.sql")
  public void givenStageToActivate_shouldActivateAndRaiseEvents() throws Exception {
    // given
    long leadId = 1L;
    long stageId = 3L;
    MockMqListener leadCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            LEAD_UPDATED_QUEUE_LISTENER,
            LEAD_EXCHANGE,
            "sales.lead.updated.v2",
            leadCreatedEventListenerV2);

    MockMqListener leadCreatedEventListener = new MockMqListener();

    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.lead.updated.1",
            LEAD_EXCHANGE,
            "sales.lead.updated",
            leadCreatedEventListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/config/lead-fields.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-1-idName-index-response.json");

    stubFor(
        WireMock.post(urlMatching("/1-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    String payload =
        getResourceAsString("classpath:contracts/lead-pipeline/requests/activate-stage.json");
    // when
    mockMvc
        .perform(
            post("/v1/leads/" + leadId + "/pipeline-stages/" + stageId + "/activate")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(payload)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    // then
    leadCreatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/mq/lead-updated-event-payload-v2.json"),
        leadCreatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT, new Customization("entity.updatedAt", (o1, o2) -> true)));
    leadCreatedEventContainer.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/mq/lead-updated-event-payload-on-pipelineStage-activate.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT, new Customization("updatedAt", (o1, o2) -> true)));
  }

  @Test
  @Sql("/pipeline-lead/V1.3.2__create-test-data-for-lead-pipeline.sql")
  public void givenLeadId_shouldGetPipelineResponse() throws Exception {
    // given
    long leadId = 1L;

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/config/lead-fields.json"))));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-1-idName-index-response.json");

    stubFor(
        WireMock.post(urlMatching("/1-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    String payload =
        getResourceAsString("classpath:contracts/lead-pipeline/requests/activate-stage.json");
    // when
    MvcResult mvcResult = mockMvc
        .perform(
            get("/v1/leads/" + leadId + "/pipeline")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(payload)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    // then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/pipeline-response-by-lead-id.json"),
        mvcResult.getResponse().getContentAsString(),
            JSONCompareMode.LENIENT);
  }


  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(
      String queueName,
      String exchange,
      String eventName,
      LeadPipelineIntegrationTest.MockMqListener mockMockMqListener) {

    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);
    Binding binding =
        BindingBuilder.bind(queue).to(new TopicExchange(exchange)).with(eventName);

    rabbitAdmin.declareBinding(binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(LEAD_EXCHANGE, "topic")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer =
        new PostgreSQLContainer("postgres:10.11")
            .withDatabaseName("product")
            .withUsername("test-user")
            .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
