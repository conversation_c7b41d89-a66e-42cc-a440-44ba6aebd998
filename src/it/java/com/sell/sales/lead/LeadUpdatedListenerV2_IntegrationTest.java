package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.sell.sales.core.domain.EntityType.LEAD;
import static com.sell.sales.infra.mq.event.Metadata.Action.UPDATED;
import static java.util.Collections.emptySet;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.times;
import static org.skyscreamer.jsonassert.JSONAssert.assertEquals;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.controller.request.lead.LeadUpdateRequest;
import com.sell.sales.controller.request.lead.Pipeline;
import com.sell.sales.controller.request.lead.PipelineStage;
import com.sell.sales.controller.request.lead.ProductDTO;
import com.sell.sales.core.event.ExecutionLogStatusEvent;
import com.sell.sales.infra.mq.WorkflowExecutionLogStatusEventPublisher;
import com.sell.sales.infra.mq.event.EditPropertyActionType;
import com.sell.sales.infra.mq.event.LeadUpdateEventPayload;
import com.sell.sales.infra.mq.event.Metadata;
import com.sell.sales.lead.LeadUpdatedListenerV2_IntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadUpdatedListenerV2_IntegrationTest.TestEnvironmentSetup;
import com.sell.sales.repository.LeadRepository;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.AfterClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.BDDMockito;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
        "newElasticsearch.host=localhost",
        "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
@Sql("/test-migrations/lead/lead_update_with_pipeline_v2.sql")
public class LeadUpdatedListenerV2_IntegrationTest {

  private static final String EX_WORKFLOW = "ex.workflow";
  private static final String EVENT = "workflow.lead.update";
  private static final String LEAD_UPDATE_EVENT_QUEUE_ON_WORKFLOW = "q.sales.lead.update.workflow";
  private static final String EX_SALES = "ex.sales";
  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(9090).jettyHeaderBufferSize(15000));


  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private LeadRepository leadRepository;
  @Autowired
  private ResourceLoader resourceLoader;
  @MockBean
  private WorkflowExecutionLogStatusEventPublisher workflowExecutionLogStatusEventPublisher;

  private SimpleMessageListenerContainer container;
  private MockMqListener mockMqListener = new MockMqListener();

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");


  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void givenUpdateLeadCommand_byWorkflow_shouldUpdateIt() throws InterruptedException, IOException, JSONException {
    // given
    long tenantId = 100L;

    MockMqListener mockMqListener = new MockMqListener();
    MockMqListener mockMqListenerV2 = new MockMqListener();

    initializeRabbitMqListener(LEAD_UPDATE_EVENT_QUEUE_ON_WORKFLOW, EX_SALES,"sales.lead.updated.v2",mockMqListenerV2);
    initializeRabbitMqListener("lead.updated.1", EX_SALES,"sales.lead.updated",mockMqListener);

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/100/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/200/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/config/lead-fields-with-pipeline.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/config/lead-fields-with-pipeline.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/users/2000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        get(urlEqualTo("/v1/products?id=2"))
            .willReturn(
                ok("[{\"id\":1,\"name\":\"cellphone\",\"isActive\":true},{\"id\":2,\"name\":\"cellphone2\",\"isActive\":true}]")
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/es/get-lead-idName-response-multiple-response.json");

    stubFor(WireMock.post(urlPathMatching("/100-id-name/_search.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(idNameResponse)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    Metadata metadata = new Metadata(tenantId, 2000L, LEAD, 4000L, UPDATED, "W1", emptySet(), null).withEventId(100L);
    LeadUpdateRequest leadUpdateRequest = getLeadUpdateRequest(4000L, "Hari", "updated stark");
    Map<String, Object> customFieldValues = new HashMap<>();
    Map<String, Object> copyCustomFieldValues = new HashMap<>();
    copyCustomFieldValues.put("cfMultiPick",Arrays.asList(123L,124L));
    customFieldValues.put("myCity","Pune 93");
    leadUpdateRequest.setCustomFieldValues(customFieldValues);
    leadUpdateRequest.setUtmTerm("Term Updated By Workflow");
    PipelineStage stage = new PipelineStage(12L,"Won");
    leadUpdateRequest.setPipeline(new Pipeline(2L,"Lead Pipeline",stage));
    leadUpdateRequest.setPipelineStageReason("Not interested");
    leadUpdateRequest.setScore(123.5);

    LeadUpdateRequest copyLeadUpdateRequest = new LeadUpdateRequest();
    copyLeadUpdateRequest.setCustomFieldValues(copyCustomFieldValues);
    HashMap<EditPropertyActionType, LeadUpdateRequest> leadUpdateRequestHashMap = new HashMap<>();
    leadUpdateRequestHashMap.put(EditPropertyActionType.REPLACE,leadUpdateRequest);
    leadUpdateRequestHashMap.put(EditPropertyActionType.APPEND,copyLeadUpdateRequest);
    LeadUpdateEventPayload event = new LeadUpdateEventPayload(leadUpdateRequestHashMap, metadata);



    //when
    rabbitTemplate.convertAndSend(EX_WORKFLOW, EVENT, event,message -> {
      message.getMessageProperties().setHeader("replyToExchange","ex.workflow");
      message.getMessageProperties().setHeader("replyToEvent","workflow.lead.update.reply");
      return message;
    });
    mockMqListenerV2.latch.await(3, TimeUnit.SECONDS);

    String expectedJsonV2 = getResourceAsString("classpath:contracts/mq/lead-v2/lead-update-event-payload-v2_2.json");

    assertEquals(expectedJsonV2, mockMqListenerV2.actualMessage, new CustomComparator(
        JSONCompareMode.NON_EXTENSIBLE,
        new Customization("entity.updatedAt", (o1, o2) -> true),
        new Customization("entity.createdAt", (o1, o2) -> true),
        new Customization("entity.actualClosureDate", (o1, o2) -> true),
        new Customization("entity.latestActivityCreatedAt", (o1, o2) -> true),
        new Customization("oldEntity.latestActivityCreatedAt", (o1, o2) -> true),
        new Customization("oldEntity.updatedAt", (o1, o2) -> true),
        new Customization("oldEntity.createdAt", (o1, o2) -> true),
        new Customization("entity.conversionAssociation.id", (actual, expected) -> true),
        new Customization("oldEntity.conversionAssociation.id", (actual, expected) -> true)
    ));

    String leadUpdatedEvent = getResourceAsString("classpath:contracts/mq/lead-v2/lead-update-event-by-workflow.json");

    assertEquals(leadUpdatedEvent, mockMqListener.actualMessage, new CustomComparator(
        JSONCompareMode.NON_EXTENSIBLE,
        new Customization("updatedAt", (o1, o2) -> true),
        new Customization("createdAt", (o1, o2) -> true),
        new Customization("actualClosureDate", (o1, o2) -> true),
        new Customization("latestActivityCreatedAt", (o1, o2) -> true)
    ));


    ArgumentCaptor<ExecutionLogStatusEvent> argumentCaptor = ArgumentCaptor.forClass(ExecutionLogStatusEvent.class);
    BDDMockito.verify(workflowExecutionLogStatusEventPublisher, times(1)).publishExecutionLogStatusUpdateEvent(argumentCaptor.capture(),eq("ex.workflow"),eq("workflow.lead.update.reply"));
    ExecutionLogStatusEvent value = argumentCaptor.getValue();
    Assertions.assertThat(value.getEventId()).isEqualTo(100L);
    Assertions.assertThat(value.getStatus()).isEqualTo("SUCCESS");
    Assertions.assertThat(value.getStatusCode()).isEqualTo(200);
    Assertions.assertThat(value.getErrorCode()).isEqualTo(null);
    Assertions.assertThat(value.getErrorMessage()).isEqualTo(null);

    verify(exactly(0), getRequestedFor(urlEqualTo("/v1/internal/share/access/LEAD/READ")));
  }

  private LeadUpdateRequest getLeadUpdateRequest(Long id, String firstName, String lastName) {
    LeadUpdateRequest lead = new LeadUpdateRequest();
    lead.setFirstName(firstName);
    lead.setLastName(lastName);
    lead.setCity("Pune");
    lead.setState("Maharashtra");
    lead.setProducts(Stream.of(new ProductDTO(2L, "cellphone2")).collect(Collectors.toList()));
    return lead;
  }

  private void initializeRabbitMqListener(String queueName, String exchangeName, String eventName) {
    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);
    TopicExchange exchange = new TopicExchange(exchangeName);
    rabbitAdmin.declareExchange(exchange);
    rabbitAdmin.declareBinding(BindingBuilder.bind(queue).to(exchange).with(eventName));
    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMqListener, "receiveMessage");
    container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
  }

  private void initializeRabbitMqListener(String queueName, String exchangeName, String eventName,MockMqListener listener) {
    Queue queue = new Queue(queueName);
    rabbitAdmin.declareQueue(queue);
    TopicExchange exchange = new TopicExchange(exchangeName);
    rabbitAdmin.declareExchange(exchange);
    rabbitAdmin.declareBinding(BindingBuilder.bind(queue).to(exchange).with(eventName));
    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(listener, "receiveMessage");
    container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queueName);
    container.setMessageListener(listenerAdapter);
    container.start();
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(EX_WORKFLOW, "topic")
          .withExchange(EX_SALES, "topic").withQueue(LEAD_UPDATE_EVENT_QUEUE_ON_WORKFLOW)
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }
  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("sales")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
