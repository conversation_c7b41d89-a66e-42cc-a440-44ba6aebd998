package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.sell.sales.controller.response.lead.PhoneNumberResponse;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.entity.model.PhoneType;
import com.sell.sales.lead.LeadIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.service.client.iam.web.response.Plan;
import com.sell.sales.service.client.iam.web.response.Tenant;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
        "newElasticsearch.host=localhost",
        "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
public class LeadIntegrationTest {

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(9090).jettyHeaderBufferSize(15000));

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_OWNER_UPDATED_QUEUE = "q.lead.owner_updated";
  private static final String LEAD_EXCHANGE = "ex.sales";
  private static final String LEAD_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER = "sales.lead.idName.delete.search";
  private static final String TENANT_USAGE_COMMAND_QUEUE = "q.tenant.usage.collected";
  private static final String LEAD_UPDATED_QUEUE_NEW = "q.sales.updated.new";
  private static final String LEAD_BULK_UPDATED_QUEUE_V1 = "q.lead.bulk.reassign_v1";
  private static final String LEAD_BULK_UPDATED_QUEUE_V2 = "q.lead.bulk.reassign_v2";

  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private Environment environment;

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true).delete(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 1000, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
    ;
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void shouldCreateLead() throws Exception {
    // given

    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.1", LEAD_EXCHANGE, "sales.lead.created", leadCreatedEventListener);

    MockMqListener idNameCreateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameCreateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.create.search.1",
            LEAD_EXCHANGE,
            "sales.idName.create",
            idNameCreateCommandListener);

    MockMqListener leadCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.v2.1", LEAD_EXCHANGE, "sales.lead.created.v2", leadCreatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-import-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/lead/uniqueness-on-mail-phone.json"))));

    stubFor(
        WireMock.post(urlEqualTo("/1000-id-name/store?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));
    stubFor(
        WireMock.post(urlMatching("/1000-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/es/id-name-empty-response.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/users/22"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/products?id=2"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-update-request-with-active-products.json"))));
    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/requests/create-lead-with-all-field.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-created-response.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("leadUtms[0].createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("id", (actual, expected) -> true)));
    leadCreatedEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-create-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("id", (actual, expected) -> true),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))
        ));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-create-jms-request-v2.json"),
        leadCreatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.id", (actual, expected) -> true),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.leadUtms[0].createdAt", (actual, expected) ->true),
            new Customization("entity.id", (actual, expected) -> true),
            new Customization("metadata.entityId", (actual, expected) -> true),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]", (actual, expected) -> true)));

    leadCreatedEventContainer.stop();
    leadCreatedEventContainerV2.stop();
    leadIdNameCreateCommandContainer.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-create-id-name-command-request.json"),
        idNameCreateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (right, expected) -> Integer.valueOf(right.toString()) > 0)));
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void shouldUpdateLead() throws Exception {
    // given

    MockMqListener leadUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.2", LEAD_EXCHANGE, "sales.lead.updated", leadUpdatedEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.10", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    MockMqListener idNameUpdateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.1",
            LEAD_EXCHANGE,
            "sales.idName.update",
            idNameUpdateCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-update-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-response.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("conversionDetails[*]", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadUpdatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-jms-request.json"),
        leadUpdatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))));

    leadCreatedEventContainer.stop();
    leadCreatedEventContainerV2.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-jms-request-v2.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.latestActivityCreatedAt", (actual, expected) -> true),
            new Customization("entity.conversionAssociations.id", (actual, expected) -> true),
            new Customization("oldEntity.conversionAssociations.id", (actual, expected) -> true),
            new Customization("entity.phoneNumbers[*]",(actual, expected)->true),
            new Customization("entity.companyPhones[*]",(actual, expected)->true),
            new Customization("oldEntity.phoneNumbers[*]",(actual, expected)->true),
            new Customization("oldEntity.companyPhones[*]",(actual, expected)->true),
            new Customization("entity.conversionAssociations[*]",(actual, expected)->true),
            new Customization("oldEntity.conversionAssociations[*]",(actual, expected)->true)));


    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-update-id-name-command-request.json"),
        idNameUpdateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (right, expected) -> Integer.valueOf(right.toString()) > 0)));

    leadIdNameUpdateCommandContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenMaskedLeadAndCompanyPhoneNumbers_withOneUpdatedPhone_shouldUpdateLead() throws Exception {
    // given

    MockMqListener leadUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.2", LEAD_EXCHANGE, "sales.lead.updated", leadUpdatedEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.10", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    MockMqListener idNameUpdateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.1",
            LEAD_EXCHANGE,
            "sales.idName.update",
            idNameUpdateCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[{\"name\": \"phoneNumbers\"}, {\"name\": \"companyPhones\"}]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-update-with-masked-and-new-phones.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-with-masked-phones-response.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            leadPhoneComparator("phoneNumbers[*]","****210", "****321"),
            leadCompanyPhoneComparator("companyPhones[*]","****333", "****444"),
            new Customization("conversionDetails[*]", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadUpdatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-with-masking-enabled.json"),
        leadUpdatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            leadPhoneComparator("phoneNumbers[*]","9876543210", "4324324321"),
            leadCompanyPhoneComparator("companyPhones[*]", "9876543333", "4324324444"),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadCreatedEventContainer.stop();
    leadCreatedEventContainerV2.stop();
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-jms-request-v2-1.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.conversionAssociations.id", (actual, expected) -> true),
            new Customization("oldEntity.conversionAssociations.id", (actual, expected) -> true),
            leadPhoneComparator("entity.phoneNumbers[*]","9876543210", "4324324321"),
            leadCompanyPhoneComparator("entity.companyPhones[*]", "9876543333", "4324324444"),
            new Customization("oldEntity.phoneNumbers[*]",(actual, expected)->true),
            new Customization("oldEntity.companyPhones[*]",(actual, expected)->true),
            new Customization("entity.conversionAssociations[*]",(actual, expected)->true),
            new Customization("oldEntity.conversionAssociations[*]",(actual, expected)->true)));


    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-update-id-name-command-request.json"),
        idNameUpdateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (right, expected) -> Integer.valueOf(right.toString()) > 0)));

    leadIdNameUpdateCommandContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenLeadOwnerWithUpdatePermission_tryToSetOwner_shouldUpdate() throws Exception {
    // given

    Action action = new Action();
    action.read(true).delete(true).readAll(true).update(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 1000, allowedPermissions);

    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.3", LEAD_EXCHANGE, "sales.lead.updated", leadCreatedEventListener);

    MockMqListener idNameUpdateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.2",
            LEAD_EXCHANGE,
            "sales.idName.update",
            idNameUpdateCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/UPDATE"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-with-owner-update-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-with-owner-update-response.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (right, expected) -> true),
            new Customization("latestActivityCreatedAt", (right, expected) -> true)
            ));
    leadCreatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-with-owner-91-update-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadCreatedEventContainer.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-update-id-name-command-request.json"),
        idNameUpdateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (right, expected) -> Integer.valueOf(right.toString()) > 0)));

    leadIdNameUpdateCommandContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenExistingSharedLead_tryToUpdateWithNewOwner_shouldThrow() throws Exception {
    //given
    Action action = new Action();
    action.read(true).delete(true).readAll(true).update(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(20, 1000, allowedPermissions);

    MockMqListener leadUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.4", LEAD_EXCHANGE, "sales.lead.updated", leadUpdatedEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            LEAD_UPDATED_QUEUE_NEW, LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    MockMqListener idNameUpdateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.3",
            LEAD_EXCHANGE,
            "sales.idName.update",
            idNameUpdateCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/UPDATE"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/store/Yj70tW4BEzU9LuAFK-nX/_update?retry_on_conflict=3&timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    //when
    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-with-owner-update-request.json")))
            .andReturn();

    JSONAssert.assertEquals("{\"code\":\"000005\",\"message\":\"Uhoh! Permission's on this record don't seem right. We have been notified and chances are our team is already on it. Please try refreshing the page.\",\"errorDetails\":[]}",
        result.getResponse().getContentAsString(),JSONCompareMode.STRICT);
    leadUpdatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        null,
        leadUpdatedEventListener.actualMessage,
        JSONCompareMode.STRICT
    );

    leadUpdatedEventContainer.stop();


    JSONAssert.assertEquals(
        null,
        idNameUpdateCommandListener.actualMessage,
        JSONCompareMode.STRICT
    );

    leadIdNameUpdateCommandContainer.stop();

    JSONAssert.assertEquals(
        null,
        leadUpdatedEventListenerV2.actualMessage,
        JSONCompareMode.STRICT
    );

    leadUpdatedEventContainerV2.stop();

    MvcResult response =
        mockMvc
            .perform(
                get("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/get-response-after-shared-lead-update.json"),
        response.getResponse().getContentAsString(),
        new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))));

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/responses/owner-change-error-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-migrations/lead/lead_with_all_field_and_association.sql")
  public void shouldDeleteLead() throws Exception {
    // given
    MockMqListener leadDeletedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadDeletedEventContainer =
        initializeRabbitMqListener(
            "q.sales.5", LEAD_EXCHANGE, "sales.lead.deleted", leadDeletedEventListener);

    MockMqListener leadDeletedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadDeletedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.delete.v2", LEAD_EXCHANGE, "sales.lead.deleted.v2", leadDeletedEventListenerV2);

    MockMqListener idNameDeleteCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameDeleteCommandContainer =
        initializeRabbitMqListener(
            LEAD_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER,
            LEAD_EXCHANGE,
            "sales.lead.idName.delete",
            idNameDeleteCommandListener);

    MockMqListener tenantUsagePublisher = new MockMqListener();
    SimpleMessageListenerContainer tenantUsagePublisherCommandExecutor =
        initializeRabbitMqListener(
            TENANT_USAGE_COMMAND_QUEUE,
            LEAD_EXCHANGE,
            "tenant.usage.collected",
            tenantUsagePublisher);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/1000-id-name/store/Yj70tW4BEzU9LuAFK-nX/_delete?retry_on_conflict=3&timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    MvcResult result =
        mockMvc
            .perform(
                delete("/v1/leads/91?publishUsage=true")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    Assertions.assertThat(result.getResponse().getContentAsString()).isEqualTo("");
    leadDeletedEventListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-deleted-event-request.json"),
        leadDeletedEventListener.actualMessage,
        JSONCompareMode.LENIENT
    );

    leadDeletedEventListenerV2.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-deleted-event-request-v2.json"),
        leadDeletedEventListenerV2.actualMessage,
        JSONCompareMode.LENIENT
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/tenant-usage-event-on-lead-delete.json"),
        tenantUsagePublisher.actualMessage,
        JSONCompareMode.LENIENT
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-delete-id-name-command-request.json"),
        idNameDeleteCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (right, expected) -> Integer.valueOf(right.toString()) > 0)));

    leadIdNameDeleteCommandContainer.stop();
    leadDeletedEventContainer.stop();
    leadDeletedEventContainerV2.stop();
    tenantUsagePublisherCommandExecutor.stop();
  }

  @Test
  @Sql("/test-migrations/lead/lead_update_with_products.sql")
  public void
  givenLeadToBeUpdated_withProducts_shouldRaiseUpdatedEventV2_withDifferentOldAndNewEntity()
      throws Exception {

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.new.1", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/store/Yj70tW4BEzU9LuAFK-nX/_update?retry_on_conflict=3&timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/products?id=2"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-update-request-with-active-products.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-update-request-with-products.json")))
            .andReturn();

    leadUpdatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-update-response-with-products.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-update-event-products-response.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))
        )
    );


    String expectedIdNameResolutionReq =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-update-command-payload-request.json");

    leadUpdatedEventContainerV2.stop();

  }

  @Test
  @Sql("/test-migrations/lead/lead_update_with_custom_fields.sql")
  public void
  givenLeadToBeUpdated_withCustomFields_shouldRaiseUpdatedEventV2_withDifferentOldAndNewEntity()
      throws Exception {

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.new.2", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    MockMqListener leadIdNameUpdatedCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdatedCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.5", LEAD_EXCHANGE, "sales.idName.update", leadIdNameUpdatedCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/custom-fields-response.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.post(urlMatching("/1000-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.post(urlMatching("/1000-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-update-request-with-custom-fields.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-response-with-custom-fields.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadUpdatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-event-custom-fields-response.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))
            ));

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-91-idName-update-command-payload-request.json"),
        leadIdNameUpdatedCommandListener.actualMessage,
        JSONCompareMode.LENIENT);

    leadUpdatedEventContainerV2.stop();
    leadIdNameUpdatedCommandContainer.stop();
  }
  @Test
  @Sql("/test-migrations/lead/lead_update_with_custom_fields.sql")
  public void givenLeadToBeUpdated_withCustomFields_tryingToUnsetCustomField_shouldUpdate()
      throws Exception {
    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/custom-fields-response.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-92-idName-index-response.json");

    stubFor(
        WireMock.post(urlMatching("/1000-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.post(urlMatching("/1000-id-name.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/92")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-update-request-with-empty-custom-fields.json")))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-response-with-unset-custom-field.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenLead_tryToReassign_shouldChangeOwnerAndPublishUpdatedEventV2() throws Exception {
    // given

    Action action = new Action();
    action.read(true).update(true).write(true);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 1000, allowedPermissions);

    MockMqListener leadCreatedEventListener = new MockMqListener();
    MockMqListener leadOwnerUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.6", LEAD_EXCHANGE, "sales.lead.updated", leadCreatedEventListener);

    SimpleMessageListenerContainer leadOwnerUpdateEventContainer =
        initializeRabbitMqListener(
            LEAD_OWNER_UPDATED_QUEUE,
            LEAD_EXCHANGE,
            "sales.lead.owner_updated",
            leadOwnerUpdateEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.updated.new.1", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/lead-fields-with-pipeline.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(urlPathMatching("/1000-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/es/get-lead-idName-response-multiple-response.json"))));

    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/91/owner")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content("{\"ownerId\":99}"))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-change-owner-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);

    leadOwnerUpdateEventListener.latch.await(3, TimeUnit.SECONDS);
    leadUpdatedEventListenerV2.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-update-owner-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-owner_update-event-jms-request.json"),
        leadOwnerUpdateEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("lead.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("lead.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("lead.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("lead.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-change-owner-event-v2-response.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("*.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("*.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("*.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))));

    leadCreatedEventContainer.stop();
    leadOwnerUpdateEventContainer.stop();
    leadUpdatedEventContainerV2.stop();
  }

  @Test
  @Sql("/test-migrations/lead-pipeline/create-test-pipeline.sql")
  public void givenLeadWithPipelineAndPipelineStage_shouldCreateLeadWithPipelineAndStage()
      throws Exception {
    // given
    Date expectedClosureDate = new Date();
    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.7", LEAD_EXCHANGE, "sales.lead.created", leadCreatedEventListener);

    MockMqListener leadCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.v2.2", LEAD_EXCHANGE, "sales.lead.created.v2", leadCreatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlMatching("/v1/entities/LEAD/fields.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/lead-fields-with-pipeline.json"))));

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(urlPathMatching("/1000-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/es/get-lead-idName-response-multiple-response.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    // when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/requests/create-lead-with-pipeline-and-stage.json")))
            .andReturn();
    // then
    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-created-response-with-pipeline-and-stage.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> true),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization(
                "actualClosureDate",
                (actualDate, expectedDate) -> {
                  try {
                    Date actualCosureDate =
                        new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS")
                            .parse(actualDate.toString());
                    return actualCosureDate.after(expectedClosureDate);
                  } catch (ParseException e) {
                    fail(e.getMessage());
                  }
                  return false;
                })));
    leadCreatedEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-create-jms-request-with-pipeline-and-stage.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization(
                "actualClosureDate",
                (actualDate, expectedDate) -> {
                  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
                  try {
                    Date actualCosureDate = sdf.parse(actualDate.toString());
                    return actualCosureDate.after(expectedClosureDate);
                  } catch (ParseException e) {
                    fail(e.getMessage());
                    return false;
                  }
                }),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))));

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-create-jms-request-with-pipeline-and-stage-v2.json"),
        leadCreatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]", (actual, expected) -> true),
            new Customization(
                "entity.actualClosureDate",
                (actualDate, expectedDate) -> {
                  SimpleDateFormat dateTimeFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                  Date actualCosureDate = null;
                  try {
                    actualCosureDate = dateTimeFormatter.parse(actualDate.toString());
                  } catch (ParseException e) {
                    fail(e.getMessage());
                  }
                  return actualCosureDate.after(expectedClosureDate);
                })));

    leadCreatedEventContainer.stop();
    leadCreatedEventContainerV2.stop();
    List<ServeEvent> idNameRequests =
        wireMockRule.getAllServeEvents().stream()
            .filter(serveEvent -> serveEvent.getRequest().getUrl().contains("1000-id-name"))
            .collect(Collectors.toList());

  }

  @Test
  @Sql("/test-migrations/lead-pipeline/create-lead-with-pipeline-tenant-1000.sql")
  public void givenLeadWithPipelineAndPipelineStage_shouldUpdateLeadWithPipelineAndStage()
      throws Exception {
    // given
    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.8", LEAD_EXCHANGE, "sales.lead.updated", leadCreatedEventListener);

    MockMqListener leadCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.v2.3", LEAD_EXCHANGE, "sales.lead.updated.v2", leadCreatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/lead-fields-with-pipeline.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));


    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/lead-fields-with-pipeline.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(urlPathMatching("/1000-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/es/lead-id-1-get-idName-response-multiple-response.json"))));
    // when
    MvcResult result =
        mockMvc
            .perform(
                put("/v1/leads/1")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/requests/update-lead-with-pipeline-and-stage.json")))
            .andReturn();
    // then


    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/update-created-response-with-pipeline-and-stage.json"),
        result.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual))));

    MvcResult pipelineResponse =
        mockMvc
            .perform(
                get("/v1/leads/1/pipeline")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-id-1-post-update-pipeline-response.json"),
        pipelineResponse.getResponse().getContentAsString(),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("stages[1].completedAt", (actual, expected) -> true),
            new Customization("stages[3].startedAt", (actual, expected) -> true),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual)))
    );

    leadCreatedEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-jms-request-with-pipeline-and-stage.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> actual == expected),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("taskDueOn", (actual, expected) -> actual == expected),
            new Customization("meetingScheduledOn", (actual, expected) -> actual == expected),
            new Customization("phoneNumbers[*]",(actual, expected)->isValidPhoneNumbers(actual))));

    JSONAssert.assertEquals(
        getResourceAsString(
            "classpath:contracts/lead/lead-update-jms-request-with-pipeline-and-stage-v2.json"),
        leadCreatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.phoneNumbers[*]",(actual, expected)->true)));

    leadCreatedEventContainer.stop();
    leadCreatedEventContainerV2.stop();
    List<ServeEvent> idNameRequests =
        wireMockRule.getAllServeEvents().stream()
            .filter(serveEvent -> serveEvent.getRequest().getUrl().contains("1000-id-name"))
            .collect(Collectors.toList());
  }

  @Test
  @Sql("/test-migrations/lead/create_lead_with_2_pipeline_and_phones.sql")
  public void givenLeadToUpdate_tryToPatchNewOwnerAndPipelineAndMultiValueCustomField_shouldUpdate() throws Exception {
    // given

    MockMqListener leadCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.9", LEAD_EXCHANGE, "sales.lead.updated", leadCreatedEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.new.4", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    MockMqListener idNameUpdateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadIdNameUpdateCommandContainer =
        initializeRabbitMqListener(
            "q.sales.idName.update.search.4",
            LEAD_EXCHANGE,
            "sales.idName.update",
            idNameUpdateCommandListener);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString(
                        "classpath:contracts/config/lead-custom-field-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString(
                        "classpath:contracts/config/lead-custom-field-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-91-idName-index-response.json");

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/products?id=99&id=55"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/product/product-response-for-id-55-and-99.json"))));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                patch("/v1/leads/bulk-patch/91")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-91-patch-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        "{\"id\":91}",
        result.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
    leadCreatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-patch-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.NON_EXTENSIBLE,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-91-patch-jms-request-v2.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.NON_EXTENSIBLE,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.actualClosureDate", (actual, expected) -> isValidUTCDateFormat(actual))
        ));

    leadCreatedEventContainer.stop();
    leadUpdatedEventContainerV2.stop();

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/lead-patch-id-name-command-request.json"),
        idNameUpdateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=LEAD].id", (actual, expected) -> Integer.valueOf(actual.toString()) > 0)));

    leadIdNameUpdateCommandContainer.stop();
  }

  @Test
  @Sql("/test-migrations/lead/lead-with-full-details.sql")
  public void givenLeadPatchRequest_shouldUpdateRequiredFields() throws Exception {
    // given

    MockMqListener leadUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainer =
        initializeRabbitMqListener(
            "q.sales.patch.updated.v1", LEAD_EXCHANGE, "sales.lead.updated", leadUpdatedEventListener);

    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.json.patch.updated.v2", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    String accessDto = getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));


    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=1000"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-patch-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString(
                        "classpath:contracts/config/lead-1991-custom-field-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/lead-1991-idName-index-response.json");

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/101"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/products?id=99&id=55"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/product/product-response-for-id-55-and-99.json"))));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));

    MvcResult result =
        mockMvc
            .perform(
                patch("/v1/leads/1991")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType("application/json-patch+json")
                    .content(
                        getResourceAsString(
                            "classpath:contracts/lead/lead-patch-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        "{\"id\":1991}",
        result.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);

    leadUpdatedEventListener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-1991-patch-jms-request.json"),
        leadUpdatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.NON_EXTENSIBLE,
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))
        )
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-1991-patch-jms-request-v2.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("oldEntity.updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.latestActivityCreatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]", (actual, expected) -> true),
            new Customization("oldEntity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("oldEntity.companyPhones[*]", (actual, expected) -> true),
            new Customization("entity.conversionAssociations[*]", (actual, expected) -> true),
            new Customization("oldEntity.conversionAssociations[*]", (actual, expected) -> true)
        ));

    leadUpdatedEventContainer.stop();
    leadUpdatedEventContainerV2.stop();
  }

  @Test
  @Sql("/test-migrations/lead/R__lead_sub_for_duplicate.sql")
  public void givenLeadId_shouldGetDuplicateLeads() throws Exception {
    //given
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\": {},\"accessByRecords\": {}}")));
    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/idName-index-response-for-duplicate-leads.json");
    stubFor(
        WireMock.post(
            urlEqualTo(
                "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    //when

    MvcResult result =
        mockMvc
            .perform(
                get("/v1/leads/501/duplicates")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    //then
    JSONAssert.assertEquals(getResourceAsString("classpath:contracts/lead/duplicate-lead-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  @Test
  @Sql("/test-migrations/lead/R__lead_sub_for_duplicate.sql")
  public void givenLeadId_shouldReturnHasDuplicate() throws Exception {
    //given
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\"accessByOwners\": {},\"accessByRecords\": {}}")));
    String idNameResponse =
        getResourceAsString("classpath:contracts/lead/idName-index-response-for-duplicate-leads.json");
    stubFor(
        WireMock.post(
            urlEqualTo(
                "/1000-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    //when

    MvcResult result =
        mockMvc
            .perform(
                get("/v1/leads/501/has-duplicates")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    //then
    JSONAssert.assertEquals(getResourceAsString("classpath:contracts/lead/check-duplicate-lead-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.STRICT);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(
      String queue,
      String exchange,
      String eventName,
      MockMqListener mockMockMqListener) {
    Queue listenerQueue = new Queue(queue);
    rabbitAdmin.declareQueue(listenerQueue);

    Binding binding =
        BindingBuilder.bind(listenerQueue).to(new TopicExchange(exchange)).with(eventName);
    rabbitAdmin.declareBinding(binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  private Customization leadPhoneComparator(String key, String val1, String val2){
    return new Customization(key, (actual, expected) -> {
      PhoneNumberResponse leadPhoneNumberResponse = null;
      try {
        leadPhoneNumberResponse = new ObjectMapper().readValue(actual.toString(), PhoneNumberResponse.class);
        if(leadPhoneNumberResponse.getType().equals(PhoneType.WORK)){
          return leadPhoneNumberResponse.getValue().equals(val1);
        }else if(leadPhoneNumberResponse.getType().equals(PhoneType.HOME)){
          return leadPhoneNumberResponse.getValue().equals(val2);
        }
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
      return true;
    });
  }

  private Customization leadCompanyPhoneComparator(String key, String va1, String val2){
    return new Customization(key, (actual, expected) -> {
      PhoneNumberResponse leadPhoneNumberResponse = null;
      try {
        leadPhoneNumberResponse = new ObjectMapper().readValue(actual.toString(), PhoneNumberResponse.class);
        if(leadPhoneNumberResponse.getType().equals(PhoneType.WORK)){
          return leadPhoneNumberResponse.getValue().equals(va1);
        }else if(leadPhoneNumberResponse.getType().equals(PhoneType.HOME)){
          return leadPhoneNumberResponse.getValue().equals(val2);
        }
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
      return true;
    });
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;
    List<String> actualMessages = new LinkedList<>();

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
      actualMessages.add(this.actualMessage);
    }
  }

  boolean isValidUTCDateFormat(Object dateString) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    try {
      return sdf.parse(dateString.toString()) != null;
    } catch (ParseException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
  }

  private boolean isValidPhoneNumbers(Object o1) {
    Map<String, Object> phoneNumbers = null;
    try {
      phoneNumbers = new ObjectMapper().readValue(o1.toString(), Map.class);
    } catch (IOException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
    return !MapUtils.isEmpty(phoneNumbers);
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(LEAD_EXCHANGE, "topic")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer =
        new PostgreSQLContainer("postgres:10.11")
            .withDatabaseName("product")
            .withUsername("test-user")
            .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
