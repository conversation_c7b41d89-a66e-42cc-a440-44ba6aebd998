package com.sell.sales.lead;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.badRequest;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.lead.LeadImport_IntegrationTest.TestDatabaseInitializer;
import com.sell.sales.lead.LeadImport_IntegrationTest.TestEnvironmentSetup;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
    })
@AutoConfigureMockMvc
public class LeadImport_IntegrationTest {

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9090);

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String LEAD_QUEUE = "q.sales";
  private static final String ID_NAME_CREATED_COMMAND_LISTENER_QUEUE = "q.sales.idName.create.search";
  private static final String LEAD_EXCHANGE = "ex.sales";


  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private Environment environment;

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
    ;
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql("/test-scripts/R__stub-pipeline.sql")
  public void givenLeadToImport_withUniquenessStrategy_shouldCreate() throws Exception {
    // given
    Date expectedActualClosureDate = new Date();
    MockMqListener leadUpdatedEventListener = new MockMqListener();
    MockMqListener leadUpdatedEventListenerV2 = new MockMqListener();

    SimpleMessageListenerContainer leadUpdatedEventContainer = initializeRabbitMqListener("q.sales.2", LEAD_EXCHANGE, "sales.lead.updated",
        leadUpdatedEventListener);

    SimpleMessageListenerContainer leadUpdatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.v2.2", LEAD_EXCHANGE, "sales.lead.updated.v2", leadUpdatedEventListenerV2);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"EMAIL\",\"primaryField\":\"EMAIL\"}")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/100-id-name/store?timeout=1m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.post(urlPathMatching("/100-id-name/_search.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.user%40sling.com"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );
    String searchResponseBody =
        getResourceAsString("classpath:contracts/product/responses/search-product-by-name-and-status-is-active.json");

    stubFor(
        WireMock.post(
                urlEqualTo("/v1/products/search?page=0&size=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(searchResponseBody)));
    String productId5Response =
        getResourceAsString("classpath:contracts/product/product-list-by-ids-5.json");

    stubFor(
        WireMock.get(
                urlEqualTo("/v1/products?id=5"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(productId5Response)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-import-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));
    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getResourceAsString("classpath:contracts/lead/import-request-lead-with-strategy.json"))
            )
            .andReturn();
    //then
    leadUpdatedEventListener.latch.await(3, TimeUnit.SECONDS);
    leadUpdatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);

    JSONAssert
        .assertEquals(getResourceAsString("classpath:contracts/lead/import-lead-updated-response.json"),
            result.getResponse().getContentAsString(),
            new CustomComparator(
                JSONCompareMode.STRICT,
                new Customization("leadId", (o1, expected) -> true)
            ));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/import-lead-update-response.json"),
        leadUpdatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("id", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> true),
            new Customization("updatedAt", (actual, expected) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> true),
            new Customization("companyPhones[*]", (o1, o2) -> true),
            new Customization("actualClosureDate", (actualDate, expectedDate) -> {
              try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
                Date actualClosureDate = sdf.parse(actualDate.toString());
                return actualClosureDate.after(expectedActualClosureDate);
              } catch (ParseException e) {
                Assertions.fail(e.getMessage());
                return false;
              }
            }),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))
        )
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-import-v2-updated-response.json"),
        leadUpdatedEventListenerV2.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.actualClosureDate", (o1, o2) -> true),
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]", (actual, expected) -> true),
            new Customization("oldEntity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("oldEntity.companyPhones[*]", (actual, expected) -> true),
            new Customization("entity.id", (o1, o2) -> true),

            new Customization("metadata.entityId", (o1, o2) -> true))
    );
    leadUpdatedEventContainer.stop();
    leadUpdatedEventContainerV2.stop();

  }

  @Test
  @Sql("/test-scripts/R__stub-pipeline.sql")
  public void givenLeadToImport_shouldCreate() throws Exception {
    // given
    Date expectedActualClosureDate = new Date();
    MockMqListener leadCreatedEventListener = new MockMqListener();
    MockMqListener idNameCreateCommandListener = new MockMqListener();
    MockMqListener leadCreatedEventListenerV2 = new MockMqListener();

    SimpleMessageListenerContainer leadCreatedEventContainer = initializeRabbitMqListener(LEAD_QUEUE, LEAD_EXCHANGE, "sales.lead.created",
        leadCreatedEventListener);

    SimpleMessageListenerContainer idNameCreatedCommandListenerContainer = initializeRabbitMqListener(ID_NAME_CREATED_COMMAND_LISTENER_QUEUE,
        LEAD_EXCHANGE, "sales.idName.create",
        idNameCreateCommandListener);

    SimpleMessageListenerContainer leadCreatedEventContainerV2 =
        initializeRabbitMqListener(
            "q.sales.v2.1", LEAD_EXCHANGE, "sales.lead.created.v2", leadCreatedEventListenerV2);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"NONE\",\"primaryField\":\"EMAIL\"}")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/100-id-name/store?timeout=1m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.post(urlPathMatching("/100-id-name/_search.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.user%40sling.com"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );
    String searchResponseBody =
        getResourceAsString("classpath:contracts/product/responses/search-product-by-name-and-status-is-active.json");

    stubFor(
        WireMock.post(
            urlEqualTo("/v1/products/search?page=0&size=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(searchResponseBody)));
    String productId5Response =
        getResourceAsString("classpath:contracts/product/product-list-by-ids-5.json");

    stubFor(
        WireMock.get(
            urlEqualTo("/v1/products?id=5"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(productId5Response)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-import-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getResourceAsString("classpath:contracts/lead/import-lead-request.json"))
            )
            .andReturn();
    //then
    leadCreatedEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert
        .assertEquals(getResourceAsString("classpath:contracts/lead/import-lead-CREATE-response.json"),
            result.getResponse().getContentAsString(),
            new CustomComparator(
                JSONCompareMode.STRICT,
                new Customization("leadId", (o1, expected) -> true)
            ));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/import-lead-CREATE-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("id", (actual, expected) -> true),
            new Customization("latestActivityCreatedAt", (actual, expected) -> true),
            new Customization("phoneNumbers[*]", (actual, expected) -> true),
            new Customization("companyPhones[*]", (actual, expected) -> true),
            new Customization("actualClosureDate", (actualDate, expectedDate) -> {
              try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
                Date actualClosureDate = sdf.parse(actualDate.toString());
                return actualClosureDate.after(expectedActualClosureDate);
              } catch (ParseException e) {
                Assertions.fail(e.getMessage());
                return false;
              }
            }),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))
        )
    );

    String actualMessage = idNameCreateCommandListener.actualMessage;

    JSONAssert.assertEquals(getResourceAsString("classpath:contracts/lead/lead-create-idName-command-payload-request.json"), actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("data[0].id", (actual, expected) -> true)
        ));

    leadCreatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/lead-import-v2-response.json"),
        leadCreatedEventListenerV2.actualMessage,
        new CustomComparator(JSONCompareMode.LENIENT,
            new Customization("entity.actualClosureDate", (o1, o2) -> true),
            new Customization("entity.id", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (actual, expected) -> true),
            new Customization("entity.companyPhones[*]", (actual, expected) -> true),
            new Customization("metadata.entityId", (o1, o2) -> true))
    );
    leadCreatedEventContainer.stop();
    idNameCreatedCommandListenerContainer.stop();
    leadCreatedEventContainerV2.stop();

  }

  @Test
  @Sql("/test-scripts/R__stub-pipeline.sql")
  public void givenLeadToImport_withAllErrors_shouldReturnAllErrors() throws Exception {
    // given
    Date expectedActualClosureDate = new Date();

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"NONE\",\"primaryField\":\"EMAIL\"}")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/100-id-name/store?timeout=1m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.post(urlPathMatching("/100-id-name/_search.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.userll%40sling.com"))
        .willReturn(badRequest()));

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );

    stubFor(
        WireMock.post(
                urlEqualTo("/v1/products/search?page=0&size=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{\n"
                        + "  \"content\": []\n"
                        + "}")));

    stubFor(
        WireMock.get(
                urlEqualTo("/v1/products?id=5"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-import-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));
    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getResourceAsString("classpath:contracts/lead/import-lead-with-error-request.json"))
            )
            .andReturn();
    //then

    JSONAssert
        .assertEquals(getResourceAsString("classpath:contracts/lead/import-lead-error-response.json"),
            result.getResponse().getContentAsString(),
            new CustomComparator(
                JSONCompareMode.STRICT
            ));

  }


  @Test
  @Sql("/test-scripts/R__stub-pipeline.sql")
  public void givenLeadToImportForNonTenantUserWithoutAdminPermissions_shouldCreate() throws Exception {
    // given

    Action action_CRUD = new Action();
    action_CRUD.read(true);
    action_CRUD.write(true);
    action_CRUD.update(true);
    action_CRUD.delete(true);

    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action_CRUD);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action_CRUD);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);

    Date expectedActualClosureDate = new Date();
    MockMqListener leadCreatedEventListener = new MockMqListener();
    MockMqListener idNameCreateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer leadCreatedEventContainer = initializeRabbitMqListener(LEAD_QUEUE, LEAD_EXCHANGE, "sales.lead.created",
        leadCreatedEventListener);

    SimpleMessageListenerContainer idNameCreatedCommandListenerContainer = initializeRabbitMqListener(ID_NAME_CREATED_COMMAND_LISTENER_QUEUE,
        LEAD_EXCHANGE, "sales.idName.create",
        idNameCreateCommandListener);

    String accessDto =
        getResourceAsString("classpath:contracts/lead/responses/access-dto.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/LEAD/UPDATE"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=true&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/LEAD"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"NONE\",\"primaryField\":\"EMAIL\"}")
        )
    );

    stubFor(WireMock.post(urlEqualTo("/100-id-name/store?timeout=1m"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.post(urlPathMatching("/100-id-name/_search.*"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{}")
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.user%40sling.com"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );
    String searchResponseBody =
        getResourceAsString("classpath:contracts/product/responses/search-product-by-name-and-status-is-active.json");

    stubFor(
        WireMock.post(
                urlEqualTo("/v1/products/search?page=0&size=1"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(searchResponseBody)));
    String productId5Response =
        getResourceAsString("classpath:contracts/product/product-list-by-ids-5.json");

    stubFor(
        WireMock.get(
            urlEqualTo("/v1/products?id=5"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(productId5Response)));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/fields?custom-only=false&tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/config/lead-import-fields-response.json"))
        )
    );

    stubFor(
        WireMock.get(urlMatching("/v1/tenants/config/currencies"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(
                        getResourceAsString(
                            "classpath:contracts/config/tenant-currencies.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/entities/LEAD/masked-fields?tenantId=100"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("[]")
        )
    );
    //when

    MvcResult result =
        mockMvc
            .perform(
                post("/v1/leads/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getResourceAsString("classpath:contracts/lead/import-lead-request.json"))
            )
            .andReturn();
    //then
    leadCreatedEventListener.latch.await(3, TimeUnit.SECONDS);

    JSONAssert
        .assertEquals(getResourceAsString("classpath:contracts/lead/import-lead-CREATE-response.json"),
            result.getResponse().getContentAsString(),
            JSONCompareMode.LENIENT);

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/lead/import-lead-CREATE-jms-request.json"),
        leadCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("updatedAt", (actual, expected) -> isValidUTCDateFormat(actual)),
            new Customization("latestActivityCreatedAt", (actual, expected) -> actual == expected),
            new Customization("phoneNumbers[*]", (o1, o2) -> true),
            new Customization("companyPhones[*]", (o1, o2) -> true),
            new Customization("actualClosureDate", (actualDate, expectedDate) -> {
              SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
              try {
                Date parse = sdf.parse(actualDate.toString());
                return parse.after(expectedActualClosureDate);
              } catch (ParseException e) {
                Assertions.fail(e.getMessage());
                return false;
              }
            }),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual)),
            new Customization("companyPhones[*]",(actual, expected) -> isValidPhoneNumbers(actual))
        )
    );

    String actualMessage = idNameCreateCommandListener.actualMessage;

    leadCreatedEventContainer.stop();
    idNameCreatedCommandListenerContainer.stop();

    JSONAssert.assertEquals(getResourceAsString("classpath:contracts/lead/lead-create-idName-command-payload-request.json"), actualMessage,
        JSONCompareMode.LENIENT);

  }

  boolean isValidUTCDateFormat(Object dateString) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    try {
      return sdf.parse(dateString.toString()) != null;
    } catch (ParseException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
  }

  private boolean isValidPhoneNumbers(Object o1) {
    Map<String, Object> phoneNumbers = null;
    try {
      phoneNumbers = new ObjectMapper().readValue(o1.toString(), Map.class);
    } catch (IOException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
    return !MapUtils.isEmpty(phoneNumbers);
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queue, String exchange, String eventName,
      LeadImport_IntegrationTest.MockMqListener mockMockMqListener) {
    Binding binding = BindingBuilder.bind(new Queue(queue))
        .to(new TopicExchange(exchange))
        .with(eventName);

    rabbitAdmin.declareBinding(
        binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.withExchange(LEAD_EXCHANGE, "topic").withQueue(LEAD_QUEUE)
          .withExchange(LEAD_EXCHANGE, "topic").withQueue(ID_NAME_CREATED_COMMAND_LISTENER_QUEUE)
          .withExchange(LEAD_EXCHANGE, "topic").withQueue("q.sales.v2.1")
          .withExchange(LEAD_EXCHANGE, "topic").withQueue("q.sales.v2.2")
          .withExchange(LEAD_EXCHANGE, "topic").withQueue("q.sales.2")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("product")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
