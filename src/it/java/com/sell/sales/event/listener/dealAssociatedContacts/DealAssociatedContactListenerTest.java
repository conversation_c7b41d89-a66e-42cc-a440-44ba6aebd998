package com.sell.sales.event.listener.dealAssociatedContacts;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.event.listener.dealAssociatedContacts.DealAssociatedContactListenerTest.TestDatabaseInitializer;
import com.sell.sales.event.listener.dealAssociatedContacts.DealAssociatedContactListenerTest.TestEnvironmentSetup;
import com.sell.sales.infra.mq.event.ContactUpdatedEvent;
import com.sell.sales.repository.ContactRepository;
import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.json.JSONException;
import org.junit.AfterClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.shaded.com.fasterxml.jackson.core.JsonEncoding;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:1289",
        "client.search.basePath=http://localhost:1289",
        "client.entity.basePath=http://localhost:1289",
        "client.company.basePath=http://localhost:1289",
        "client.iam.basePath=http://localhost:1289",
        "newElasticsearch.port=1289"
    })
@AutoConfigureMockMvc
@Sql("/test-scripts/deal_contact_associations.sql")
public class DealAssociatedContactListenerTest {

  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private RabbitTemplate rabbitTemplate;

  @Autowired private ContactRepository contactRepository;

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(1289).jettyHeaderBufferSize(36000));

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String SALES_EXCHANGE = "ex.sales";
  private static final String DEAL_EXCHANGE = "ex.deal";
  private static final String CONTACTS_QUEUE = "q.sales.contact";

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void givenDifferentContactWithAnExistingDeal_shouldAssociate() throws InterruptedException, IOException, JSONException {
    //given
    Long dealId = 21L;
    Long tenantId = 200L;
    Long userId = 100L;
    Long existingContact1 = 1001L;
    Long existingContact2 = 1002L;
    Long newContact = 1003L;

    MockMqListener contactUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer = initializeRabbitMqListener(CONTACTS_QUEUE, SALES_EXCHANGE, ContactUpdatedEvent.getEventName(),
        contactUpdateEventListener);

    CountDownLatch latch = new CountDownLatch(1);
    DealAssociatedContactEvent event = new DealAssociatedContactEvent(dealId, tenantId, userId, asList(existingContact1, newContact));


    String idNameResponse =
        getResourceAsString("classpath:contracts/contact/response/contact-1002-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/200-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/200-company/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=200"))
        .willReturn(
            aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(getResourceAsString("classpath:contracts/config/contact-fields.json"))));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=200"))
        .willReturn(
            aResponse()
                .withStatus(200)
                .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody(getResourceAsString("classpath:contracts/config/contact-fields.json"))));

    stubFor(
        WireMock.get(
                urlEqualTo("/v1/tenants/200/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));

    //when
    rabbitTemplate.convertAndSend(DEAL_EXCHANGE, DealAssociatedContactEvent.getEventName(), event);
    latch.await(3, TimeUnit.SECONDS);

    //then
    List<BigInteger> deals = contactRepository.findContactsAssociatedWithADeal(dealId, tenantId);
    assertThat(deals.size()).isEqualTo(2);
    assertThat(deals).containsExactlyInAnyOrder(BigInteger.valueOf(existingContact1), BigInteger.valueOf(newContact));

    contactUpdateEventListener.latch.await(4, TimeUnit.SECONDS);
    assertThat(contactUpdateEventListener.actualMessages.size()).isEqualTo(2);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/associated-contact-1002-update.json"),
        contactUpdateEventListener.actualMessages.get(0),
        JSONCompareMode.STRICT);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/associated-contact-1003-update.json"),
        contactUpdateEventListener.actualMessages.get(1),
        JSONCompareMode.STRICT);
    listenerContainer.stop();
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queue, String exchange, String eventName,
      MockMqListener mockMockMqListener) {
    Binding binding = BindingBuilder.bind(new Queue(queue))
        .to(new TopicExchange(exchange))
        .with(eventName);

    rabbitAdmin.declareBinding(
        binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, JsonEncoding.UTF8.getJavaName());
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    List<String> actualMessages = new ArrayList<>();

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessages.add(new String(messageInBytes));
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(SALES_EXCHANGE, "topic")
          .withExchange(DEAL_EXCHANGE, "topic")
          .withQueue(CONTACTS_QUEUE).start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("sales")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}