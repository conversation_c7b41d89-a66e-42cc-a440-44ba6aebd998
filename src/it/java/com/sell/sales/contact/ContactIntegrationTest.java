package com.sell.sales.contact;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.okForContentType;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertTrue;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.contact.ContactIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.contact.ContactIntegrationTest.TestEnvironmentSetup;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.domain.layout.MultiConversionAssociation;
import com.sell.sales.repository.MultiConversionAssociationRepository;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import javax.sql.DataSource;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.shaded.com.fasterxml.jackson.core.JsonEncoding;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.company.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
public class ContactIntegrationTest {

  String authToken;
  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9090);

  @Autowired
  EntityManager em;
  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ResourceLoader resourceLoader;
  @Autowired
  private MultiConversionAssociationRepository multiConversionAssociationRepository;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private static final String SALES_QUEUE = "q.sales";
  private static final String SALES_QUEUE_REASSIGNED = "q.sales.reassigned";
  private static final String SALES_QUEUE_UPDATED_REASSIGNED = "q.sales.updated.reassigned";
  private static final String SALES_QUEUE_REASSIGNED_OWNER_UPDATED = "q.sales.owner.reassigned";
  private static final String SALES_EXCHANGE = "ex.sales";
  private static final String CONTACT_CREATED_EVENT = "sales.contact.created";
  private static final String CONTACT_CREATED_EVENT_V2 = "sales.contact.created.v2";
  private static final String CONTACT_UPDATED_EVENT = "sales.contact.updated";
  private static final String CONTACT_UPDATED_EVENT_V2 = "sales.contact.updated.v2";
  private static final String CONTACT_REASSIGNED_EVENT = "sales.contact.owner_updated";

  private static final String CONTACT_DELETED_EVENT_V2 = "sales.contact.deleted.v2";
  private static final String AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-dyFL63oRYbkFmbJAtius7RrS3hWpqYe6cDSmxrpONs";
  private static final String ID_NAME_CREATE_COMMAND_QUEUE_LISTENER = "sales.idName.create.search";
  private static final String ID_NAME_UPDATE_COMMAND_QUEUE_LISTENER = "sales.idName.update.search";
  private static final String CONTACT_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER = "sales.contact.idName.delete.search";
  private static final String TENANT_USAGE_COMMAND_QUEUE = "q.tenant.usage.collected";
  private static final String SALES_QUEUE_NEW = "q.sales_new";
  private static final String SALES_QUEUE_1 = "q.sales_1";
  private static final String SALES_QUEUE_2 = "q.sales_2";
  private static final String SALES_QUEUE_3 = "q.sales_3";
  private static final String SALES_QUEUE_4 = "q.sales_4";
  private static final String CONTACT_UPDATED_QUEUE = "q.sales_41";
  private static final String CONTACT_UPDATED_EVENT_V2_QUEUE = "q.sales_51";
  private static final String CONTACT_OWNER_UPDATED_QUEUE = "q.sales_61";
  private static final String CONTACT_IMPORT_QUEUE = "q.sales_62";
  private static final String CONTACT_IMPORT_QUEUE_v2 = "q.sales_63";
  private static final String CONTACT_DELETED_EVENT_V2_QUEUE = "q.sales_deleted_v2";
  private static final String CONTACT_IMPORT_QUEUE_v1 = "q.sales_75";
  private static final String CONTACT_IMPORT_QUEUE2_v2 = "q.sales_76";


  @Before
  public void setUp() {
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true).readAll(true).updateAll(true).task(true).note(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);
  }


  @After
  public void tearDownTest() {
    wireMockRule.resetAll();
  }

  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactRequest_shouldCreateContact() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true).note(true).task(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);
    String contactCreateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-create-request.json");

    MockMqListener idNameCreateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer idNameCreateCommandContainer =
        initializeRabbitMqListener(
            ID_NAME_CREATE_COMMAND_QUEUE_LISTENER,
            SALES_EXCHANGE,
            "sales.idName.create",
            idNameCreateCommandListener);

    MockMqListener contactCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer = initializeRabbitMqListener(SALES_QUEUE, SALES_EXCHANGE, CONTACT_CREATED_EVENT,
        contactCreatedEventListener);

    MockMqListener contactCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer contactCreatedEventContainerV2 =
        initializeRabbitMqListener(SALES_QUEUE_1,
            SALES_EXCHANGE,
            CONTACT_CREATED_EVENT_V2,
            contactCreatedEventListenerV2);

    MockMqListener contactReassignedEventListener = new MockMqListener();
    SimpleMessageListenerContainer contactReassignedEventContainer =
        initializeRabbitMqListener(SALES_QUEUE_REASSIGNED,
            SALES_EXCHANGE,
            CONTACT_REASSIGNED_EVENT,
            contactReassignedEventListener);

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[{\"name\": \"phoneNumbers\"}]")));

    stubFor(get(urlEqualTo("/v1/companies/221"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok()
            .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String accessDto =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/22"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));



    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/contacts")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                    .content(contactCreateRequest))
            .andExpect(status().isOk())
            .andReturn();
    String responseContent = result.getResponse().getContentAsString();

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-create-response.json"),
        responseContent,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("id", (o1, o2) -> true),
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers[*].id", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
        ));

    contactCreatedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-created-event.json"),
        contactCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("id", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> true)
        ));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/contact-create-id-name-command-request.json"),
        idNameCreateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=CONTACT].id", (right, left) -> Integer.valueOf(right.toString()) > 0)));

    contactCreatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-create-jms-request-v2.json"),
        contactCreatedEventListenerV2.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (o1, o2) -> true),
            new Customization("entity.id", (o1, o2) -> true),
            new Customization("metadata.entityId", (o1, o2) -> true),
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*].id", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );

    contactReassignedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-reassigned-event.json"),
        contactReassignedEventListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("contact.createdAt", (o1, o2) -> true),
            new Customization("contact.id", (o1, o2) -> true),
            new Customization("entityId", (o1, o2) -> true),
            new Customization("contact.updatedAt", (o1, o2) -> true),
            new Customization("contact.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );
    listenerContainer.stop();
    contactCreatedEventContainerV2.stop();
    contactReassignedEventContainer.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactImportRequest_withUniquenessStrategy_shouldUpdateContact() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true).note(true).task(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);
    String contactCreateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-import-uniqueness-request.json");

    MockMqListener contactCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer =
        initializeRabbitMqListener(CONTACT_IMPORT_QUEUE_v1, SALES_EXCHANGE, CONTACT_UPDATED_EVENT,
            contactCreatedEventListener);

    MockMqListener contactCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer contactCreatedEventContainerV2 =
        initializeRabbitMqListener(CONTACT_IMPORT_QUEUE2_v2,
            SALES_EXCHANGE,
            CONTACT_UPDATED_EVENT_V2,
            contactCreatedEventListenerV2);

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.user%40sling.com"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );
    stubFor(get(urlEqualTo("/v1/companies/103"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok()
            .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String accessDto =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"field\":\"EMAIL\",\"primaryField\":\"EMAIL\"}")
        )
    );

    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/contacts/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(contactCreateRequest)
            )
            .andReturn();

    //then
    result.getResponse().getContentAsString();
    contactCreatedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-import-response.json"),
        contactCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
        ));

    contactCreatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-import-response-v2.json"),
        contactCreatedEventListenerV2.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (o1, o2) -> true),
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("oldEntity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );
    listenerContainer.stop();
    contactCreatedEventContainerV2.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactImportRequest_shouldCreateContact() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true).note(true).task(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);
    String contactCreateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-import-request.json");

    MockMqListener contactCreatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer =
        initializeRabbitMqListener(CONTACT_IMPORT_QUEUE, SALES_EXCHANGE, CONTACT_CREATED_EVENT,
            contactCreatedEventListener);

    MockMqListener contactCreatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer contactCreatedEventContainerV2 =
        initializeRabbitMqListener(CONTACT_IMPORT_QUEUE_v2,
            SALES_EXCHANGE,
            CONTACT_CREATED_EVENT_V2,
            contactCreatedEventListenerV2);

    stubFor(WireMock.get(urlEqualTo("/v1/users/email?emailId=test.user%40sling.com"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))
        )
    );
    stubFor(get(urlEqualTo("/v1/companies/103"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok()
            .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String accessDto =
        getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok()
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(accessDto)
        )
    );

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/users/4021"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:contracts/user/user-response-by-email-with-permission.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    //when
    MvcResult result =
        mockMvc
            .perform(
                post("/v1/contacts/import")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(contactCreateRequest)
            )
            .andReturn();

    //then
    result.getResponse().getContentAsString();
    contactCreatedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-created-import-response.json"),
        contactCreatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT
        ));

    contactCreatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-import-response-v2.json"),
        contactCreatedEventListenerV2.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.createdAt", (o1, o2) -> true),
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );
    listenerContainer.stop();
    contactCreatedEventContainerV2.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_shouldUpdate() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);

    MockMqListener idNameCreateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer idNameUpdateCommandContainer =
        initializeRabbitMqListener(
            ID_NAME_UPDATE_COMMAND_QUEUE_LISTENER,
            SALES_EXCHANGE,
            "sales.idName.update",
            idNameCreateCommandListener);

    MockMqListener contactUpdatedV2 = new MockMqListener();
    SimpleMessageListenerContainer contactUpdateCommandContainer =
        initializeRabbitMqListener(
            SALES_QUEUE_1,
            SALES_EXCHANGE,
            CONTACT_UPDATED_EVENT_V2,
            contactUpdatedV2);

    MockMqListener contactReassignedEventListener = new MockMqListener();
    SimpleMessageListenerContainer ContactReassignedEventContainer =
        initializeRabbitMqListener(SALES_QUEUE_UPDATED_REASSIGNED,
            SALES_EXCHANGE,
            CONTACT_REASSIGNED_EVENT,
            contactReassignedEventListener);

    MockMqListener contactUpdated = new MockMqListener();
    SimpleMessageListenerContainer contactUpdateCommandContainerV1 =
        initializeRabbitMqListener(
            SALES_QUEUE_4,
            SALES_EXCHANGE,
            CONTACT_UPDATED_EVENT,
            contactUpdated);

    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-request-with-custom-field.json");

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/custom-fields-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1008")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    String responseContent = result.getResponse().getContentAsString();

    //then
    MultiConversionAssociation contact = multiConversionAssociationRepository.findByIdAndTenantId(99L, 100L).get();
    assertTrue(contact.getEntityName().equals("Tony Stark"));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-response.json"),
        responseContent,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
        ));

    idNameCreateCommandListener.latch.await(1, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/contact-update-id-name-command-request.json"),
        idNameCreateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=CONTACT].id", (right, left) -> Integer.valueOf(right.toString()) > 0)));

    contactUpdated.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-event-with-owner.json"),
        contactUpdated.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers", (o1, o2) -> true)
        ));

    contactUpdatedV2.latch.await(5, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/contact-update-v2.json"),
        contactUpdatedV2.actualMessage, new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)),
            new Customization("oldEntity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );

    contactReassignedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-reassigned-event.json"),
        contactReassignedEventListener.actualMessage,
        new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
            new Customization("contact.createdAt", (o1, o2) -> true),
            new Customization("contact.updatedAt", (o1, o2) -> true),
            new Customization("contact.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
            )
    );

    idNameUpdateCommandContainer.stop();
    contactUpdateCommandContainer.stop();
    ContactReassignedEventContainer.stop();
    contactUpdateCommandContainerV1.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_withMaskedData_shouldUpdate() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);

    MockMqListener idNameCreateCommandListener = new MockMqListener();
    SimpleMessageListenerContainer idNameUpdateCommandContainer =
        initializeRabbitMqListener(
            ID_NAME_UPDATE_COMMAND_QUEUE_LISTENER,
            SALES_EXCHANGE,
            "sales.idName.update",
            idNameCreateCommandListener);

    MockMqListener contactUpdatedV2 = new MockMqListener();
    SimpleMessageListenerContainer contactUpdateCommandContainer =
        initializeRabbitMqListener(
            SALES_QUEUE_1,
            SALES_EXCHANGE,
            CONTACT_UPDATED_EVENT_V2,
            contactUpdatedV2);

    MockMqListener contactReassignedEventListener = new MockMqListener();
    SimpleMessageListenerContainer ContactReassignedEventContainer =
        initializeRabbitMqListener(SALES_QUEUE_UPDATED_REASSIGNED,
            SALES_EXCHANGE,
            CONTACT_REASSIGNED_EVENT,
            contactReassignedEventListener);

    MockMqListener contactUpdated = new MockMqListener();
    SimpleMessageListenerContainer contactUpdateCommandContainerV1 =
        initializeRabbitMqListener(
            SALES_QUEUE_4,
            SALES_EXCHANGE,
            CONTACT_UPDATED_EVENT,
            contactUpdated);

    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-with-masked-fields-request.json");

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[{\"name\": \"phoneNumbers\"}]")));
    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/10222")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    String responseContent = result.getResponse().getContentAsString();

    //then

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-with-masked-field-response.json"),
        responseContent,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
        ));

    idNameCreateCommandListener.latch.await(1, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        "{\"tenantId\":100,\"userId\":10,\"data\":[{\"id\":10222,\"tenantId\":100,\"name\":\"Tony Stark\",\"forType\":\"CONTACT\"}]}",
        idNameCreateCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=CONTACT].id", (right, left) -> Integer.valueOf(right.toString()) > 0)));

    contactUpdated.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-event-with-unmasked-data.json"),
        contactUpdated.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers", (o1, o2) -> true)
        ));

    contactUpdatedV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/contact-update-v2-with-unmasked-data.json"),
        contactUpdatedV2.actualMessage, new CustomComparator(JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (o1, o2) -> true),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)),
            new Customization("oldEntity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );

    contactReassignedEventListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-reassign-event-with-unmasked-data.json"),
        contactReassignedEventListener.actualMessage,
        new CustomComparator(JSONCompareMode.NON_EXTENSIBLE,
            new Customization("contact.createdAt", (o1, o2) -> true),
            new Customization("contact.updatedAt", (o1, o2) -> true),
            new Customization("contact.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );

    idNameUpdateCommandContainer.stop();
    contactUpdateCommandContainer.stop();
    ContactReassignedEventContainer.stop();
    contactUpdateCommandContainerV1.stop();
  }
  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_withCustomFields_tryingToUnsetShouldUpdate() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);

    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-request-with-empty-custom-field.json");

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/custom-fields-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(ok("{}").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));
    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1008")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    String responseContent = result.getResponse().getContentAsString();

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-update-response-with-unset-custom-field-values.json"),
        responseContent,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true)
        ));
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenCompanyId_tryToGetContact_shouldReturnContact() throws Exception {
    //given
    long companyId = 101;

    stubFor(
        WireMock.post(
                urlPathMatching(
                    "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
                urlPathMatching(
                    "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody("{\"accessByOwners\":{},\"accessByRecords\":{}}")
        )
    );

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    //when
    MvcResult result =
        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/v1/contacts?page=0&size=3&sort=updatedAt,desc&companyId=" + companyId)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken))
            .andExpect(status().isOk())
            .andReturn();
    String responseContent = result.getResponse().getContentAsString();

    //then
    JSONAssert.assertEquals(getResourceAsString("classpath:contracts/contact/response/contact-by-companyId-response.json"), responseContent,
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_tryToRemoveCompany_shouldUpdateContact() throws Exception {
    //given
    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-request.json");

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));


    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    MockMqListener contactUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer = initializeRabbitMqListener(SALES_QUEUE, SALES_EXCHANGE, CONTACT_UPDATED_EVENT,
        contactUpdatedEventListener);

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[{\"name\": \"phoneNumbers\"}]")));

    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1005")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    contactUpdatedEventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-update-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-event.json"),
        contactUpdatedEventListener.actualMessage,
        JSONCompareMode.LENIENT);
    listenerContainer.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_tryToAssociateCompany_shouldUpdateContact() throws Exception {
    //given
    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-with-company-request.json");

    stubFor(get(urlEqualTo("/v1/companies/103"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/company-id-name-response.json"))));

    stubFor(
        WireMock.post(
            urlMatching(
                "/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1006")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-update-with-company-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT
    );
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenContactUpdateRequest_withAssociatedDeals_shouldUpdateContact() throws Exception {
    //given
    String contactUpdateRequest =
        getResourceAsString("classpath:contracts/contact/request/contact-update-with-company-request.json");

    stubFor(get(urlEqualTo("/v1/companies/103"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    MockMqListener contactUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer = initializeRabbitMqListener(SALES_QUEUE, SALES_EXCHANGE, CONTACT_UPDATED_EVENT,
        contactUpdatedEventListener);

    stubFor(
        WireMock.post(urlMatching("/100-company/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/es/id-name-empty-response.json"))));
    stubFor(
        WireMock.post(urlMatching("/100-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/es/id-name-empty-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1007")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(contactUpdateRequest))
        .andExpect(status().isOk())
        .andReturn();

    contactUpdatedEventListener.latch.await(3, TimeUnit.SECONDS);

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-update-with-company-response-1007.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT
    );

    System.out.println(contactUpdatedEventListener.actualMessage);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-updated-event-with-associated-deals.json"),
        contactUpdatedEventListener.actualMessage,
        JSONCompareMode.LENIENT);
    listenerContainer.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenLeadConversionLayoutRequest_shouldFetchLayout() throws Exception {
    //given
    String layoutResponse = getResourceAsString("classpath:contracts/contact/response/create-contact-layout.json");
    stubFor(WireMock.get(urlEqualTo("/v1/ui/layouts/CREATE/CONTACT"))
        .willReturn(okForContentType(MediaType.APPLICATION_JSON_VALUE, layoutResponse)));
    //when
    MvcResult mvcResult =
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/contacts/layout?view=lead-conversion&mode=new").contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(status().isOk())
            .andReturn();
    // then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/new-contact-layout-for-lead-conversion.json"),
        mvcResult.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT);
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void givenReassignmentRequest_shouldReassignAndPublishContactUpdateEvent() throws Exception {
    //given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);
    String accessDto = getResourceAsString("classpath:contracts/contact/response/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));
    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/contact-custom-fields-response.json");

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/contact/response/contact-1005-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    MockMqListener contactUpdatedEventListener = new MockMqListener();
    SimpleMessageListenerContainer listenerContainer = initializeRabbitMqListener(SALES_QUEUE, SALES_EXCHANGE, CONTACT_UPDATED_EVENT,
        contactUpdatedEventListener);

    MockMqListener contactUpdatedEventListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer listenerContainerV2 = initializeRabbitMqListener(SALES_QUEUE_2, SALES_EXCHANGE, "sales.contact.updated.v2",
        contactUpdatedEventListenerV2);


    MockMqListener contactReassignedEventListener = new MockMqListener();
    SimpleMessageListenerContainer contactReassignedEventContainer =
        initializeRabbitMqListener(SALES_QUEUE_REASSIGNED_OWNER_UPDATED,
            SALES_EXCHANGE,
            CONTACT_REASSIGNED_EVENT,
            contactReassignedEventListener);


    stubFor(WireMock.get(urlEqualTo("/v1/users/99"))
        .willReturn(aResponse()
            .withStatus(200)
            .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .withBody(getResourceAsString("classpath:user/user-99-response.json"))
        )
    );

    //when
    MvcResult result = mockMvc.perform(
            put("/v1/contacts/1005/owner")
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authTokenWithUpdate)
                .content("{\"ownerId\":99}"))
        .andExpect(status().isOk())
        .andReturn();

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-1005-reassignment-response.json"),
        result.getResponse().getContentAsString(),
        JSONCompareMode.LENIENT
    );

    contactUpdatedEventListener.latch.await(5, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-1005-reassignment-contactUpdate-event.json"),
        contactUpdatedEventListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true)
        ));

    contactUpdatedEventListenerV2.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-1005-reassignment-contactUpdate-event-v2.json"),
        contactUpdatedEventListenerV2.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("*.createdAt", (o1, o2) -> true),
            new Customization("*.updatedAt", (o1, o2) -> true)
        ));

    contactReassignedEventListener.latch.await(3,TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-owner-reassign-event.json"),
        contactReassignedEventListener.actualMessage,
        new CustomComparator(JSONCompareMode.STRICT,
            new Customization("contact.createdAt", (o1, o2) -> true),
            new Customization("contact.updatedAt", (o1, o2) -> true))
    );

    listenerContainer.stop();
    listenerContainerV2.stop();
    contactReassignedEventContainer.stop();
  }

  @Test
  @Sql("/test-migrations/contact/contact_with_full_details.sql")
  public void givenRequest_toFetchFullContactDetailsByIds_shouldFetchAllDetails() throws Exception {
    //given

    String accessResponse = getResourceAsString("classpath:contracts/contact/response/access-contact.json");

    stubFor(get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
        .willReturn(ok(accessResponse).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
        .willReturn(ok("[]").withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok().withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    //when
    MvcResult mvcResult =
        mockMvc
            .perform(
                MockMvcRequestBuilders.get("/v1/contacts?id=101&id=109&view=full").contentType(MediaType.APPLICATION_JSON_VALUE)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + AUTH_TOKEN))
            .andExpect(status().isOk())
            .andReturn();

    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-with-full-details-response.json"),
        mvcResult.getResponse().getContentAsString(),
        new CustomComparator(JSONCompareMode.STRICT_ORDER,
            new Customization("[*].phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1)))
    );
  }

  @Test
  @Sql("/test-migrations/contact/contact_with_full_details_for_delete.sql")
  public void shouldDeleteContact() throws Exception {
    // given
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true).delete(true);
    PermissionDTO contactReadPermission = new PermissionDTO();
    contactReadPermission.setName("contact");
    contactReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(100, 100, allowedPermissions);
    MockMqListener contactDeletedEventListener = new MockMqListener();
    SimpleMessageListenerContainer contactDeletedEventContainer =
        initializeRabbitMqListener(
            SALES_QUEUE, SALES_EXCHANGE, "sales.contact.deleted", contactDeletedEventListener);

    MockMqListener contactDeletedEventV2Listener = new MockMqListener();
    SimpleMessageListenerContainer contactDeletedEventV2Container =
        initializeRabbitMqListener(
            CONTACT_DELETED_EVENT_V2_QUEUE, SALES_EXCHANGE, "sales.contact.deleted.v2", contactDeletedEventV2Listener);

    MockMqListener idNameDeleteCommandListener = new MockMqListener();
    SimpleMessageListenerContainer contactIdNameDeleteCommandContainer =
        initializeRabbitMqListener(
            CONTACT_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER,
            SALES_EXCHANGE,
            "sales.contact.idName.delete",
            idNameDeleteCommandListener);

    MockMqListener tenantUsagePublisher = new MockMqListener();
    SimpleMessageListenerContainer tenantUsagePublisherCommandContainer =
        initializeRabbitMqListener(
            TENANT_USAGE_COMMAND_QUEUE,
            SALES_EXCHANGE,
            "tenant.usage.collected",
            tenantUsagePublisher);

    String accessDto = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/contact/response/contact-idName-index-response.json");

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/1000-id-name/store/Yj70tW4BEzU9LuAFK-nX/_delete?retry_on_conflict=3&timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    MvcResult result =
        mockMvc
            .perform(
                delete("/v1/contacts/101?publishUsage=true")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();

    assertThat(result.getResponse().getContentAsString()).isEqualTo("");
    contactDeletedEventListener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-deleted-event-request.json"),
        contactDeletedEventListener.actualMessage,
        JSONCompareMode.LENIENT
    );

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/es/contact-delete-id-name-command-request.json"),
        idNameDeleteCommandListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.LENIENT,
            new Customization("data[forType=CONTACT].id", (right, left) -> Integer.parseInt(right.toString()) > 0)));

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/tenant-usage-record-on-contact-delete.json"),
        tenantUsagePublisher.actualMessage,
        JSONCompareMode.LENIENT
    );

    contactDeletedEventV2Listener.latch.await(2, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-101-contactDelete-event-publish-V2.json"),
        contactDeletedEventV2Listener.actualMessage,
        JSONCompareMode.LENIENT
    );

    contactIdNameDeleteCommandContainer.stop();
    contactDeletedEventContainer.stop();
    contactIdNameDeleteCommandContainer.stop();
    contactDeletedEventV2Container.stop();
  }

  @Test
  @Sql("/test-migrations/contact/stub_contact.sql")
  public void shouldGetContact() throws Exception {
    // given
    Action action = new Action();
    action.read(true).write(true).delete(true).update(true).readAll(true).note(true).task(true);
    PermissionDTO contactPermission = new PermissionDTO();
    contactPermission.setId(18L);
    contactPermission.setName("contact");
    contactPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactPermission).collect(Collectors.toSet());
    String authTokenWithUpdate = TestEntity.getJwt(10, 100, allowedPermissions);

    String accessDto = getResourceAsString("classpath:contracts/contact/response/access-contact.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/contact/response/contact-idName-index-response.json");

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[]")));

    stubFor(
        WireMock.delete(
                urlEqualTo(
                    "/1000-id-name/store/Yj70tW4BEzU9LuAFK-nX/_delete?retry_on_conflict=3&timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));
    //when
    MvcResult result =
        mockMvc.
            perform(
                MockMvcRequestBuilders
                    .get("/v1/contacts/1008")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(HttpHeaders.AUTHORIZATION, " Bearer " + authTokenWithUpdate))
            .andReturn();
    String response = result.getResponse().getContentAsString();
    //then

    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/contact/response/contact-get-response.json"),
        response,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))
        ));
  }

  @Test
  @Sql("/test-migrations/contact/stub-contact-for-patch-request.sql")
  public void givenContactPatchRequest_shouldUpdate() throws Exception {
    // given
    Action action = new Action();
    action.read(true).delete(true).readAll(true).update(true).updateAll(true);
    PermissionDTO contactReadPermission = new PermissionDTO();
    contactReadPermission.setName("contact");
    contactReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(contactReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(10, 100, allowedPermissions);

    ContactIntegrationTest.MockMqListener contactUpdatedEventListener = new ContactIntegrationTest.MockMqListener();
    SimpleMessageListenerContainer contactUpdatedEventContainer =
        initializeRabbitMqListener(
            CONTACT_UPDATED_QUEUE, SALES_EXCHANGE, "sales.contact.updated", contactUpdatedEventListener);

    ContactIntegrationTest.MockMqListener contactUpdatedV2Listener = new ContactIntegrationTest.MockMqListener();
    SimpleMessageListenerContainer contactUpdatedV2ListenerContainer =
        initializeRabbitMqListener(
            CONTACT_UPDATED_EVENT_V2_QUEUE,
            SALES_EXCHANGE,
            "sales.contact.updated.v2",
            contactUpdatedV2Listener);

    ContactIntegrationTest.MockMqListener contactOwnerUpdateEventListener = new MockMqListener();
    SimpleMessageListenerContainer contactOwnerUpdateEventContainer = initializeRabbitMqListener(CONTACT_OWNER_UPDATED_QUEUE, SALES_EXCHANGE,
        "sales.contact.owner_updated",
        contactOwnerUpdateEventListener);

    String accessDto = getResourceAsString("classpath:contracts/contact/response/access-dto.json");
    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/READ"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/internal/share/access/CONTACT/UPDATE"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(accessDto)));
    String customFieldsResponse =
        getResourceAsString("classpath:contracts/lead/responses/contact-custom-fields-response.json");

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=100"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(customFieldsResponse)));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/99"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)));

    String idNameResponse =
        getResourceAsString("classpath:contracts/contact/response/contact-1001-idName-index-response.json");

    stubFor(
        WireMock.post(
                urlEqualTo(
                    "/100-id-name/_search?typed_keys=true&ignore_unavailable=false&expand_wildcards=open&allow_no_indices=true&search_type=query_then_fetch&batched_reduce_size=512"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(idNameResponse)));

    stubFor(get(urlEqualTo("/v1/companies/103"))
        .willReturn(
            ok(getResourceAsString("classpath:contracts/contact/response/company-response.json"))
                .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    MvcResult result =
        mockMvc
            .perform(
                patch("/v1/contacts/1001")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(
                        getResourceAsString("classpath:contracts/contact/request/contact-1001-patch-request.json")))
            .andReturn();

    JSONAssert.assertEquals(
        "{\"id\":1001}",
        result.getResponse().getContentAsString(), JSONCompareMode.LENIENT);
    contactUpdatedEventListener.latch.await(2, TimeUnit.SECONDS);
    contactUpdatedV2Listener.latch.await(2, TimeUnit.SECONDS);

    JSONAssert.assertEquals(
        contactUpdatedEventListener.actualMessage,
        getResourceAsString("classpath:contracts/contact/request/contact-1001-patch-jms-request.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("updatedAt", (right, left) -> true),
            new Customization("version", (right, left) -> true),
            new Customization("phoneNumbers[*]",(actual, expected)-> isValidPhoneNumbers(actual))));

    contactUpdatedEventContainer.stop();

    JSONAssert.assertEquals(
        contactUpdatedV2Listener.actualMessage,
        getResourceAsString("classpath:contracts/es/contact-update-v2-payload-for-patch-request.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("entity.updatedAt", (right, left) -> true),
            new Customization("oldEntity.updatedAt", (right, left) -> true),
            new Customization("entity.phoneNumbers[*]", (o1, o2) -> isValidPhoneNumbers(o1))));

    contactUpdatedV2ListenerContainer.stop();

    JSONAssert.assertEquals(
        contactOwnerUpdateEventListener.actualMessage,
        getResourceAsString("classpath:contracts/contact/response/contact-owner_update-event-jms-request.json"),
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("contact.createdAt", (o1, o2) -> true),
            new Customization("contact.updatedAt", (o1, o2) -> true),
            new Customization("contact.phoneNumbers[*]",(o1, o2)-> isValidPhoneNumbers(o1))
        )
    );

    contactOwnerUpdateEventContainer.stop();

  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, JsonEncoding.UTF8.getJavaName());
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(String queue, String exchange, String eventName,
      ContactIntegrationTest.MockMqListener mockMockMqListener) {
    Binding binding = BindingBuilder.bind(new Queue(queue))
        .to(new TopicExchange(exchange))
        .with(eventName);

    rabbitAdmin.declareBinding(
        binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;
    LinkedList<String> actualMessages = new LinkedList<>();

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
      this.actualMessages.add(this.actualMessage);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer.withExchange(SALES_EXCHANGE, "topic")
          .withQueue(SALES_QUEUE_REASSIGNED)
          .withQueue(SALES_QUEUE_UPDATED_REASSIGNED)
          .withQueue(SALES_QUEUE_REASSIGNED_OWNER_UPDATED)
          .withQueue(SALES_QUEUE)
          .withQueue(ID_NAME_UPDATE_COMMAND_QUEUE_LISTENER)
          .withQueue(ID_NAME_CREATE_COMMAND_QUEUE_LISTENER)
          .withQueue(CONTACT_ID_NAME_STORE_DELETE_COMMAND_QUEUE_LISTENER)
          .withQueue(TENANT_USAGE_COMMAND_QUEUE)
          .withQueue(SALES_QUEUE_NEW)
          .withQueue(SALES_QUEUE_1)
          .withQueue(SALES_QUEUE_2)
          .withQueue(SALES_QUEUE_3)
          .withQueue(SALES_QUEUE_4)
          .withQueue(CONTACT_UPDATED_EVENT_V2_QUEUE)
          .withQueue(CONTACT_UPDATED_QUEUE)
          .withQueue(CONTACT_OWNER_UPDATED_QUEUE)
          .withQueue(CONTACT_DELETED_EVENT_V2_QUEUE)
          .withQueue(CONTACT_IMPORT_QUEUE)
          .withQueue(CONTACT_IMPORT_QUEUE_v2)
          .withQueue(CONTACT_IMPORT_QUEUE_v1)
          .withQueue(CONTACT_IMPORT_QUEUE2_v2)
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("product")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }

  private boolean isValidPhoneNumbers(Object o1) {
    Map<String, Object> phoneNumbers = null;
    try {
      phoneNumbers = new ObjectMapper().readValue(o1.toString(), Map.class);
    } catch (IOException e) {
      Assertions.fail(e.getMessage());
      return false;
    }
    return !MapUtils.isEmpty(phoneNumbers);
  }
}