package com.sell.sales.infra.mq;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.sell.sales.core.domain.EntityType.CONTACT;
import static com.sell.sales.infra.mq.event.Metadata.Action.UPDATED;
import static java.util.Collections.emptySet;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.times;
import static org.skyscreamer.jsonassert.JSONAssert.assertEquals;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.event.ExecutionLogStatusEvent;
import com.sell.sales.domain.Contact;
import com.sell.sales.domain.ContactUtm;
import com.sell.sales.infra.mq.ContactUpdatedListenerV2IntegrationTest.TestDatabaseInitializer;
import com.sell.sales.infra.mq.ContactUpdatedListenerV2IntegrationTest.TestEnvironmentSetup;
import com.sell.sales.infra.mq.event.ContactUpdateEventPayload;
import com.sell.sales.infra.mq.event.EditPropertyActionType;
import com.sell.sales.infra.mq.event.Metadata;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.assertj.core.api.Assertions;
import org.json.JSONException;
import org.junit.AfterClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.BDDMockito;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestEnvironmentSetup.class, TestDatabaseInitializer.class})
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
        "newElasticsearch.host=localhost",
        "newElasticsearch.port=9090"
    })
@AutoConfigureMockMvc
@Sql("/test-migrations/contact/contact_update_v2.sql")
public class ContactUpdatedListenerV2IntegrationTest {
  private static final String EX_WORKFLOW = "ex.workflow";
  private static final String EVENT = "workflow.contact.update";

  private static final String EX_SALES = "ex.sales";

  @Rule
  public WireMockRule wireMockRule = new WireMockRule(WireMockConfiguration.wireMockConfig().port(9090).jettyHeaderBufferSize(15000));


  @Autowired
  private ConnectionFactory connectionFactory;
  @Autowired
  private AmqpAdmin rabbitAdmin;
  @Autowired
  private RabbitTemplate rabbitTemplate;
  @Autowired
  private ResourceLoader resourceLoader;
  @MockBean
  private WorkflowExecutionLogStatusEventPublisher workflowExecutionLogStatusEventPublisher;

  private SimpleMessageListenerContainer container;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");
  @AfterClass
  public static void tearDown() {
    rabbitMQContainer.stop();
  }

  @Test
  public void givenUpdateContactCommand_byWorkflow_shouldUpdateIt() throws InterruptedException, JSONException, IOException {
    //given
    stubFor(
        WireMock.get(urlEqualTo("/v1/entities/CONTACT/masked-fields?tenantId=1000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("[{\"name\": \"phoneNumbers\"}]")));

    stubFor(get(urlEqualTo("/v1/configurations/uniqueness/CONTACT"))
        .willReturn(ok()
            .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));


    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=false&tenantId=1000"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(get(urlEqualTo("/v1/entities/CONTACT/fields?custom-only=true&tenantId=1000"))
        .willReturn(ok(getResourceAsString("classpath:contracts/config/contact-fields.json")).withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));

    stubFor(
        WireMock.post(
                urlMatching(
                    "/1000-id-name/_search.*"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:contracts/contact/response/id-name-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/users/2000"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-99-response.json"))));

    stubFor(
        WireMock.get(urlEqualTo("/v1/tenants/1000/creator"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody(getResourceAsString("classpath:user/user-response-with-permission.json"))));


    MockMqListener contactUpdatedListenerV2 = new MockMqListener();
    SimpleMessageListenerContainer contactUpdatedEventContainerV2 =
        initializeRabbitMqListener("q.sales.contact.updated.v2.1",
            EX_SALES,
            "sales.contact.updated.v2",
            contactUpdatedListenerV2);

    MockMqListener contactUpdatedListener = new MockMqListener();
    SimpleMessageListenerContainer contactUpdatedEventContainer =
        initializeRabbitMqListener("q.sales.contact.updated.1",
            EX_SALES,
            "sales.contact.updated",
            contactUpdatedListener);

    Metadata metadata = new Metadata(1000L, 2000L, CONTACT, 4001L, UPDATED, "W1", emptySet(), null).withEventId(100L);
    Contact contactToUpdate = getContact(4001L, "Hari", null);
    Map<String,Object> customCopyFields=new HashMap<>();
    customCopyFields.put("cfMyMultiPicklist", Arrays.asList(1001,1002));
    Contact contact = new Contact();
    contact.setCustomFieldValues(customCopyFields);

    Map<String, Object> customFieldToUpdate = new HashMap<>();
    customFieldToUpdate.put("myName","my name updated");
    contactToUpdate.setCustomFieldValues(customFieldToUpdate);
    contactToUpdate.setScore(100.522);
    Map<EditPropertyActionType,Contact> map=new HashMap<>();
    map.put(EditPropertyActionType.REPLACE,contactToUpdate);
    map.put(EditPropertyActionType.APPEND,contact);
    ContactUpdateEventPayload event = new ContactUpdateEventPayload(map, null, metadata);

    //when
    rabbitTemplate.convertAndSend(EX_WORKFLOW, EVENT, event,message -> {
      message.getMessageProperties().setHeader("replyToExchange","ex.workflow");
      message.getMessageProperties().setHeader("replyToEvent","workflow.contact.update.reply");
      return message;
    });

    //then
    contactUpdatedListener.latch.await(3, TimeUnit.SECONDS);
    JSONAssert.assertEquals(
        getResourceAsString("classpath:contracts/mq/contact-v2/contact-update-event-by-workflow.json"),
        contactUpdatedListener.actualMessage,
        new CustomComparator(
            JSONCompareMode.STRICT,
            new Customization("createdAt", (o1, o2) -> true),
            new Customization("updatedAt", (o1, o2) -> true),
            new Customization("phoneNumbers", (o1, o2) -> true)
        ));

    contactUpdatedListenerV2.latch.await(3, TimeUnit.SECONDS);
    String expectedJsonV2 = getResourceAsString("classpath:contracts/mq/contact-v2/contact-update-v2-event-by-workflow.json");
    assertEquals(expectedJsonV2, contactUpdatedListenerV2.actualMessage, new CustomComparator(
        JSONCompareMode.STRICT,
        new Customization("entity.updatedAt", (o1, o2) -> true),
        new Customization("entity.createdAt", (o1, o2) -> true),
        new Customization("entity.actualClosureDate", (o1, o2) -> true),
        new Customization("oldEntity.updatedAt", (o1, o2) -> true),
        new Customization("oldEntity.createdAt", (o1, o2) -> true)
    ));


    ArgumentCaptor<ExecutionLogStatusEvent> argumentCaptor = ArgumentCaptor.forClass(ExecutionLogStatusEvent.class);
    BDDMockito.verify(workflowExecutionLogStatusEventPublisher, times(1)).publishExecutionLogStatusUpdateEvent(argumentCaptor.capture(),eq("ex.workflow"),eq("workflow.contact.update.reply"));
    ExecutionLogStatusEvent value = argumentCaptor.getValue();
    Assertions.assertThat(value.getEventId()).isEqualTo(100L);
    Assertions.assertThat(value.getStatus()).isEqualTo("SUCCESS");
    Assertions.assertThat(value.getStatusCode()).isEqualTo(200);
    Assertions.assertThat(value.getErrorCode()).isEqualTo(null);
    Assertions.assertThat(value.getErrorMessage()).isEqualTo(null);

    verify(exactly(0), getRequestedFor(urlEqualTo("/v1/internal/share/access/CONTACT/READ")));

    contactUpdatedEventContainerV2.stop();
    contactUpdatedEventContainer.stop();
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(
      String queue,
      String exchange,
      String eventName,
      MockMqListener mockMockMqListener) {
    Queue listenerQueue = new Queue(queue);
    rabbitAdmin.declareQueue(listenerQueue);

    Binding binding =
        BindingBuilder.bind(listenerQueue).to(new TopicExchange(exchange)).with(eventName);
    rabbitAdmin.declareBinding(binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(queue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }


  private Contact getContact(Long id, String firstName, String lastName) {
    Contact contact = new Contact();
    contact.setId(id);
    contact.setFirstName(firstName);
    contact.setLastName(lastName);
    contact.setTenantId(1000L);
    contact.setCity("Pune");
    contact.setState("Maharashtra");
    contact.setDnd(true);
    contact.setStakeholder(true);
    Set<ContactUtm> utms = new LinkedHashSet<>();
    ContactUtm contactUtm = new ContactUtm();
    contactUtm.setUtmCampaign("Campaign Updated By Workflow");
    utms.add(contactUtm);
    contact.setContactUtms(utms);
    return contact;
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  static class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  public static class TestEnvironmentSetup implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(EX_WORKFLOW, "topic")
          .withExchange(EX_SALES, "topic")
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer = new PostgreSQLContainer("postgres:10.11")
        .withDatabaseName("sales")
        .withUsername("test-user")
        .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }

}