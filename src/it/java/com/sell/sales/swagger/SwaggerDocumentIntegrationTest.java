package com.sell.sales.swagger;

import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.swagger.SwaggerDocumentIntegrationTest.TestDatabaseInitializer;
import java.io.File;
import java.io.IOException;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;

@RunWith(SpringRunner.class)
@SpringBootTest
@ContextConfiguration(initializers = {TestDatabaseInitializer.class})
@AutoConfigureTestDatabase(replace = Replace.NONE)
@TestPropertySource(
    properties = {
        "client.config.basePath=http://localhost:9090",
        "client.search.basePath=http://localhost:9090",
        "client.entity.basePath=http://localhost:9090",
        "client.deal.basePath=http://localhost:9090",
        "client.iam.basePath=http://localhost:9090",
        "client.product.basePath=http://localhost:9090",
    })
@AutoConfigureMockMvc
public class SwaggerDocumentIntegrationTest {
  @Rule
  public WireMockRule wireMockRule = new WireMockRule(9090);
  @Autowired
  private MockMvc mockMvc;
  @Autowired private ResourceLoader resourceLoader;

  @Test
  @Ignore
  public void validateSwaggerV2Document() throws Exception {
    //when
    MvcResult result =
        mockMvc
            .perform(
                get("/v2/api-docs")
                    .contentType(MediaType.APPLICATION_JSON))
            .andReturn();
    //then
    JSONAssert.assertEquals(
        getResourceAsString("classpath:swagger/v2-api-docs.json"),
        result.getResponse().getContentAsString(),
            JSONCompareMode.LENIENT);

  }
  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer =
        new PostgreSQLContainer("postgres:10.11")
            .withDatabaseName("product")
            .withUsername("test-user")
            .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }
}
