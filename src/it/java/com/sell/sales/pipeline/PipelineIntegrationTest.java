package com.sell.sales.pipeline;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.test.context.support.TestPropertySourceUtils.addInlinedPropertiesToEnvironment;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.pipeline.PipelineIntegrationTest.RabbitMQSetup;
import com.sell.sales.pipeline.PipelineIntegrationTest.TestDatabaseInitializer;
import com.sell.sales.pipeline.domain.LeadPipeline;
import com.sell.sales.pipeline.domain.LeadPipelineStage;
import com.sell.sales.pipeline.repository.LeadPipelineRepository;
import com.sell.sales.utils.TestEntity;
import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.sql.DataSource;
import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.Customization;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.comparator.CustomComparator;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = Replace.NONE)
@ContextConfiguration(initializers = {TestDatabaseInitializer.class, RabbitMQSetup.class})
@AutoConfigureMockMvc
public class PipelineIntegrationTest {
  private static final String EX_SALES = "ex.sales";
  private static final String ID_NAME_COMMAND_CONSUMER_QUEUE = "sales.idName.create.search";
  @Rule public WireMockRule wireMockRule = new WireMockRule(9090);

  @Autowired private MockMvc mockMvc;
  @Autowired private ResourceLoader resourceLoader;
  @Autowired private Environment environment;

  @Autowired private ConnectionFactory connectionFactory;
  @Autowired private AmqpAdmin rabbitAdmin;
  @Autowired private LeadPipelineRepository leadPipelineRepository;

  private static RabbitMQContainer rabbitMQContainer =
      new RabbitMQContainer("rabbitmq:3.7-management-alpine");

  private String authToken;

  @Before
  public void setUp() {
    Action action = new Action();
    action.readAll(true).updateAll(true).write(true).updateAll(true);
    PermissionDTO pipelineReadPermission = new PermissionDTO();
    pipelineReadPermission.setName("pipeline");
    pipelineReadPermission.setAction(action);
    PermissionDTO leadReadPermission = new PermissionDTO();
    leadReadPermission.setName("lead");
    leadReadPermission.setAction(action);
    Set<PermissionDTO> allowedPermissions =
        Stream.of(pipelineReadPermission, leadReadPermission).collect(Collectors.toSet());
    authToken = TestEntity.getJwt(1, 1, allowedPermissions);
  }

  @After
  public void tearDownMockServer() {
    wireMockRule.resetAll();
  }

  @Test
  public void givenLeadPipelineName_shouldCreatePipelineWithDefault4Stages() throws Exception {
    // given
    MockMqListener mockListener = new MockMqListener();
    initializeRabbitMqListener(
        ID_NAME_COMMAND_CONSUMER_QUEUE, EX_SALES, "sales.idName.create", mockListener);
    stubFor(
        WireMock.post(urlEqualTo("/1-id-name/store?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));
    // when
    MvcResult mvcResult =
        mockMvc
            .perform(
                post("/v1/pipelines")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .content("{\"name\":\"Lead Pipeline\",\"entityType\":\"LEAD\"}")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();
    // then
    String pipelineResponse = mvcResult.getResponse().getContentAsString();
    String resourceAsString =
        getResourceAsString(
            "classpath:contracts/pipeline/create-lead-pipeline-response-with-stages.json");
    JSONAssert.assertEquals(
        resourceAsString,
        pipelineResponse,
        new CustomComparator(JSONCompareMode.LENIENT, new Customization("id", (o1, o2) -> true)));
    mockListener.latch.await(500, TimeUnit.MILLISECONDS);

    String expectedPipelineAndStageCreatedIdNameCommandPayload =
        getResourceAsString(
            "classpath:contracts/pipeline/pipeline-created-idName-command-payload.json");

    JSONAssert.assertEquals(
        expectedPipelineAndStageCreatedIdNameCommandPayload,
        mockListener.actualMessage,new CustomComparator(JSONCompareMode.STRICT,
                    new Customization("data[*].id",(o, t1) -> true)));
  }

  @Test
  public void givenDealPipelineName_shouldCreatePipelineWithDefault4Stages() throws Exception {
    // given
    stubFor(
        WireMock.post(urlEqualTo("/1-id-name/store?timeout=1m"))
            .willReturn(
                aResponse()
                    .withStatus(200)
                    .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .withBody("{}")));
    // when
    MvcResult mvcResult =
        mockMvc
            .perform(
                post("/v1/pipelines")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                    .content("{\"name\":\"Deal Pipeline\",\"entityType\":\"DEAL\"}")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();
    // then
    String pipelineResponse = mvcResult.getResponse().getContentAsString();
    String resourceAsString =
        getResourceAsString(
            "classpath:contracts/pipeline/create-deal-pipeline-response-with-stages.json");
    JSONAssert.assertEquals(
        resourceAsString,
        pipelineResponse,
        new CustomComparator(JSONCompareMode.LENIENT, new Customization("id", (o1, o2) -> true)));
  }

  @Test
  @Sql("/test-scripts/stub_lead_with_pipeline.sql")
  public void givenExistingPipelineAttachedOnLead_tryToAddNewStage_shouldAddNewStageOnAllLeadPipeline() throws Exception {
    //given
    String pipelineUpdateRequest = getResourceAsString("classpath:contracts/pipeline/update-pipeline-add-new-stages.json");
    //when
            mockMvc
                    .perform(
                            put("/v1/pipelines/101")
                                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                                    .content(pipelineUpdateRequest)
                                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andReturn();
    //then
    new CountDownLatch(1).await(3,TimeUnit.SECONDS);
    LeadPipeline leadPipeline111 = leadPipelineRepository.findOne(111L);
    Collections.sort(leadPipeline111.getStages(), Comparator.comparingLong(LeadPipelineStage::getId));


    String lead1PipelineResponse = getResourceAsString("classpath:contracts/lead/lead-pipeline-response-post-adding-new-stage-1.json");
    JSONAssert
            .assertEquals(lead1PipelineResponse,new ObjectMapper().writeValueAsString(leadPipeline111.getStages()),new CustomComparator(JSONCompareMode.STRICT,
                    new Customization("[*].id",(o, t1) -> true),
                new Customization("[*].pipelineStageId",(o, t1) -> true)));

    LeadPipeline leadPipeline222 = leadPipelineRepository.findOne(222L);
    Collections.sort(leadPipeline222.getStages(), Comparator.comparingLong(LeadPipelineStage::getId));


    String lead6PipelineResponse = getResourceAsString("classpath:contracts/lead/lead-pipeline-response-post-adding-new-stage-2.json");
    JSONAssert
            .assertEquals(lead6PipelineResponse,new ObjectMapper().writeValueAsString(leadPipeline222.getStages()),new CustomComparator(JSONCompareMode.STRICT,
                    new Customization("[*].id",(o, t1) -> true),
                new Customization("[*].pipelineStageId",(o, t1) -> true)));


  }

  @Test
  @Sql("/test-scripts/stub_lead_with_pipeline.sql")
  public void givenNonAttachedPipeline_tryToUpdate_shouldUpdate() throws Exception {
    //given
    String pipelineUpdateRequest = getResourceAsString("classpath:contracts/pipeline/update-non-attached-pipeline.json");
    //when
    MvcResult mvcResult = mockMvc
        .perform(
            put("/v1/pipelines/201")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + authToken)
                .content(pipelineUpdateRequest)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
    //then
    new CountDownLatch(1).await(3,TimeUnit.SECONDS);
    LeadPipeline leadPipeline111 = leadPipelineRepository.findOne(111L);
    Collections.sort(leadPipeline111.getStages(), Comparator.comparingLong(LeadPipelineStage::getId));


    String lead1PipelineResponse = getResourceAsString("classpath:contracts/lead/non-attached-pipeline-response.json");
    JSONAssert
        .assertEquals(lead1PipelineResponse,mvcResult.getResponse().getContentAsString(),new CustomComparator(JSONCompareMode.STRICT,
            new Customization("[*].id",(o, t1) -> true),
            new Customization("updatedAt",(o, t1) -> true),
            new Customization("[*].pipelineStageId",(o, t1) -> true),
            new Customization("stages[*].createdAt",(o, t1) -> true),
            new Customization("stages[*].updatedAt",(o, t1) -> true),
            new Customization("stages[*].id",(o, t1) -> true)
            ));



  }
  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }

  private SimpleMessageListenerContainer initializeRabbitMqListener(
      String consumerQueue, String exchange, String eventName, MockMqListener mockMockMqListener) {
    Binding binding =
        BindingBuilder.bind(new Queue(consumerQueue))
            .to(new TopicExchange(exchange))
            .with(eventName);

    rabbitAdmin.declareBinding(binding);

    MessageListenerAdapter listenerAdapter =
        new MessageListenerAdapter(mockMockMqListener, "receiveMessage");

    SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
    container.setConnectionFactory(connectionFactory);
    container.setQueueNames(consumerQueue);
    container.setMessageListener(listenerAdapter);
    container.start();
    return container;
  }

  class MockMqListener {

    CountDownLatch latch = new CountDownLatch(1);
    String actualMessage;

    public void receiveMessage(byte[] messageInBytes) {
      this.actualMessage = new String(messageInBytes);
    }
  }

  @TestConfiguration
  @EnableJpaRepositories
  public static class TestDatabaseInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    PostgreSQLContainer postgreSQLContainer =
        new PostgreSQLContainer("postgres:10.11")
            .withDatabaseName("pipeline")
            .withUsername("test-user")
            .withPassword("test-password");

    @Bean
    public PostgreSQLContainer postgreSQLContainer() {
      postgreSQLContainer.start();
      return postgreSQLContainer;
    }

    @Bean
    @Primary
    public DataSource dataSource(PostgreSQLContainer container) {
      postgreSQLContainer.start();

      return DataSourceBuilder.create()
          .url(container.getJdbcUrl())
          .username(container.getUsername())
          .password(container.getPassword())
          .driverClassName(container.getDriverClassName())
          .build();
    }

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      postgreSQLContainer.start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.url=" + postgreSQLContainer.getJdbcUrl());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.username=" + postgreSQLContainer.getUsername());

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "spring.datasource.password=" + postgreSQLContainer.getPassword());
    }
  }

  @TestConfiguration
  public static class RabbitMQSetup
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
      rabbitMQContainer
          .withExchange(EX_SALES, "topic")
          .withQueue(ID_NAME_COMMAND_CONSUMER_QUEUE)
          .start();

      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.port=" + rabbitMQContainer.getAmqpPort());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.host=" + rabbitMQContainer.getContainerIpAddress());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.username=" + rabbitMQContainer.getAdminUsername());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext,
          "core.rabbitmq.password=" + rabbitMQContainer.getAdminPassword());
      addInlinedPropertiesToEnvironment(
          configurableApplicationContext, "core.rabbitmq.virtualHost=" + "/");
    }
  }
}
