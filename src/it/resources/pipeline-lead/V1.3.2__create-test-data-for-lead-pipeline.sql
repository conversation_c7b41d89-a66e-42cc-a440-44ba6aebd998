-- CREATE PIPELINE
DELETE FROM lead_pipeline_stage;
DELETE FROM lead;
DELETE FROM lead_pipeline;
DELETE FROM pipeline_stage;
DELETE FROM pipeline;
DELETE FROM users;

INSERT INTO pipeline (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                      active, entity_type, lost_reasons, name, unqualified_reasons)
VALUES (1, '2019-10-01 05:43:45.675000', 1, '2019-10-01 05:43:45.675000', 1, FALSE, 0, 1, 1, TRUE, 'LEAD',
        E'\\xaced0005757200135b4c6a6176612e6c616e672e537472696e673badd256e7e91d7b4702000078700000000774000b4e6f20666f6c6c6f77757074000e4e6f7420696e7465726573746564740016426f6f6b6564207769746820636f6d70657469746f727400184e6f7420616e73776572696e672f726573706f6e64696e67740026426f756768742070726f647563742f73657276696365207769746820636f6d70657469746f7274000a4c6f7720627564676574740030506f7374706f6e656420746865206465636973696f6e206f6620627579696e672070726f647563742f73657276696365',
        'nurturing pipeline',
        E'\\xaced0005757200135b4c6a6176612e6c616e672e537472696e673badd256e7e91d7b4702000078700000000874001542756467657420646f6573206e6f74206d6174636874000d46616c736520656e717569727974000c57726f6e67206e756d62657274002b437573746f6d657220616c726561647920626f75676874207468652070726f647563742f73657276696365740026426f756768742070726f647563742f73657276696365207769746820636f6d70657469746f72740012446964206e6f74207069636b2070686f6e657400164e6f7420696e746572657374656420616e796d6f72657400124475706c696361746520437573746f6d6572');

-- CREATE PIPELINE STAGES
INSERT INTO pipeline_stage (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id,
                            description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES (1, '2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 1, 'Open stage', 'OPEN', 'Open', 1, 0,
        1);
INSERT INTO pipeline_stage (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id,
                            description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES (2, '2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Won stage', 'OPEN', 'Won', 2,
        100, 1);
INSERT INTO pipeline_stage (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id,
                            description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES (3, '2019-10-01 05:43:45.723000', 1, '2019-10-01 05:43:45.723000', 1, FALSE, 0, 1, 'Closed Unqualified stage', 'OPEN',
        'Closed Unqualified', 3, 0, 1);
INSERT INTO pipeline_stage (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id,
                            description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES (4, '2019-10-01 05:43:45.725000', 1, '2019-10-01 05:43:45.725000', 1, FALSE, 0, 1, 'Closed Lost stage', 'CLOSED_LOST',
        'Closed Lost', 4, 0, 1);

-- CREATE LEAD
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name,
                  pipeline, pipeline_stage, pipeline_stage_reason, salutation)
VALUES (1, '2019-10-01 05:52:10.919000', 1, '2019-10-01 05:52:10.919000', 2, FALSE, 0, 1, 1,
       '{}', 'Tony', 'Stark',
        1, 1, NULL, 473);

-- CREATE LEAD PIPELINE
INSERT INTO lead_pipeline(id, tenant_id, lead_id, pipeline_id)  OVERRIDING SYSTEM VALUE
VALUES (101,1, 1, 1);

INSERT INTO lead_pipeline_stage(pipeline_stage_id, status, forecasting_type, position, name, started_at, completed_at, lead_pipeline_id,win_likelihood)
VALUES (1, 'COMPLETED', 'OPEN', 1, 'Open', '2019-10-01 05:43:45.717000', '2019-10-03 05:43:45.717000', 101,100);

INSERT INTO lead_pipeline_stage(pipeline_stage_id, status, forecasting_type, position, name, started_at,lead_pipeline_id, win_likelihood)
VALUES (3, 'IN_PROGRESS', 'OPEN', 3, 'Closed Unqualified', '2019-10-01 05:43:45.717000', 101,100);

INSERT INTO users(id, tenant_id, name)
VALUES
(2000,1000, 'Tony Stark'),
(1,1, 'Tony Stark'),
(10,100, 'Tony Stark'),
(11,100, 'test user'),
(2051,1050, 'Bony Stark');
