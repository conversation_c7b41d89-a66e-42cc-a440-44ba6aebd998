{"swagger": "2.0", "info": {"description": "This is the Core API documentation", "version": "0.0.1-SNAPSHOT", "title": "Core", "termsOfService": "https://amura.com/termsofservice", "contact": {"name": "<PERSON><PERSON>", "url": "https://amura.com", "email": "<EMAIL>"}, "license": {"name": "License Info", "url": "https://amura.com/license"}}, "host": "localhost", "basePath": "/", "tags": [{"name": "Contacts", "description": "Contact Controller"}, {"name": "Pipelines", "description": "Pipeline Controller"}, {"name": "audit-events-mvc-endpoint", "description": "Audit Events Mvc Endpoint"}, {"name": "contact-layout-controller", "description": "Contact Layout Controller"}, {"name": "contact-health-controller", "description": "Contact Health Controller"}, {"name": "endpoint-mvc-adapter", "description": "Endpoint Mvc Adapter"}, {"name": "environment-manager-mvc-endpoint", "description": "Environment Manager M<PERSON><PERSON> Endpoint"}, {"name": "environment-mvc-endpoint", "description": "Environment Mvc Endpoint"}, {"name": "generic-postable-mvc-endpoint", "description": "Generic Postable Mvc Endpoint"}, {"name": "health-mvc-endpoint", "description": "Health Mvc Endpoint"}, {"name": "heapdump-mvc-endpoint", "description": "Heapdump Mvc Endpoint"}, {"name": "lead-capture-forms-controller", "description": "Lead Capture Forms Controller"}, {"name": "lead-controller", "description": "Lead Controller"}, {"name": "lead-health-controller", "description": "Lead Health Controller"}, {"name": "lead-layout-controller", "description": "Lead Layout Controller"}, {"name": "lead-pipeline-controller", "description": "Lead Pipeline Controller"}, {"name": "loggers-mvc-endpoint", "description": "Loggers Mvc Endpoint"}, {"name": "metrics-mvc-endpoint", "description": "Metrics Mvc Endpoint"}, {"name": "prometheus-scrape-mvc-endpoint", "description": "Prometheus Scrape Mvc Endpoint"}], "paths": {"/archaius": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/archaius.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_1", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/auditevents": {"get": {"tags": ["audit-events-mvc-endpoint"], "summary": "findByPrincipalAndAfterAndType", "operationId": "findByPrincipalAndAfterAndTypeUsingGET", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "after", "in": "query", "description": "after", "required": false, "type": "string", "format": "date-time"}, {"name": "principal", "in": "query", "description": "principal", "required": false, "type": "string"}, {"name": "type", "in": "query", "description": "type", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/auditevents.json": {"get": {"tags": ["audit-events-mvc-endpoint"], "summary": "findByPrincipalAndAfterAndType", "operationId": "findByPrincipalAndAfterAndTypeUsingGET_1", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "after", "in": "query", "description": "after", "required": false, "type": "string", "format": "date-time"}, {"name": "principal", "in": "query", "description": "principal", "required": false, "type": "string"}, {"name": "type", "in": "query", "description": "type", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/autoconfig": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_2", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/autoconfig.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_3", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/beans": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_4", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/beans.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_5", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/configprops": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_6", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/configprops.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_7", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/dump": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_8", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/dump.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_9", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/env": {"get": {"tags": ["environment-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_20", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["environment-manager-mvc-endpoint"], "summary": "value", "operationId": "valueUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "params", "in": "query", "description": "params", "required": true, "items": {"type": "object", "additionalProperties": {"type": "string"}}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/env.json": {"get": {"tags": ["environment-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_21", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/env/reset": {"post": {"tags": ["environment-manager-mvc-endpoint"], "summary": "reset", "operationId": "resetUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/env/{name}": {"get": {"tags": ["environment-mvc-endpoint"], "summary": "value", "operationId": "valueUsingGET", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "name", "in": "path", "description": "name", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/features": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_10", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/features.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_11", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/flyway": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_12", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/flyway.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_13", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/health": {"get": {"tags": ["health-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_22", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/health.json": {"get": {"tags": ["health-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_23", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/heapdump": {"get": {"tags": ["heapdump-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_24", "produces": ["application/octet-stream"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "live", "in": "query", "description": "live", "required": false, "type": "boolean", "default": true}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/heapdump.json": {"get": {"tags": ["heapdump-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_25", "produces": ["application/octet-stream"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "live", "in": "query", "description": "live", "required": false, "type": "boolean", "default": true}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/info": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_14", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/info.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_15", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/loggers": {"get": {"tags": ["loggers-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_26", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/loggers.json": {"get": {"tags": ["loggers-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_27", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/loggers/{name}": {"get": {"tags": ["loggers-mvc-endpoint"], "summary": "get", "operationId": "getUsingGET", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "name", "in": "path", "description": "name", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["loggers-mvc-endpoint"], "summary": "set", "operationId": "setUsingPOST", "consumes": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "configuration", "description": "configuration", "required": true, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, {"name": "name", "in": "path", "description": "name", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/mappings": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_16", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/mappings.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_17", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/metrics": {"get": {"tags": ["metrics-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_28", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/metrics.json": {"get": {"tags": ["metrics-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_29", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/metrics/{name}": {"get": {"tags": ["metrics-mvc-endpoint"], "summary": "value", "operationId": "valueUsingGET_1", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "name", "in": "path", "description": "name", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/prometheus": {"get": {"tags": ["prometheus-scrape-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_30", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/prometheus.json": {"get": {"tags": ["prometheus-scrape-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingGET_31", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/refresh": {"post": {"tags": ["generic-postable-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/refresh.json": {"post": {"tags": ["generic-postable-mvc-endpoint"], "summary": "invoke", "operationId": "invokeUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/trace": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_18", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/trace.json": {"get": {"tags": ["endpoint-mvc-adapter"], "summary": "invoke", "operationId": "invokeUsingGET_19", "produces": ["application/vnd.spring-boot.actuator.v1+json", "application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts": {"get": {"tags": ["Contacts"], "summary": "Get contact list", "operationId": "getContactListUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "companyId", "in": "query", "description": "companyId", "required": false, "type": "integer", "format": "int64"}, {"name": "id", "in": "query", "description": "id", "required": false, "type": "array", "items": {"type": "integer", "format": "int64"}, "collectionFormat": "multi"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "stakeholder", "in": "query", "description": "stakeholder", "required": false, "type": "boolean"}, {"name": "view", "in": "query", "description": "view", "required": false, "type": "string", "default": "summary"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ResponseEntity", "$ref": "#/definitions/ResponseEntity"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["Contacts"], "summary": "Create contact", "operationId": "createContactUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "contactCreateRequest", "description": "contactCreateRequest", "required": true, "schema": {"originalRef": "ContactCreateRequest", "$ref": "#/definitions/ContactCreateRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ContactResponse", "$ref": "#/definitions/ContactResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false, "x-access-policy": {"action": "create", "policy": "records", "resource": "contact"}}}, "/v1/contacts/delete": {"delete": {"tags": ["Contacts"], "summary": "delete multiple contacts", "operationId": "deleteContactsByIdsUsingDELETE", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "bulkDeleteRequest", "description": "bulkDeleteRequest", "required": true, "schema": {"originalRef": "BulkDeleteRequest", "$ref": "#/definitions/BulkDeleteRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "BulkDeleteResponse", "$ref": "#/definitions/BulkDeleteResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/duplicates/{uniquenessStrategyField}": {"get": {"tags": ["Contacts"], "summary": "validateDuplicates", "operationId": "validateDuplicatesUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "uniquenessStrategy", "in": "query", "description": "uniquenessStrategy", "required": false, "type": "string", "enum": ["NONE", "EMAIL", "PHONE", "EMAIL_PHONE"]}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/layout": {"get": {"tags": ["Contacts"], "summary": "getLayoutByView", "operationId": "getLayoutByViewUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "mode", "in": "query", "description": "mode", "required": false, "type": "string", "default": "new"}, {"name": "view", "in": "query", "description": "view", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "Layout", "$ref": "#/definitions/Layout"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/layout/list": {"get": {"tags": ["contact-layout-controller"], "summary": "getListLayout", "operationId": "getListLayoutUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ListLayoutResponse", "$ref": "#/definitions/ListLayoutResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/lookup": {"get": {"tags": ["Contacts"], "summary": "lookUpContacts", "operationId": "lookUpContactsUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "q", "in": "query", "description": "q", "required": true, "type": "string"}, {"name": "view", "in": "query", "description": "view", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PageOfLookUp", "$ref": "#/definitions/PageOfLookUp"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/picklists/{picklistName}": {"delete": {"tags": ["Contacts"], "summary": "Delete contact picklist value", "operationId": "deletePicklistValueUsingDELETE", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"name": "picklistName", "in": "path", "description": "picklistName", "required": true, "type": "string"}, {"name": "value", "in": "query", "description": "value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/reassign": {"put": {"tags": ["Contacts"], "summary": "reassign contacts to new owner", "operationId": "reassignContactsByIdsUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "contactBulkReassignRequest", "description": "contactBulkReassignRequest", "required": true, "schema": {"originalRef": "ContactBulkReassignRequest", "$ref": "#/definitions/ContactBulkReassignRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "BulkReassignResponse", "$ref": "#/definitions/BulkReassignResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/share": {"post": {"tags": ["Contacts"], "summary": "Create an ACL share rule for all entityType records", "operationId": "createShareRuleForAllEntityTypeRecordUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/share/{shareRuleId}": {"get": {"tags": ["Contacts"], "summary": "Get an ACL share rule", "operationId": "getShareRuleUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["Contacts"], "summary": "Update a shareRule for All entityType records", "operationId": "updateShareRuleForAllEntityTypeRecordsUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["Contacts"], "summary": "Delete a shareRule", "operationId": "deleteShareRuleUsingDELETE", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/{contactId}": {"get": {"tags": ["Contacts"], "summary": "Get contact", "operationId": "getContactUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "contactId", "in": "path", "description": "contactId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ContactResponse", "$ref": "#/definitions/ContactResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["Contacts"], "summary": "Update contact", "operationId": "updateContactUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "contactId", "in": "path", "description": "contactId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateRequest", "description": "updateRequest", "required": true, "schema": {"originalRef": "ContactUpdateRequest", "$ref": "#/definitions/ContactUpdateRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ContactResponse", "$ref": "#/definitions/ContactResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["Contacts"], "summary": "Delete contact", "operationId": "deleteContactUsingDELETE", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "contactId", "in": "path", "description": "contactId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/{entityId}/share": {"post": {"tags": ["Contacts"], "summary": "Create an ACL share rule for record Id", "operationId": "createShareRuleUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/{id}": {"patch": {"tags": ["Contacts"], "summary": "Patch Contact", "operationId": "patchContactUsingPATCH", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "contactPatchRequest", "description": "contactPatchRequest", "required": true, "schema": {"originalRef": "ContactPatchRequest", "$ref": "#/definitions/ContactPatchRequest"}}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ContactSummary", "$ref": "#/definitions/ContactSummary"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/{id}/owner": {"put": {"tags": ["Contacts"], "summary": "Update contact owner", "operationId": "updateOwnerUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"originalRef": "UpdateOwnerRequest", "$ref": "#/definitions/UpdateOwnerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ChangeOwnerResponseOflong", "$ref": "#/definitions/ChangeOwnerResponseOflong"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/contacts/{shareRuleId}/share/{entityId}": {"put": {"tags": ["Contacts"], "summary": "Update a shareRule", "operationId": "updateShareRuleUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/lead-capture-forms": {"get": {"tags": ["lead-capture-forms-controller"], "summary": "getWebformIdsByProduct", "operationId": "getWebformIdsByProductUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"name": "productId", "in": "query", "description": "productId", "required": true, "type": "integer", "format": "int64"}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["lead-capture-forms-controller"], "summary": "Create new Lead capture form", "operationId": "createFormUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "formCreateRequest", "description": "formCreateRequest", "required": true, "schema": {"originalRef": "LeadCaptureFormRequest", "$ref": "#/definitions/LeadCaptureFormRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadCaptureFormCreatedResponse", "$ref": "#/definitions/LeadCaptureFormCreatedResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false, "x-access-policy": {"action": "create", "policy": "storage", "resource": "lead-capture-form"}}}, "/v1/lead-capture-forms/search": {"post": {"tags": ["lead-capture-forms-controller"], "summary": "getResults", "operationId": "getResultsUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"in": "body", "name": "searchRequest", "description": "searchRequest", "required": true, "schema": {"originalRef": "SearchRequest", "$ref": "#/definitions/SearchRequest"}}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PageOfSearchResponse", "$ref": "#/definitions/PageOfSearchResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/lead-capture-forms/{formId}": {"get": {"tags": ["lead-capture-forms-controller"], "summary": "getLeadCaptureFormDetails", "operationId": "getLeadCaptureFormDetailsUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "formId", "in": "path", "description": "formId", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadCaptureFormDetails", "$ref": "#/definitions/LeadCaptureFormDetails"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["lead-capture-forms-controller"], "summary": "updateLeadCaptureForm", "operationId": "updateLeadCaptureFormUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "formId", "in": "path", "description": "formId", "required": true, "type": "string", "format": "uuid"}, {"in": "body", "name": "formUpdateRequest", "description": "formUpdateRequest", "required": true, "schema": {"originalRef": "LeadCaptureFormRequest", "$ref": "#/definitions/LeadCaptureFormRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadCaptureFormDetails", "$ref": "#/definitions/LeadCaptureFormDetails"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/lead-capture-forms/{formId}/activate": {"post": {"tags": ["lead-capture-forms-controller"], "summary": "activate", "operationId": "activateUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "formId", "in": "path", "description": "formId", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadCaptureForm", "$ref": "#/definitions/LeadCaptureForm"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/lead-capture-forms/{formId}/deactivate": {"post": {"tags": ["lead-capture-forms-controller"], "summary": "deactivate", "operationId": "deactivateUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "formId", "in": "path", "description": "formId", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadCaptureForm", "$ref": "#/definitions/LeadCaptureForm"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads": {"get": {"tags": ["lead-controller"], "summary": "getLeadIdsByProduct", "operationId": "getLeadIdsByProductUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"name": "productId", "in": "query", "description": "productId", "required": true, "type": "string"}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadProductResponse", "$ref": "#/definitions/LeadProductResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["lead-controller"], "summary": "Create lead", "operationId": "createLeadUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "leadRequest", "description": "leadRequest", "required": true, "schema": {"originalRef": "LeadRequest", "$ref": "#/definitions/LeadRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadResponse", "$ref": "#/definitions/LeadResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false, "x-access-policy": {"action": "create", "policy": "records", "resource": "lead"}}}, "/v1/leads/count-by-owner": {"get": {"tags": ["lead-controller"], "summary": "Get lead count by ownerId", "operationId": "getCountByOwnerUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": true, "type": "string", "format": "date-time"}, {"name": "ownerId", "in": "query", "description": "ownerId", "required": true, "type": "array", "items": {"type": "integer", "format": "int64"}, "collectionFormat": "multi"}, {"name": "toDate", "in": "query", "description": "toDate", "required": true, "type": "string", "format": "date-time"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"originalRef": "LeadCount", "$ref": "#/definitions/LeadCount"}}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/delete": {"delete": {"tags": ["lead-controller"], "summary": "delete multiple leads", "operationId": "deleteLeadsByIdsUsingDELETE", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "bulkDeleteRequest", "description": "bulkDeleteRequest", "required": true, "schema": {"originalRef": "BulkDeleteRequest", "$ref": "#/definitions/BulkDeleteRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "BulkDeleteResponse", "$ref": "#/definitions/BulkDeleteResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/duplicates/{uniquenessStrategyField}": {"get": {"tags": ["lead-controller"], "summary": "validateDuplicates", "operationId": "validateDuplicatesUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "uniquenessStrategyField", "in": "path", "description": "uniquenessStrategyField", "required": true, "type": "string", "enum": ["NONE", "EMAIL", "PHONE", "EMAIL_PHONE"]}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/import": {"post": {"tags": ["lead-controller"], "summary": "Imports a given lead into the system depending on the provided duplication strategy", "operationId": "importLeadUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "leadImportRequestData", "description": "leadImportRequestData", "required": true, "schema": {"originalRef": "LeadImportRequestData", "$ref": "#/definitions/LeadImportRequestData"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/layout/list": {"get": {"tags": ["lead-layout-controller"], "summary": "getListLayout", "operationId": "getListLayoutUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ListLayoutResponse", "$ref": "#/definitions/ListLayoutResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/picklists/{picklistName}": {"delete": {"tags": ["lead-controller"], "summary": "Delete lead picklist value", "operationId": "deletePicklistValueUsingDELETE_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "query", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"name": "picklistName", "in": "path", "description": "picklistName", "required": true, "type": "string"}, {"name": "value", "in": "query", "description": "value", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/reassign": {"put": {"tags": ["lead-controller"], "summary": "reassign leads to new owner", "operationId": "reassignLeadsByIdsUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "leadBulkReassignRequest", "description": "leadBulkReassignRequest", "required": true, "schema": {"originalRef": "LeadBulkReassignRequest", "$ref": "#/definitions/LeadBulkReassignRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "BulkReassignResponse", "$ref": "#/definitions/BulkReassignResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/share": {"post": {"tags": ["lead-controller"], "summary": "Create an ACL share rule for all entityType records", "operationId": "createShareRuleForAllEntityTypeRecordUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/share/{shareRuleId}": {"get": {"tags": ["lead-controller"], "summary": "Get an ACL share rule", "operationId": "getShareRuleUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["lead-controller"], "summary": "Update a shareRule for All entityType records", "operationId": "updateShareRuleForAllEntityTypeRecordsUsingPUT_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["lead-controller"], "summary": "Delete a shareRule", "operationId": "deleteShareRuleUsingDELETE_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{entityId}/share": {"post": {"tags": ["lead-controller"], "summary": "Create an ACL share rule for record Id", "operationId": "createShareRuleUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{id}": {"get": {"tags": ["lead-controller"], "summary": "getLead", "operationId": "getLeadUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadResponse", "$ref": "#/definitions/LeadResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["lead-controller"], "summary": "updateLeadById", "operationId": "updateLeadByIdUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "leadUpdateRequest", "description": "leadUpdateRequest", "required": true, "schema": {"originalRef": "LeadUpdateRequest", "$ref": "#/definitions/LeadUpdateRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadResponse", "$ref": "#/definitions/LeadResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["lead-controller"], "summary": "deleteLead", "operationId": "deleteLeadUsingDELETE", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"name": "publishUsage", "in": "query", "description": "publishUsage", "required": false, "type": "boolean", "default": true}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "patch": {"tags": ["lead-controller"], "summary": "<PERSON>", "operationId": "patchLeadUsingPATCH", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "leadPatchRequest", "description": "leadPatchRequest", "required": true, "schema": {"originalRef": "LeadPatchRequest", "$ref": "#/definitions/LeadPatchRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadSummary", "$ref": "#/definitions/LeadSummary"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{id}/convert": {"post": {"tags": ["lead-controller"], "summary": "Converts a given lead", "operationId": "convertLeadUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "leadConversionRequest", "description": "leadConversionRequest", "required": false, "schema": {"originalRef": "LeadConversionRequest", "$ref": "#/definitions/LeadConversionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "LeadConversionResponse", "$ref": "#/definitions/LeadConversionResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{id}/duplicates": {"get": {"tags": ["lead-controller"], "summary": "getDuplicateRecords", "operationId": "getDuplicateRecordsUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "DuplicateRecordDTO", "$ref": "#/definitions/DuplicateRecordDTO"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{id}/has-duplicates": {"get": {"tags": ["lead-controller"], "summary": "hasDuplicate", "operationId": "hasDuplicateUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "DuplicateRecordDTO", "$ref": "#/definitions/DuplicateRecordDTO"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{id}/owner": {"put": {"tags": ["lead-controller"], "summary": "Update lead owner", "operationId": "updateOwnerUsingPUT_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"originalRef": "UpdateOwnerRequest", "$ref": "#/definitions/UpdateOwnerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ChangeOwnerResponseOflong", "$ref": "#/definitions/ChangeOwnerResponseOflong"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{leadId}/pipeline": {"get": {"tags": ["lead-pipeline-controller"], "summary": "Gets the details of stages of given lead's pipeline", "operationId": "getLeadPipelineStagesUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "leadId", "in": "path", "description": "leadId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{leadId}/pipeline-stages/{stageId}/activate": {"post": {"tags": ["lead-pipeline-controller"], "summary": "Marks the given pipeline stage as active for the given lead", "operationId": "getLeadPipelineStagesUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "leadId", "in": "path", "description": "leadId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"originalRef": "ActivateStageRequest", "$ref": "#/definitions/ActivateStageRequest"}}, {"name": "stageId", "in": "path", "description": "stageId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/leads/{shareRuleId}/share/{entityId}": {"put": {"tags": ["lead-controller"], "summary": "Update a shareRule", "operationId": "updateShareRuleUsingPUT_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines": {"get": {"tags": ["Pipelines"], "summary": "Get all available (active & inactive) pipelines", "operationId": "getPipelinesUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "entityType", "in": "query", "description": "entityType", "required": true, "type": "string"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PageOfPipelineResponse", "$ref": "#/definitions/PageOfPipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["Pipelines"], "summary": "Create a pipeline", "operationId": "createPipelineUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createPipelineRequest", "description": "createPipelineRequest", "required": true, "schema": {"originalRef": "CreatePipelineRequest", "$ref": "#/definitions/CreatePipelineRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/lookup": {"get": {"tags": ["Pipelines"], "summary": "lookup for pipeline", "operationId": "getLookupResultsUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "entityType", "in": "query", "description": "entityType", "required": true, "type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, {"name": "q", "in": "query", "description": "q", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PageOfSearchResponse", "$ref": "#/definitions/PageOfSearchResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/search": {"post": {"tags": ["Pipelines"], "summary": "getResults", "operationId": "getResultsUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "page", "in": "query", "description": "Results page you want to retrieve (0..N)", "required": false, "type": "integer", "format": "int32"}, {"in": "body", "name": "searchRequest", "description": "searchRequest", "required": true, "schema": {"originalRef": "SearchRequest", "$ref": "#/definitions/SearchRequest"}}, {"name": "size", "in": "query", "description": "Number of records per page.", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PageOfSearchResponse", "$ref": "#/definitions/PageOfSearchResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/share": {"post": {"tags": ["Pipelines"], "summary": "Create an ACL share rule for all entityType records", "operationId": "createShareRuleForAllEntityTypeRecordUsingPOST_2", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/share/{shareRuleId}": {"get": {"tags": ["Pipelines"], "summary": "Get an ACL share rule", "operationId": "getShareRuleUsingGET_2", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["Pipelines"], "summary": "Update a shareRule for All entityType records", "operationId": "updateShareRuleForAllEntityTypeRecordsUsingPUT_2", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["Pipelines"], "summary": "Delete a shareRule", "operationId": "deleteShareRuleUsingDELETE_2", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{entityId}/share": {"post": {"tags": ["Pipelines"], "summary": "Create an ACL share rule for record Id", "operationId": "createShareRuleUsingPOST_2", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createShareRuleRequest", "description": "createShareRuleRequest", "required": true, "schema": {"originalRef": "CreateShareRuleRequest", "$ref": "#/definitions/CreateShareRuleRequest"}}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}": {"get": {"tags": ["Pipelines"], "summary": "Get a pipeline", "operationId": "getPipelineUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["Pipelines"], "summary": "Update a pipeline", "operationId": "updatePipelineUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updatePipelineRequest", "description": "updatePipelineRequest", "required": true, "schema": {"originalRef": "UpdatePipelineRequest", "$ref": "#/definitions/UpdatePipelineRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["Pipelines"], "summary": "Delete a pipeline", "operationId": "deletePipelineUsingDELETE", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}/activate": {"post": {"tags": ["Pipelines"], "summary": "Activates the pipeline", "operationId": "activateUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}/deactivate": {"post": {"tags": ["Pipelines"], "summary": "Deactivates the pipeline", "operationId": "deactivateUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}/pipelineStageReasons": {"get": {"tags": ["Pipelines"], "summary": "Get all available  pipeline stage reasons in a pipeline", "operationId": "getPipelineStageReasonsUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}/stages": {"get": {"tags": ["Pipelines"], "summary": "Get all available (active & inactive) pipeline stages in a pipeline", "operationId": "getPipelineStagesUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"originalRef": "PipelineStageResponse", "$ref": "#/definitions/PipelineStageResponse"}}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "post": {"tags": ["Pipelines"], "summary": "Create a pipeline stage in a pipeline", "operationId": "createPipelineStageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"in": "body", "name": "createPipelineStageRequest", "description": "createPipelineStageRequest", "required": true, "schema": {"originalRef": "CreatePipelineStageRequest", "$ref": "#/definitions/CreatePipelineStageRequest"}}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineStageResponse", "$ref": "#/definitions/PipelineStageResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{pipelineId}/stages/{pipelineStageId}": {"get": {"tags": ["Pipelines"], "summary": "Get a pipeline stage in a pipeline", "operationId": "getPipelineStageUsingGET", "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}, {"name": "pipelineStageId", "in": "path", "description": "pipelineStageId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineStageResponse", "$ref": "#/definitions/PipelineStageResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "put": {"tags": ["Pipelines"], "summary": "Update a pipeline stage in a pipeline", "operationId": "updatePipelineStageUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}, {"name": "pipelineStageId", "in": "path", "description": "pipelineStageId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updatePipelineStageRequest", "description": "updatePipelineStageRequest", "required": true, "schema": {"originalRef": "UpdatePipelineStageRequest", "$ref": "#/definitions/UpdatePipelineStageRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "PipelineStageResponse", "$ref": "#/definitions/PipelineStageResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}, "delete": {"tags": ["Pipelines"], "summary": "Delete a pipeline stage in a pipeline", "operationId": "deletePipelineStageUsingDELETE", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "pipelineId", "in": "path", "description": "pipelineId", "required": true, "type": "integer", "format": "int64"}, {"name": "pipelineStageId", "in": "path", "description": "pipelineStageId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK"}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}, "/v1/pipelines/{shareRuleId}/share/{entityId}": {"put": {"tags": ["Pipelines"], "summary": "Update a shareRule", "operationId": "updateShareRuleUsingPUT_2", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "Authorization", "in": "header", "description": "Description of header", "required": false, "type": "string"}, {"name": "entityId", "in": "path", "description": "entityId", "required": true, "type": "integer", "format": "int64"}, {"name": "shareRuleId", "in": "path", "description": "shareRuleId", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "updateShareRuleRequest", "description": "updateShareRuleRequest", "required": true, "schema": {"originalRef": "UpdateShareRuleRequest", "$ref": "#/definitions/UpdateShareRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"originalRef": "ShareRuleResponse", "$ref": "#/definitions/ShareRuleResponse"}}, "406": {"description": "406 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}, "500": {"description": "500 message", "schema": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}}}, "security": [{"JWT": ["global"]}], "deprecated": false}}}, "securityDefinitions": {"JWT": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "definitions": {"Action": {"type": "object", "properties": {"call": {"type": "boolean"}, "delete": {"type": "boolean"}, "deleteAll": {"type": "boolean"}, "document": {"type": "boolean"}, "email": {"type": "boolean"}, "meeting": {"type": "boolean"}, "note": {"type": "boolean"}, "quotation": {"type": "boolean"}, "read": {"type": "boolean"}, "readAll": {"type": "boolean"}, "sms": {"type": "boolean"}, "task": {"type": "boolean"}, "update": {"type": "boolean"}, "updateAll": {"type": "boolean"}, "write": {"type": "boolean"}}, "title": "Action"}, "ActionConfig": {"type": "object", "properties": {"columnSelector": {"type": "boolean"}, "create": {"type": "boolean"}, "filter": {"type": "boolean"}, "importItems": {"type": "boolean"}, "search": {"type": "boolean"}}, "title": "ActionConfig"}, "ActivateStageRequest": {"type": "object", "properties": {"reasonForClosing": {"type": "string"}}, "title": "ActivateStageRequest"}, "BulkDeleteRequest": {"type": "object", "properties": {"entityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "userId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}}, "title": "BulkDeleteRequest"}, "BulkDeleteResponse": {"type": "object", "properties": {"errorCount": {"type": "integer", "format": "int64"}, "response": {"type": "array", "items": {"originalRef": "DeleteDetail", "$ref": "#/definitions/DeleteDetail"}}, "successCount": {"type": "integer", "format": "int64"}}, "title": "BulkDeleteResponse"}, "BulkReassignResponse": {"type": "object", "properties": {"errorCount": {"type": "integer", "format": "int64"}, "response": {"type": "array", "items": {"originalRef": "ReassignDetail", "$ref": "#/definitions/ReassignDetail"}}, "successCount": {"type": "integer", "format": "int64"}}, "title": "BulkReassignResponse"}, "ChangeOwnerResponseOflong": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "newOwnerId": {"type": "integer", "format": "int64"}, "oldOwnerId": {"type": "integer", "format": "int64"}}, "title": "ChangeOwnerResponseOflong"}, "ContactBulkReassignRequest": {"type": "object", "properties": {"contactIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "leadIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "ownerId": {"type": "integer", "format": "int64"}, "executeWorkflow": {"type": "boolean"}, "sendEmail": {"type": "boolean"}}, "title": "ContactBulkReassignRequest"}, "ContactConversionRequest": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["CREATE", "EXISTING"]}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}, "title": "ContactConversionRequest"}, "ContactCreateRequest": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "company": {"type": "integer", "format": "int64"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "facebook": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "linkedin": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "stakeholder": {"type": "boolean"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "ContactCreateRequest"}, "ContactImportRequest": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "company": {"type": "integer", "format": "int64"}, "companyName": {"type": "string"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "facebook": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "linkedin": {"type": "string"}, "ownerEmail": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "stakeholder": {"type": "boolean"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "ContactImportRequest"}, "ContactImportRequestData": {"type": "object", "properties": {"jobId": {"type": "integer", "format": "int64"}, "contact": {"originalRef": "ContactImportRequest", "$ref": "#/definitions/ContactImportRequest"}, "importStrategy": {"originalRef": "ImportStrategy", "$ref": "#/definitions/ImportStrategy"}}, "title": "ContactImportRequestData"}, "ContactPatchRequest": {"type": "object", "properties": {"contact": {"originalRef": "ContactUpdateRequestV2", "$ref": "#/definitions/ContactUpdateRequestV2"}, "operation": {"originalRef": "Operation", "$ref": "#/definitions/Operation"}, "executeWorkflow": {"type": "boolean"}, "sendNotification": {"type": "boolean"}}, "title": "ContactPatchRequest"}, "ContactResponse": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "company": {"type": "integer", "format": "int64"}, "convertedAt": {"type": "string", "format": "date-time"}, "convertedBy": {"type": "integer", "format": "int64"}, "country": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "createdViaId": {"type": "string"}, "createdViaName": {"type": "string"}, "createdViaType": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "facebook": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "importedBy": {"type": "integer", "format": "int64"}, "lastName": {"type": "string"}, "linkedin": {"type": "string"}, "metaData": {"type": "object"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "stakeholder": {"type": "boolean"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}, "updatedViaId": {"type": "string"}, "updatedViaName": {"type": "string"}, "updatedViaType": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "ContactResponse"}, "ContactSummary": {"type": "object", "properties": {"firstName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastName": {"type": "string"}}, "title": "ContactSummary"}, "ContactUpdateRequest": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "company": {"type": "integer", "format": "int64"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "facebook": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "linkedin": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "stakeholder": {"type": "boolean"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "ContactUpdateRequest"}, "ContactUpdateRequestV2": {"type": "object", "properties": {"actualClosureDate": {"type": "string", "format": "date-time"}, "address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "company": {"originalRef": "IdName", "$ref": "#/definitions/IdName"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"originalRef": "Email", "$ref": "#/definitions/Email"}, "facebook": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "linkedin": {"type": "string"}, "ownerId": {"originalRef": "IdName", "$ref": "#/definitions/IdName"}, "phoneNumbers": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "stakeholder": {"type": "boolean"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "ContactUpdateRequestV2"}, "ConversionAssociationDTO": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "entityType": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}}, "title": "ConversionAssociationDTO"}, "ConversionRequest": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["CREATE", "EXISTING"]}, "details": {"type": "object", "additionalProperties": {"type": "object"}}}, "title": "ConversionRequest"}, "CreatePipelineRequest": {"type": "object", "required": ["entityType", "name"], "properties": {"entityType": {"type": "string", "example": "LEAD", "description": "Entity type applicable for the list", "enum": ["LEAD", "DEAL"]}, "name": {"type": "string", "example": "Baner Properties", "description": "Unique name for this pipeline"}}, "title": "CreatePipelineRequest"}, "CreatePipelineStageRequest": {"type": "object", "required": ["forecastingType", "name", "winLikelihood"], "properties": {"name": {"type": "string", "example": "Site visit", "description": "Unique name for this stage"}, "description": {"type": "string", "example": "Site visit stage", "description": "Description for this stage"}, "forecastingType": {"type": "string", "example": "OPEN", "description": "Stage type", "enum": ["OPEN", "CLOSED_WON", "CLOSED_UNQUALIFIED", "CLOSED_LOST", "CLOSED"]}, "position": {"type": "integer", "format": "int32", "example": 1, "description": "Position in the attached pipeline"}, "winLikelihood": {"type": "integer", "format": "int32", "example": 50, "description": "Win likelihood percentage between 0-100%"}}, "title": "CreatePipelineStageRequest"}, "CreateShareRuleRequest": {"type": "object", "required": ["actions", "fromId", "fromType", "name", "toId", "toType"], "properties": {"name": {"type": "string", "example": "Sharing my leads with my friend", "description": "name for this share rule"}, "description": {"type": "string", "example": "Sharing my leads with my friend", "description": "Description for this share rule"}, "fromType": {"type": "string", "example": "USER", "description": "Entity type (sharing access)", "enum": ["USER", "TEAM"]}, "fromId": {"type": "integer", "format": "int64", "example": 1, "description": "Entity id (sharing access)"}, "toType": {"type": "string", "example": "USER", "description": "Entity type (getting shared access)", "enum": ["USER", "TEAM"]}, "toId": {"type": "integer", "format": "int64", "example": 1, "description": "Entity id (getting shared access)"}, "childEntities": {"type": "array", "example": ["TASK", "NOTE"], "description": "Valid child entities of shared entity", "items": {"type": "string", "enum": ["TASK", "NOTE", "MEETING"]}}, "actions": {"example": {"read": true}, "description": "Permission set which is being shared", "originalRef": "Action", "$ref": "#/definitions/Action"}}, "title": "CreateShareRuleRequest"}, "DefaultConfig": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}}}, "title": "DefaultConfig"}, "DeleteDetail": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "ownerId": {"type": "integer", "format": "int64"}, "result": {"type": "string", "enum": ["SUCCESS", "ERROR"]}, "tenantId": {"type": "integer", "format": "int64"}}, "title": "DeleteDetail"}, "DuplicateEntityDTO": {"type": "object", "properties": {"firstName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastName": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}}, "title": "DuplicateEntityDTO"}, "DuplicateRecordDTO": {"type": "object", "properties": {"hasDuplicate": {"type": "boolean"}, "message": {"originalRef": "ErrorResource", "$ref": "#/definitions/ErrorResource"}, "metaData": {"type": "object"}, "records": {"type": "array", "items": {"originalRef": "DuplicateEntityDTO", "$ref": "#/definitions/DuplicateEntityDTO"}}}, "title": "DuplicateRecordDTO"}, "Email": {"type": "object", "properties": {"primary": {"type": "boolean"}, "type": {"type": "string", "enum": ["OFFICE", "PERSONAL"]}, "value": {"type": "string"}}, "title": "Email"}, "ErrorResource": {"type": "object", "properties": {"code": {"type": "string"}, "errorDetails": {"type": "array", "items": {"originalRef": "FieldErrorResource", "$ref": "#/definitions/FieldErrorResource"}}, "message": {"type": "string"}}, "title": "ErrorResource"}, "FieldErrorResource": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}}, "title": "FieldErrorResource"}, "FieldParams": {"type": "object", "properties": {"allowFutureDate": {"type": "boolean"}, "allowPastDate": {"type": "boolean"}}, "title": "FieldParams"}, "FieldValue": {"type": "object", "properties": {"disabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "internalName": {"type": "string"}, "name": {"type": "string"}, "selected": {"type": "boolean"}}, "title": "FieldValue"}, "FormField": {"type": "object", "properties": {"active": {"type": "boolean"}, "allowedValues": {"type": "array", "items": {"originalRef": "FieldValue", "$ref": "#/definitions/FieldValue"}}, "columnNumber": {"type": "integer", "format": "int32"}, "config": {"originalRef": "FieldParams", "$ref": "#/definitions/FieldParams"}, "displayName": {"type": "string"}, "helperText": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "internal": {"type": "boolean"}, "isHidden": {"type": "boolean"}, "isRequired": {"type": "boolean"}, "maxLength": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "placeholder": {"type": "string"}, "rowNumber": {"type": "integer", "format": "int32"}, "standard": {"type": "boolean"}, "tenantId": {"type": "integer", "format": "int64"}, "type": {"type": "string"}}, "title": "FormField"}, "IdName": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "IdName"}, "ImportStrategy": {"type": "object", "properties": {"type": {"type": "string", "enum": ["NONE", "IGNORE_DUPLICATE", "MERGE_DUPLICATE_BY_REPLACING_DATA", "MERGE_DUPLICATE_WITH_MISSING_DATA"]}}, "title": "ImportStrategy"}, "Item": {"type": "object", "properties": {"active": {"type": "boolean"}, "description": {"type": "string"}, "displayName": {"type": "string"}, "entity": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, "filterable": {"type": "boolean"}, "greaterThan": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "internal": {"type": "boolean"}, "internalName": {"type": "string"}, "length": {"type": "integer", "format": "int32"}, "lessThan": {"type": "string"}, "lookupUrl": {"type": "string"}, "multiValue": {"type": "boolean"}, "pickLists": {"type": "array", "items": {"type": "object"}}, "readOnly": {"type": "boolean"}, "regex": {"type": "string"}, "required": {"type": "boolean"}, "sectionId": {"type": "integer", "format": "int64"}, "showDefaultOptions": {"type": "boolean"}, "sortable": {"type": "boolean"}, "standard": {"type": "boolean"}, "type": {"type": "string", "enum": ["TEXT_FIELD", "SINGLE_LINE_TEXT", "PARAGRAPH_TEXT", "RICH_TEXT", "NUMBER", "CHECKBOX", "RADIO_BUTTON", "PICK_LIST", "MULTI_PICKLIST", "DATE_PICKER", "TIME_PICKER", "DATETIME_PICKER", "URL", "EMAIL", "PRODUCT", "PHONE", "LOOK_UP", "ENTITY_LOOKUP", "AUTO_INCREMENT", "TOGGLE", "FORECASTING_TYPE", "PIPELINE", "LATEST_NOTES", "MONEY", "PIPELINE_STAGE", "PIPELINE_STAGE_REASON", "DISCOUNT"]}, "unique": {"type": "boolean"}}, "title": "<PERSON><PERSON>"}, "JsonRule": {"type": "object", "properties": {"condition": {"type": "string"}, "data": {"type": "object"}, "field": {"type": "string"}, "id": {"type": "string"}, "input": {"type": "string"}, "not": {"type": "boolean"}, "operator": {"type": "string"}, "rules": {"type": "array", "items": {"originalRef": "JsonRule", "$ref": "#/definitions/JsonRule"}}, "type": {"type": "string"}, "value": {"type": "object"}}, "title": "JsonRule"}, "Layout": {"type": "object", "properties": {"layoutItems": {"type": "array", "items": {"originalRef": "LayoutItem", "$ref": "#/definitions/LayoutItem"}}}, "title": "Layout"}, "LayoutItem": {"type": "object", "properties": {"column": {"type": "integer", "format": "int32"}, "heading": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "item": {"originalRef": "<PERSON><PERSON>", "$ref": "#/definitions/Item"}, "layoutItems": {"type": "array", "items": {"originalRef": "LayoutItem", "$ref": "#/definitions/LayoutItem"}}, "row": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["SECTION", "FIELD"]}, "width": {"type": "integer", "format": "int32"}}, "title": "LayoutItem"}, "LeadBulkReassignRequest": {"type": "object", "properties": {"leadIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "ownerId": {"type": "integer", "format": "int64"}, "executeWorkflow": {"type": "boolean"}, "sendEmail": {"type": "boolean"}}, "title": "LeadBulkReassignRequest"}, "LeadCaptureForm": {"type": "object", "properties": {"apiKey": {"type": "string"}, "createdAt": {"type": "string", "example": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"}, "createdBy": {"type": "integer", "format": "int64"}, "disclaimer": {"type": "string"}, "fields": {"type": "array", "items": {"originalRef": "FormField", "$ref": "#/definitions/FormField"}}, "greeting": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "isActive": {"type": "boolean"}, "name": {"type": "string"}, "onSubmitMessage": {"type": "string"}, "submitButtonText": {"type": "string"}, "tenantId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["WEBSITE"]}, "updatedAt": {"type": "string", "example": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"}, "updatedBy": {"type": "integer", "format": "int64"}, "uploadSize": {"type": "integer", "format": "int64"}}, "title": "LeadCaptureForm"}, "LeadCaptureFormCreatedResponse": {"type": "object", "properties": {"apiKey": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "isActive": {"type": "boolean"}}, "title": "LeadCaptureFormCreatedResponse"}, "LeadCaptureFormDetails": {"type": "object", "properties": {"apiKey": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "disclaimer": {"type": "string"}, "fields": {"type": "array", "items": {"originalRef": "LeadCaptureFormField", "$ref": "#/definitions/LeadCaptureFormField"}}, "greeting": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "isActive": {"type": "boolean"}, "metaData": {"type": "object"}, "name": {"type": "string"}, "onSubmitMessage": {"type": "string"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "submitButtonText": {"type": "string"}, "tenantId": {"type": "integer", "format": "int64"}, "totalFields": {"type": "integer", "format": "int32"}, "type": {"originalRef": "ValueLabel", "$ref": "#/definitions/ValueLabel"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}}, "title": "LeadCaptureFormDetails"}, "LeadCaptureFormField": {"type": "object", "properties": {"displayName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "isRequired": {"type": "boolean"}, "isHidden": {"type": "boolean"}, "rowNumber": {"type": "integer", "format": "int32"}, "columnNumber": {"type": "integer", "format": "int32"}, "displayNumber": {"type": "string"}, "placeholder": {"type": "string"}, "helperText": {"type": "string"}, "fieldType": {"type": "string"}, "allowedValues": {"type": "array", "items": {"originalRef": "FieldValue", "$ref": "#/definitions/FieldValue"}}, "config": {"originalRef": "FieldParams", "$ref": "#/definitions/FieldParams"}}, "title": "LeadCaptureFormField"}, "LeadCaptureFormRequest": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "greeting": {"type": "string"}, "onSubmitMessage": {"type": "string"}, "submitButtonText": {"type": "string"}, "disclaimer": {"type": "string"}, "fields": {"type": "array", "items": {"originalRef": "LeadCaptureFormField", "$ref": "#/definitions/LeadCaptureFormField"}}}, "title": "LeadCaptureFormRequest"}, "LeadConversionRequest": {"type": "object", "properties": {"deal": {"originalRef": "ConversionRequest", "$ref": "#/definitions/ConversionRequest"}, "contact": {"originalRef": "ContactConversionRequest", "$ref": "#/definitions/ContactConversionRequest"}, "company": {"originalRef": "ConversionRequest", "$ref": "#/definitions/ConversionRequest"}}, "title": "LeadConversionRequest"}, "LeadConversionResponse": {"type": "object", "properties": {"conversionDetails": {"type": "array", "items": {"originalRef": "ConversionAssociationDTO", "$ref": "#/definitions/ConversionAssociationDTO"}}, "convertedAt": {"type": "string", "format": "date-time"}, "convertedBy": {"type": "integer", "format": "int64"}, "metaData": {"type": "object"}, "ownerId": {"type": "integer", "format": "int64"}}, "title": "LeadConversionResponse"}, "LeadCount": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64"}, "ownerId": {"type": "integer", "format": "int64"}}, "title": "LeadCount"}, "LeadImportRequest": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "companyAddress": {"type": "string"}, "companyAnnualRevenue": {"type": "number", "format": "double"}, "companyBusinessType": {"type": "string"}, "companyCity": {"type": "string"}, "companyCountry": {"type": "string"}, "companyEmployees": {"type": "integer", "format": "int32"}, "companyIndustry": {"type": "string"}, "companyName": {"type": "string"}, "companyPhones": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "companyState": {"type": "string"}, "companyWebsite": {"type": "string"}, "companyZipcode": {"type": "string"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "expectedClosureOn": {"type": "string", "format": "date-time"}, "facebook": {"type": "string"}, "firstCampaign": {"type": "string"}, "firstMedium": {"type": "string"}, "firstName": {"type": "string"}, "firstRespondedAt": {"type": "string", "format": "date-time"}, "firstSource": {"type": "string"}, "firstSubSource": {"type": "string"}, "lastCampaign": {"type": "string"}, "lastMedium": {"type": "string"}, "lastName": {"type": "string"}, "lastRespondedAt": {"type": "string", "format": "date-time"}, "lastSource": {"type": "string"}, "lastSubSource": {"type": "string"}, "linkedIn": {"type": "string"}, "ownerEmail": {"type": "string"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "pipeline": {"originalRef": "Pipeline", "$ref": "#/definitions/Pipeline"}, "pipelineStageReason": {"type": "string"}, "productName": {"type": "string"}, "requirementBudget": {"type": "number", "format": "double"}, "requirementCurrency": {"type": "string"}, "requirementName": {"type": "string"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "title": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "LeadImportRequest"}, "LeadImportRequestData": {"type": "object", "properties": {"jobId": {"type": "integer", "format": "int64"}, "leadToImport": {"originalRef": "LeadImportRequest", "$ref": "#/definitions/LeadImportRequest"}, "lead": {"originalRef": "LeadImportRequest", "$ref": "#/definitions/LeadImportRequest"}, "importStrategy": {"originalRef": "ImportStrategy", "$ref": "#/definitions/ImportStrategy"}}, "title": "LeadImportRequestData"}, "LeadPatchRequest": {"type": "object", "properties": {"lead": {"originalRef": "LeadUpdateRequestV2", "$ref": "#/definitions/LeadUpdateRequestV2"}, "operation": {"originalRef": "Operation", "$ref": "#/definitions/Operation"}, "executeWorkflow": {"type": "boolean"}, "sendNotification": {"type": "boolean"}}, "title": "LeadPatchRequest"}, "LeadProductResponse": {"type": "object", "properties": {"leadIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "LeadProductResponse"}, "LeadRequest": {"type": "object", "properties": {"address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "companyAddress": {"type": "string"}, "companyAnnualRevenue": {"type": "number", "format": "double"}, "companyBusinessType": {"type": "string"}, "companyCity": {"type": "string"}, "companyCountry": {"type": "string"}, "companyEmployees": {"type": "integer", "format": "int32"}, "companyIndustry": {"type": "string"}, "companyName": {"type": "string"}, "companyPhones": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "companyState": {"type": "string"}, "companyWebsite": {"type": "string"}, "companyZipcode": {"type": "string"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "expectedClosureOn": {"type": "string", "format": "date-time"}, "facebook": {"type": "string"}, "firstCampaign": {"type": "string"}, "firstMedium": {"type": "string"}, "firstName": {"type": "string"}, "firstRespondedAt": {"type": "string", "format": "date-time"}, "firstSource": {"type": "string"}, "firstSubSource": {"type": "string"}, "lastCampaign": {"type": "string"}, "lastMedium": {"type": "string"}, "lastName": {"type": "string"}, "lastRespondedAt": {"type": "string", "format": "date-time"}, "lastSource": {"type": "string"}, "lastSubSource": {"type": "string"}, "linkedIn": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "pipeline": {"originalRef": "Pipeline", "$ref": "#/definitions/Pipeline"}, "pipelineStageReason": {"type": "string"}, "products": {"type": "array", "items": {"originalRef": "ProductDTO", "$ref": "#/definitions/ProductDTO"}}, "requirementBudget": {"type": "number", "format": "double"}, "requirementCurrency": {"type": "string"}, "requirementName": {"type": "string"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "title": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "LeadRequest"}, "LeadResponse": {"type": "object", "properties": {"actualClosureDate": {"type": "string", "format": "date-time"}, "address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "companyAddress": {"type": "string"}, "companyAnnualRevenue": {"type": "number", "format": "double"}, "companyBusinessType": {"type": "string"}, "companyCity": {"type": "string"}, "companyCountry": {"type": "string"}, "companyEmployees": {"type": "integer", "format": "int32"}, "companyIndustry": {"type": "string"}, "companyName": {"type": "string"}, "companyPhones": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "companyState": {"type": "string"}, "companyWebsite": {"type": "string"}, "companyZipcode": {"type": "string"}, "conversionDetails": {"type": "array", "items": {"originalRef": "ConversionAssociationDTO", "$ref": "#/definitions/ConversionAssociationDTO"}}, "convertedAt": {"type": "string", "format": "date-time"}, "convertedBy": {"type": "integer", "format": "int64"}, "country": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "createdViaId": {"type": "string"}, "createdViaName": {"type": "string"}, "createdViaType": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emailStatus": {"type": "string"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "expectedClosureOn": {"type": "string", "format": "date-time"}, "facebook": {"type": "string"}, "firstCampaign": {"type": "string"}, "firstMedium": {"type": "string"}, "firstName": {"type": "string"}, "firstRespondedAt": {"type": "string", "format": "date-time"}, "firstSource": {"type": "string"}, "firstSubSource": {"type": "string"}, "forecastingType": {"type": "string", "enum": ["OPEN", "CLOSED_WON", "CLOSED_UNQUALIFIED", "CLOSED_LOST", "CLOSED"]}, "id": {"type": "integer", "format": "int64"}, "importedBy": {"type": "integer", "format": "int64"}, "ip": {"type": "string"}, "lastAttemptedContactAt": {"type": "string", "format": "date-time"}, "lastCampaign": {"type": "string"}, "lastContactedAt": {"type": "string", "format": "date-time"}, "lastContactedMode": {"type": "string"}, "lastMedium": {"type": "string"}, "lastName": {"type": "string"}, "lastNote": {"type": "string"}, "lastRespondedAt": {"type": "string", "format": "date-time"}, "lastSeenAt": {"type": "string", "format": "date-time"}, "lastSource": {"type": "string"}, "lastSubSource": {"type": "string"}, "lastSuccessfulContactAt": {"type": "string", "format": "date-time"}, "latestActivityCreatedAt": {"type": "string", "format": "date-time"}, "linkedIn": {"type": "string"}, "metaData": {"type": "object"}, "nextActivityAt": {"type": "string", "format": "date-time"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "phoneStatus": {"type": "string"}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "pipeline": {"originalRef": "Pipeline", "$ref": "#/definitions/Pipeline"}, "pipelineStageReason": {"type": "string"}, "products": {"type": "array", "items": {"originalRef": "ProductDTO", "$ref": "#/definitions/ProductDTO"}}, "quality": {"type": "string"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "requirementBudget": {"type": "number", "format": "double"}, "requirementCurrency": {"type": "string"}, "requirementName": {"type": "string"}, "salutation": {"type": "integer", "format": "int64"}, "score": {"type": "integer", "format": "int32"}, "source": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "tags": {"type": "string"}, "timezone": {"type": "string"}, "twitter": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}, "updatedViaId": {"type": "string"}, "updatedViaName": {"type": "string"}, "updatedViaType": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "LeadResponse"}, "LeadSummary": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "LeadSummary"}, "LeadUpdateRequest": {"type": "object", "properties": {"actualClosureDate": {"type": "string", "format": "date-time"}, "address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "companyAddress": {"type": "string"}, "companyAnnualRevenue": {"type": "number", "format": "double"}, "companyBusinessType": {"type": "string"}, "companyCity": {"type": "string"}, "companyCountry": {"type": "string"}, "companyEmployees": {"type": "integer", "format": "int32"}, "companyIndustry": {"type": "string"}, "companyName": {"type": "string"}, "companyPhones": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "companyState": {"type": "string"}, "companyWebsite": {"type": "string"}, "companyZipcode": {"type": "string"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"type": "array", "items": {"originalRef": "Email", "$ref": "#/definitions/Email"}}, "expectedClosureOn": {"type": "string", "format": "date-time"}, "facebook": {"type": "string"}, "firstCampaign": {"type": "string"}, "firstMedium": {"type": "string"}, "firstName": {"type": "string"}, "firstRespondedAt": {"type": "string", "format": "date-time"}, "firstSource": {"type": "string"}, "firstSubSource": {"type": "string"}, "lastCampaign": {"type": "string"}, "lastMedium": {"type": "string"}, "lastName": {"type": "string"}, "lastRespondedAt": {"type": "string", "format": "date-time"}, "lastSource": {"type": "string"}, "lastSubSource": {"type": "string"}, "linkedIn": {"type": "string"}, "ownerId": {"type": "integer", "format": "int64"}, "phoneNumbers": {"type": "array", "items": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "pipeline": {"originalRef": "Pipeline", "$ref": "#/definitions/Pipeline"}, "pipelineStageReason": {"type": "string"}, "products": {"type": "array", "items": {"originalRef": "ProductDTO", "$ref": "#/definitions/ProductDTO"}}, "requirementBudget": {"type": "number", "format": "double"}, "requirementCurrency": {"type": "string"}, "requirementName": {"type": "string"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "title": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "LeadUpdateRequest"}, "LeadUpdateRequestV2": {"type": "object", "properties": {"actualClosureDate": {"type": "string", "format": "date-time"}, "address": {"type": "string"}, "campaign": {"type": "integer", "format": "int64"}, "city": {"type": "string"}, "companyAddress": {"type": "string"}, "companyAnnualRevenue": {"type": "number", "format": "double"}, "companyBusinessType": {"type": "string"}, "companyCity": {"type": "string"}, "companyCountry": {"type": "string"}, "companyEmployees": {"type": "integer", "format": "int32"}, "companyIndustry": {"type": "string"}, "companyName": {"type": "string"}, "companyPhones": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}, "companyState": {"type": "string"}, "companyWebsite": {"type": "string"}, "companyZipcode": {"type": "string"}, "country": {"type": "string"}, "customFieldValues": {"type": "object"}, "department": {"type": "string"}, "designation": {"type": "string"}, "dnd": {"type": "boolean"}, "emails": {"originalRef": "Email", "$ref": "#/definitions/Email"}, "expectedClosureOn": {"type": "string", "format": "date-time"}, "facebook": {"type": "string"}, "firstCampaign": {"type": "string"}, "firstMedium": {"type": "string"}, "firstName": {"type": "string"}, "firstRespondedAt": {"type": "string", "format": "date-time"}, "firstSource": {"type": "string"}, "firstSubSource": {"type": "string"}, "lastCampaign": {"type": "string"}, "lastMedium": {"type": "string"}, "lastName": {"type": "string"}, "lastRespondedAt": {"type": "string", "format": "date-time"}, "lastSource": {"type": "string"}, "lastSubSource": {"type": "string"}, "linkedIn": {"type": "string"}, "ownerId": {"originalRef": "IdName", "$ref": "#/definitions/IdName"}, "phoneNumbers": {"originalRef": "PhoneNumber", "$ref": "#/definitions/PhoneNumber"}, "photoUrls": {"type": "array", "items": {"type": "string"}}, "pipeline": {"originalRef": "Pipeline", "$ref": "#/definitions/Pipeline"}, "pipelineStageReason": {"type": "string"}, "products": {"originalRef": "Product", "$ref": "#/definitions/Product"}, "requirementBudget": {"type": "number", "format": "double"}, "requirementCurrency": {"type": "string"}, "requirementName": {"type": "string"}, "salutation": {"type": "integer", "format": "int64"}, "source": {"type": "integer", "format": "int64"}, "state": {"type": "string"}, "subSource": {"type": "string"}, "timezone": {"type": "string"}, "title": {"type": "string"}, "twitter": {"type": "string"}, "utmCampaign": {"type": "string"}, "utmContent": {"type": "string"}, "utmMedium": {"type": "string"}, "utmSource": {"type": "string"}, "utmTerm": {"type": "string"}, "zipcode": {"type": "string"}}, "title": "LeadUpdateRequestV2"}, "ListLayoutResponse": {"type": "object", "properties": {"defaultConfig": {"originalRef": "DefaultConfig", "$ref": "#/definitions/DefaultConfig"}, "leftNav": {"type": "boolean"}, "pageConfig": {"originalRef": "PageConfig", "$ref": "#/definitions/PageConfig"}}, "title": "ListLayoutResponse"}, "LookUp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "LookUp"}, "Lookup": {"type": "object", "properties": {"entity": {"type": "string"}, "lookupUrl": {"type": "string"}}, "title": "Lookup"}, "Operation": {"type": "object", "properties": {"executeWorkflow": {"type": "boolean"}, "sendNotification": {"type": "boolean"}}, "title": "Operation"}, "PageConfig": {"type": "object", "properties": {"actionConfig": {"originalRef": "ActionConfig", "$ref": "#/definitions/ActionConfig"}, "tableConfig": {"originalRef": "TableConfig", "$ref": "#/definitions/TableConfig"}}, "title": "PageConfig"}, "PageOfLeadResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"originalRef": "LeadResponse", "$ref": "#/definitions/LeadResponse"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sort": {"originalRef": "Sort", "$ref": "#/definitions/Sort"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}, "title": "PageOfLeadResponse"}, "PageOfLookUp": {"type": "object", "properties": {"content": {"type": "array", "items": {"originalRef": "LookUp", "$ref": "#/definitions/LookUp"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sort": {"originalRef": "Sort", "$ref": "#/definitions/Sort"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}, "title": "PageOfLookUp"}, "PageOfPipelineResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"originalRef": "PipelineResponse", "$ref": "#/definitions/PipelineResponse"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sort": {"originalRef": "Sort", "$ref": "#/definitions/Sort"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}, "title": "PageOfPipelineResponse"}, "PageOfSearchResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"originalRef": "SearchResponse", "$ref": "#/definitions/SearchResponse"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sort": {"originalRef": "Sort", "$ref": "#/definitions/Sort"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}, "title": "PageOfSearchResponse"}, "PhoneNumber": {"type": "object", "properties": {"code": {"type": "string"}, "dialCode": {"type": "string"}, "primary": {"type": "boolean"}, "type": {"type": "string", "enum": ["MOBILE", "WORK", "HOME", "PERSONAL"]}, "value": {"type": "string"}}, "title": "PhoneNumber"}, "Picklist": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "picklistValues": {"type": "array", "items": {"originalRef": "PicklistValue", "$ref": "#/definitions/PicklistValue"}}}, "title": "Picklist"}, "PicklistValue": {"type": "object", "properties": {"displayName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "PicklistValue"}, "Pipeline": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "stage": {"originalRef": "PipelineStage", "$ref": "#/definitions/PipelineStage"}}, "title": "Pipeline"}, "PipelineResponse": {"type": "object", "properties": {"active": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "entityType": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, "id": {"type": "integer", "format": "int64"}, "lostReasons": {"type": "array", "items": {"type": "string"}}, "metaData": {"type": "object"}, "name": {"type": "string"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "stages": {"type": "array", "items": {"originalRef": "PipelineStageResponse", "$ref": "#/definitions/PipelineStageResponse"}}, "unqualifiedReasons": {"type": "array", "items": {"type": "string"}}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}}, "title": "PipelineResponse"}, "PipelineStage": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "deleted": {"type": "boolean"}, "description": {"type": "string"}, "forecastingType": {"type": "string", "enum": ["OPEN", "CLOSED_WON", "CLOSED_UNQUALIFIED", "CLOSED_LOST", "CLOSED"]}, "id": {"type": "integer", "format": "int64"}, "metaData": {"type": "object"}, "name": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "tenantId": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int32"}, "winLikelihood": {"type": "integer", "format": "int32"}}, "title": "PipelineStage"}, "PipelineStageResponse": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "forecastingType": {"type": "string", "enum": ["OPEN", "CLOSED_WON", "CLOSED_UNQUALIFIED", "CLOSED_LOST", "CLOSED"]}, "id": {"type": "integer", "format": "int64"}, "metaData": {"type": "object"}, "name": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}, "winLikelihood": {"type": "integer", "format": "int32"}}, "title": "PipelineStageResponse"}, "Product": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "tenantId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "Product"}, "ProductDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "title": "ProductDTO"}, "ReassignDetail": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "newOwnerId": {"type": "integer", "format": "int64"}, "oldOwnerId": {"type": "integer", "format": "int64"}, "result": {"type": "string", "enum": ["SUCCESS", "ERROR"]}}, "title": "ReassignDetail"}, "ResponseEntity": {"type": "object", "properties": {"body": {"type": "object"}, "statusCode": {"type": "string", "enum": ["100", "101", "102", "103", "200", "201", "202", "203", "204", "205", "206", "207", "208", "226", "300", "301", "302", "303", "304", "305", "307", "308", "400", "401", "402", "403", "404", "405", "406", "407", "408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "426", "428", "429", "431", "451", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseEntity"}, "SearchRequest": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}}, "jsonRule": {"originalRef": "JsonRule", "$ref": "#/definitions/JsonRule"}}, "title": "SearchRequest"}, "SearchResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"type": "object"}}, "fields": {"type": "array", "items": {"type": "object"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "metaData": {"type": "object"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sort": {"originalRef": "Sort", "$ref": "#/definitions/Sort"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}, "title": "SearchResponse"}, "ShareRuleResponse": {"type": "object", "properties": {"actions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "childEntities": {"type": "array", "items": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "entityId": {"type": "integer", "format": "int64"}, "entityShareRuleId": {"type": "integer", "format": "int64"}, "entityType": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, "fromId": {"type": "integer", "format": "int64"}, "fromType": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, "id": {"type": "integer", "format": "int64"}, "metaData": {"type": "object"}, "name": {"type": "string"}, "recordActions": {"originalRef": "Action", "$ref": "#/definitions/Action"}, "shareAllRecords": {"type": "boolean"}, "toId": {"type": "integer", "format": "int64"}, "toType": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}, "updatedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "integer", "format": "int64"}}, "title": "ShareRuleResponse"}, "Sort": {"type": "object", "title": "Sort"}, "TableColumn": {"type": "object", "properties": {"active": {"type": "boolean"}, "fieldType": {"type": "string", "enum": ["TEXT_FIELD", "SINGLE_LINE_TEXT", "PARAGRAPH_TEXT", "RICH_TEXT", "NUMBER", "CHECKBOX", "RADIO_BUTTON", "PICK_LIST", "MULTI_PICKLIST", "DATE_PICKER", "TIME_PICKER", "DATETIME_PICKER", "URL", "EMAIL", "PRODUCT", "PHONE", "LOOK_UP", "ENTITY_LOOKUP", "AUTO_INCREMENT", "TOGGLE", "FORECASTING_TYPE", "PIPELINE", "PIPELINE_STAGE", "PIPELINE_STAGE_REASON", "LATEST_NOTES"]}, "header": {"type": "string"}, "id": {"type": "string"}, "isFilterable": {"type": "boolean"}, "isInternal": {"type": "boolean"}, "isSortable": {"type": "boolean"}, "isStandard": {"type": "boolean"}, "lookup": {"originalRef": "Lookup", "$ref": "#/definitions/Lookup"}, "multiValue": {"type": "boolean"}, "picklist": {"originalRef": "Picklist", "$ref": "#/definitions/Picklist"}, "relatedFieldIds": {"type": "array", "items": {"type": "string"}}, "relatedFieldTypes": {"type": "array", "items": {"type": "string", "enum": ["TEXT_FIELD", "SINGLE_LINE_TEXT", "PARAGRAPH_TEXT", "RICH_TEXT", "NUMBER", "CHECKBOX", "RADIO_BUTTON", "PICK_LIST", "MULTI_PICKLIST", "DATE_PICKER", "TIME_PICKER", "DATETIME_PICKER", "URL", "EMAIL", "PRODUCT", "PHONE", "LOOK_UP", "ENTITY_LOOKUP", "AUTO_INCREMENT", "TOGGLE", "FORECASTING_TYPE", "PIPELINE", "PIPELINE_STAGE", "PIPELINE_STAGE_REASON", "LATEST_NOTES"]}}, "showDefaultOptions": {"type": "boolean"}, "values": {"type": "object"}}, "title": "TableColumn"}, "TableConfig": {"type": "object", "properties": {"clickActionUrl": {"type": "string"}, "columns": {"type": "array", "items": {"originalRef": "TableColumn", "$ref": "#/definitions/TableColumn"}}, "fetchURL": {"type": "string"}, "recordClickAction": {"type": "string", "enum": ["VIEW", "EDIT"]}, "searchService": {"type": "string"}}, "title": "TableConfig"}, "UpdateOwnerRequest": {"type": "object", "properties": {"childEntities": {"type": "array", "items": {"type": "string", "enum": ["NOTE", "TASK", "MEETING", "USER", "TEAM", "AVAILABILITY", "PROFILE", "PROFILE_PERMISSION", "PROFILE_PRODUCT", "PROFILE_MODULE", "LEAD", "DEAL", "CONTACT", "COMPANY", "PIPELINE", "PIPELINE_STAGE", "TENANT_CONFIGURATION", "TENANT_PLAN", "CONVERSION_MAPPING", "ENTITY_DEF", "FIELD", "LAYOUT", "LAYOUT_FIELD", "LAYOUT_ITEM", "SECTION", "SHARE_RULE", "PRODUCT", "REPORT", "MARKETING", "LEAD_CAPTURE_FORM", "WORKFLOW", "COMMUNICATION", "EMAIL"]}}, "ownerId": {"type": "integer", "format": "int64"}}, "title": "UpdateOwnerRequest"}, "UpdatePipelineRequest": {"type": "object", "properties": {"stages": {"type": "array", "items": {"originalRef": "PipelineStage", "$ref": "#/definitions/PipelineStage"}}, "entityType": {"type": "string", "example": "LEAD", "description": "Entity type applicable for the list", "enum": ["LEAD", "DEAL"]}, "unqualifiedReasons": {"type": "array", "example": ["Wrong number", "Did not pick phone"], "description": "User entered values which to be used as picklist in pipeline execution", "items": {"type": "string"}}, "lostReasons": {"type": "array", "example": ["Low budget", "Not interested"], "description": "User entered values which to be used as picklist in pipeline execution", "items": {"type": "string"}}, "name": {"type": "string", "example": "Baner Properties", "description": "Unique name for this pipeline"}}, "title": "UpdatePipelineRequest"}, "UpdatePipelineStageRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Site visit", "description": "Unique name for this stage"}, "description": {"type": "string", "example": "Site visit stage", "description": "Description for this stage"}, "forecastingType": {"type": "string", "example": "OPEN", "description": "Stage type", "enum": ["OPEN", "CLOSED_WON", "CLOSED_UNQUALIFIED", "CLOSED_LOST", "CLOSED"]}, "position": {"type": "integer", "format": "int32", "example": 1, "description": "Position in the attached pipeline"}, "winLikelihood": {"type": "integer", "format": "int32", "example": 50, "description": "Win likelihood percentage between 0-100%"}}, "title": "UpdatePipelineStageRequest"}, "UpdateShareRuleRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "Sharing my leads with my friend", "description": "name for this share rule"}, "description": {"type": "string", "example": "Sharing my leads with my friend", "description": "Description for this share rule"}, "fromType": {"type": "string", "example": "USER", "description": "Entity type (sharing access)", "enum": ["USER", "TEAM"]}, "fromId": {"type": "integer", "format": "int64", "example": 1, "description": "Entity id (sharing access)"}, "toType": {"type": "string", "example": "USER", "description": "Entity type (getting shared access)", "enum": ["USER", "TEAM"]}, "toId": {"type": "integer", "format": "int64", "example": 1, "description": "Entity id (getting shared access)"}, "childEntities": {"type": "array", "example": ["TASK", "NOTE"], "description": "Valid child entities of shared entity", "items": {"type": "string", "enum": ["TASK", "NOTE", "MEETING"]}}, "actions": {"example": {"read": true}, "description": "Permission set which is being shared", "originalRef": "Action", "$ref": "#/definitions/Action"}}, "title": "UpdateShareRuleRequest"}, "ValueLabel": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}}, "title": "ValueLabel"}}}