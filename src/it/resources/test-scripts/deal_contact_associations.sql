/*
tenantId=200,
ownerid=8, 10
company=101,102,103
*/

DELETE FROM contact_associated_deals where contact_id in(1001,1002);
DELETE FROM contact where id in(1001,1002,1003);
DELETE FROM users;

INSERT INTO contact
(id,tenant_id, owner_id, company, first_name, last_name, stakeholder,created_by, created_at, updated_by, updated_at,deleted, version,dnd)
    OVERRIDING SYSTEM VALUE
VALUES
(1001,200,10,101,'tony','stark',true,11, '2019-10-01 05:43:45.717000', 11, '2019-10-01 05:43:45.717000',false,0,false),
(1002,200,10,101,'hulk',null,false, 11, '2019-10-01 06:43:45.717000', 11, '2019-10-01 06:43:45.717000',false,0,false),
(1003,200,10,101,'natasha',null,false, 11, '2019-10-01 07:43:45.717000', 11, '2019-10-01 07:43:45.717000',false,0,false);

INSERT INTO contact_associated_deals (contact_id, associated_deals)
VALUES
(1001, 21),
(1002, 21);


INSERT INTO users(id, tenant_id, name)
VALUES
(2000,1000, '<PERSON> <PERSON>'),
(10,200, '<PERSON> <PERSON>'),
(11,200, 'test user'),
(2051,1050, 'Bony Stark');
