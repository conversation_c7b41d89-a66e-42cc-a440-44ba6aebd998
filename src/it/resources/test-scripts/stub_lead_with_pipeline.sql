--   Create pipeline
DELETE FROM meta_info;
DELETE FROM lead_pipeline_stage ;
DELETE FROM lead_pipeline;
DELETE FROM pipeline_stage;
DELETE FROM pipeline ;
DELETE FROM lead_utm ;
DELETE FROM multi_conversion_association;
DELETE FROM lead ;
DELETE FROM conversion_association;
DELETE FROM users;

INSERT INTO pipeline (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                      active, entity_type, lost_reasons, name, unqualified_reasons)
VALUES (101, '2019-10-01 05:43:45.675000', 1, '2019-10-01 05:43:45.675000', 1, FALSE, 0, 1, 1, TRUE, 'LEAD',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000B4F7665722062756467657474000E4E6F7420696E7465726573746564',
        'Nurturing Pipeline',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000C57726F6E67206E756D626572740012446964206E6F74207069636B2070686F6E65');

-- CREATE DEFAULT PIPELINE STAGES
INSERT INTO pipeline_stage (id,created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES
(100,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 1, 'Open stage', 'OPEN', 'Open', 1, 10, 101),
(110,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 1, 'Prospect/Contacted', 'OPEN', 'Prospect/Contacted', 2, 15, 101),
(120,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Won stage', 'CLOSED_WON', 'Won', 3, 100, 101),
(130,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Closed Unqualified Stage', 'CLOSED_UNQUALIFIED', 'Closed Unqualified', 4, 0, 101),
(140,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Closed Lost Stage', 'CLOSED_LOST', 'Closed Lost', 5, 0, 101);

-- Create Lead
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id, custom_field, first_name, last_name,
            salutation, pipeline, pipeline_stage)
VALUES (1, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 1, 1, '{}', 'Tony', 'Stark'
        , 473, 101, 100);


INSERT INTO meta_info (lead_id, is_new)
VALUES (1, true);

INSERT INTO lead_pipeline(id, tenant_id, lead_id, pipeline_id) OVERRIDING SYSTEM VALUE
VALUES (111, 1, 1, 101);

INSERT INTO lead_pipeline_stage
(id, pipeline_stage_id, status, forecasting_type, position, name, started_at, completed_at, lead_pipeline_id,win_likelihood,description)
OVERRIDING SYSTEM VALUE VALUES
(50,100, 'SKIPPED', 'OPEN', 1, 'Open', '2019-10-01 05:43:45.717000', null, 111,10,'This is description 50'),
(55,110, 'IN_PROGRESS', 'OPEN', 2, 'Prospect/Contacted', null, null, 111,15,'This is description 55'),
(60,120, 'UNEXPLORED', 'CLOSED_WON', 3, 'Won', null, null, 111,100,'This is description 60'),
(65,130, 'UNEXPLORED', 'CLOSED_UNQUALIFIED', 4, 'Closed Unqualified', null, null, 111,0,'This is description 65'),
(70,140, 'UNEXPLORED', 'CLOSED_LOST', 5, 'Closed Lost', null, null, 111,0,'This is description 70');


INSERT INTO conversion_association (id, deal_id, contact_id, company_id, tenant_id)
VALUES (10, 110, 109, 109,1);


--  create lead pipeline

--- lead id = 6
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id, custom_field, first_name, last_name,
            salutation, pipeline, pipeline_stage)
VALUES (6, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 1, 1, '{}', 'Tony', 'Stark'
        , 473, 101, 100);


INSERT INTO lead_pipeline(id, tenant_id, lead_id, pipeline_id) OVERRIDING SYSTEM VALUE
VALUES (222, 1, 6, 101);

INSERT INTO lead_pipeline_stage
(id, pipeline_stage_id, status, forecasting_type, position, name, started_at, completed_at, lead_pipeline_id,win_likelihood, description)
OVERRIDING SYSTEM VALUE VALUES
(90,100, 'IN_PROGRESS', 'OPEN', 1, 'Open', '2019-10-01 05:43:45.717000', null, 222,10,'This is description 90'),
(92,110, 'UNEXPLORED', 'OPEN', 2, 'Prospect/Contacted', null, null, 222,15,'This is description 92'),
(94,120, 'UNEXPLORED', 'CLOSED_WON', 3, 'Won', null, null, 222,100,'This is description 94'),
(96,130, 'UNEXPLORED', 'CLOSED_UNQUALIFIED', 4, 'Closed Unqualified', null, null, 222,0,'This is description 96'),
(98,140, 'UNEXPLORED', 'CLOSED_LOST', 5, 'Closed Lost', null, null, 222,0,'This is description 98');


INSERT INTO lead_utm(id,sub_source,utm_source,utm_campaign,utm_medium,utm_term,utm_content,created_at,updated_at,created_by,updated_by,lead_id)
OVERRIDING SYSTEM VALUE
VALUES
(1901,'google','fb','trial','google-ad','cpc','ad','2020-10-01 05:52:10.919000','2020-10-01 05:52:10.919000',10,10,6);

INSERT INTO meta_info(lead_id, is_new,created_via_id,created_via_name,created_via_type,updated_via_id,updated_via_name,updated_via_type)
VALUES
(6, true,'72','User','Web',null,null, null);

INSERT INTO multi_conversion_association(id,tenant_id,lead_id,entity_id,entity_type,entity_name,converted_at)
OVERRIDING SYSTEM VALUE
VALUES
(99,1,6,109,'DEAL','deal 1','2022-10-01 05:43:45.717000'),
(100,1,6,110,'DEAL','deal 2','2022-10-01 05:43:45.717000'),
(101,1,6,109,'COMPANY','company 1','2022-10-01 05:43:45.717000'),
(102,1,6,110,'COMPANY','company 2','2022-10-01 05:43:45.717000'),
(103,1,6,109,'CONTACT','contact 1','2022-10-01 05:43:45.717000');



INSERT INTO pipeline (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                      active, entity_type, lost_reasons, name, unqualified_reasons)
VALUES (201, '2019-10-01 05:43:45.675000', 1, '2019-10-01 05:43:45.675000', 1, FALSE, 0, 1, 1, FALSE, 'LEAD',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000B4F7665722062756467657474000E4E6F7420696E7465726573746564',
        'Nurturing Pipeline non attached',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000C57726F6E67206E756D626572740012446964206E6F74207069636B2070686F6E65');

-- CREATE DEFAULT PIPELINE STAGES
INSERT INTO pipeline_stage (id,created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES
(300,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 1, 'Open stage', 'OPEN', 'Open', 1, 10, 201),
(310,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 1, 'Prospect/Contacted', 'OPEN', 'Prospect/Contacted', 2, 15, 201),
(320,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Won stage', 'CLOSED_WON', 'Won', 3, 100, 201),
(330,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Closed Unqualified Stage', 'CLOSED_UNQUALIFIED', 'Closed Unqualified', 4, 0, 201),
(340,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 1, 'Closed Lost Stage', 'CLOSED_LOST', 'Closed Lost', 5, 0, 201);

INSERT INTO users(id, tenant_id, name)
VALUES (1,1, 'Tony Stark');
