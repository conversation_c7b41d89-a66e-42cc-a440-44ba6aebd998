--   Create pipeline
DELETE FROM pipeline_stage where id in(9010,9011,9012);
DELETE FROM pipeline WHERE id in(901);
DELETE FROM lead_product;
DELETE FROM lead_utm where id not in (1901);
DELETE FROM lead WHERE id in(1454);
DELETE FROM lead_phone_number;

INSERT INTO pipeline (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                      active, entity_type, lost_reasons, name, unqualified_reasons)
VALUES (901, '2019-10-01 05:43:45.675000', 10, '2019-10-01 05:43:45.675000', 10, FALSE, 0, 100, 10, TRUE, 'LEAD',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000B4F7665722062756467657474000E4E6F7420696E7465726573746564',
        'Lead Pipeline',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000C57726F6E67206E756D626572740012446964206E6F74207069636B2070686F6E65');

-- CREATE DEFAULT PIPELINE STAGES
INSERT INTO pipeline_stage (id,created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES
(9010,'2019-10-01 05:43:45.717000', 10, '2019-10-01 05:43:45.717000', 10, FALSE, 0, 100, 'Open stage', 'OPEN', 'Open', 1, 0, 901),
(9011,'2019-10-01 05:43:45.717000', 10, '2019-10-01 05:43:45.717000', 10, FALSE, 0, 100, 'Open stage', 'OPEN', 'Open with sales', 1, 0, 901),
(9012,'2019-10-01 05:43:45.722000', 10, '2019-10-01 05:43:45.722000', 10, FALSE, 0, 100, 'Won stage', 'CLOSED_WON', 'Won', 2, 100, 901);


-- CREATE LEAD
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name,
                  pipeline, pipeline_stage, pipeline_stage_reason, salutation,emails, phone_numbers)
VALUES (1454, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10,
       '{}', 'Tony', 'Stark',
        NULL, NULL, NULL, 473, '[{"type": "OFFICE", "value": "<EMAIL>", "primary": true}]',
         '[{"code": "IN", "type": "MOBILE", "value": "7777777777", "primary": true, "dialCode": "+91"}, {"code": "IN", "type": "MOBILE", "value": "8888888888", "primary": false, "dialCode": "+91"}]');

INSERT INTO lead_phone_number(id, type, code, value, dial_code, is_primary, lead_id)
OVERRIDING SYSTEM VALUE
VALUES
(90870,'MOBILE', 'IN', '7777777777', '+91', true, 1454),
(90880,'MOBILE', 'IN', '8888888888', '+91', false, 1454);