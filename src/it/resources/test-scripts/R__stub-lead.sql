--   Create pipeline
DELETE FROM meta_info WHERE lead_id in (1,7, 3, 8);
DELETE FROM lead_pipeline_stage WHERE pipeline_stage_id in (100,101);
DELETE FROM lead_pipeline WHERE tenant_id = 100;
DELETE FROM pipeline_stage WHERE pipeline_id = 101;
DELETE FROM pipeline WHERE id = 101;
DELETE FROM lead_gps_address where lead_id in (1,4);
DELETE FROM lead_utm where lead_id = 1;
DELETE FROM multi_conversion_association;
DELETE FROM lead where tenant_id IN (100, 2000);
DELETE FROM conversion_association;

INSERT INTO pipeline (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                      active, entity_type, lost_reasons, name, unqualified_reasons)
VALUES (101, '2019-10-01 05:43:45.675000', 1, '2019-10-01 05:43:45.675000', 1, FALSE, 0, 100, 1, TRUE, 'LEAD',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000B4F7665722062756467657474000E4E6F7420696E7465726573746564',
        'Nurturing Pipeline',
        E'\\xACED0005757200135B4C6A6176612E6C616E672E537472696E673BADD256E7E91D7B4702000078700000000274000C57726F6E67206E756D626572740012446964206E6F74207069636B2070686F6E65');

-- CREATE DEFAULT PIPELINE STAGES
INSERT INTO pipeline_stage (id,created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, description, forecasting_type, name, position, win_likelihood, pipeline_id)
VALUES
(100,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 100, 'Open stage', 'OPEN', 'Open', 1, 10, 101),
(110,'2019-10-01 05:43:45.717000', 1, '2019-10-01 05:43:45.717000', 1, FALSE, 0, 100, 'Open stage', 'OPEN', 'Prospect/Contacted', 2, 15, 101),
(120,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 100, 'Won stage', 'CLOSED_WON', 'Won', 3, 100, 101),
(130,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 100, 'Closed Unqualified Stage ', 'CLOSED_UNQUALIFIED', 'Closed Unqualified', 4, 0, 101),
(140,'2019-10-01 05:43:45.722000', 1, '2019-10-01 05:43:45.722000', 1, FALSE, 0, 100, 'Closed Lost Stage', 'CLOSED_LOST', 'Closed Lost', 5, 0, 101);

-- Create Lead
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id, custom_field, first_name, last_name,
            salutation, pipeline, pipeline_stage)
VALUES (1, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Tony', 'Stark'
        , 473, 101, 100);


INSERT INTO meta_info (lead_id, is_new)
VALUES (1, true);

INSERT INTO lead_pipeline(id, tenant_id, lead_id, pipeline_id) OVERRIDING SYSTEM VALUE
VALUES (111, 100, 1, 101);

INSERT INTO lead_pipeline_stage
(id, pipeline_stage_id, status, forecasting_type, position, name, started_at, completed_at, lead_pipeline_id,win_likelihood)
OVERRIDING SYSTEM VALUE VALUES
(50,100, 'IN_PROGRESS', 'OPEN', 1, 'Open', '2019-10-01 05:43:45.717000', null, 111,10),
(55,110, 'UNEXPLORED', 'OPEN', 2, 'Prospect/Contacted', null, null, 111,15),
(60,120, 'UNEXPLORED', 'CLOSED_WON', 3, 'Won', null, null, 111,100),
(65,130, 'UNEXPLORED', 'CLOSED_UNQUALIFIED', 4, 'Closed Unqualified', null, null, 111,0),
(70,140, 'UNEXPLORED', 'CLOSED_LOST', 5, 'Closed Lost', null, null, 111,0);


INSERT INTO conversion_association (id, deal_id, contact_id, company_id, tenant_id)
VALUES (10, 110, 109, 109, 100);

----------
INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation)
VALUES (2, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Tony', 'Stark'
        , 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation)
VALUES (3, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Tony', 'Stark'
       , 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation)
VALUES (4, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Steve', 'Rojer'
       , 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation)
VALUES (5, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Peter', 'Parkar', 473),
       (50, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Peter', 'Parkar', 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation, pipeline, pipeline_stage)
VALUES (6, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Black', 'Adam'
        , 473, 101, 200);


INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id,
                  custom_field, first_name, last_name, salutation)
VALUES (7, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Steve', 'Rojer'
       , 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id, custom_field, first_name, last_name, salutation)
VALUES (8, '2019-10-01 05:52:10.919000', 2000, '2019-10-01 05:52:10.919000', 2000, FALSE, 0, 2000, 2000, '{}', 'Steve', 'Rojer', 473);

INSERT INTO lead (id, created_at, created_by, updated_at, updated_by, deleted, version, tenant_id, owner_id, custom_field, first_name, last_name,
            salutation, pipeline, pipeline_stage,converted_by,conversion_association_id)
VALUES (10, '2019-10-01 05:52:10.919000', 10, '2019-10-01 05:52:10.919000', 10, FALSE, 0, 100, 10, '{}', 'Tony', 'Stark'
        , 473, 101, 100,10,10);


--  create lead pipeline

--- lead id = 6

INSERT INTO lead_pipeline(id, tenant_id, lead_id, pipeline_id) OVERRIDING SYSTEM VALUE
VALUES (211, 100, 6, 101);

INSERT INTO lead_pipeline_stage(pipeline_stage_id, status, forecasting_type, position, name, started_at, completed_at, lead_pipeline_id,win_likelihood)
VALUES (200, 'IN_PROGRESS', 'OPEN', 1, 'Open', '2019-10-01 05:43:45.717000', null, 211,100);

INSERT INTO lead_pipeline_stage(pipeline_stage_id, status, forecasting_type, position, name, started_at, lead_pipeline_id,win_likelihood)
VALUES (201, 'UNEXPLORED', 'CLOSED_WON', 2, 'Closed Won', null, 211,100);


INSERT INTO lead_utm(id,sub_source,utm_source,utm_campaign,utm_medium,utm_term,utm_content,created_at,updated_at,created_by,updated_by,lead_id)
OVERRIDING SYSTEM VALUE
VALUES
(1901,'google','fb','trial','google-ad','cpc','ad','2020-10-01 05:52:10.919000','2020-10-01 05:52:10.919000',10,10,1);

INSERT INTO meta_info(lead_id, is_new,created_via_id,created_via_name,created_via_type,updated_via_id,updated_via_name,updated_via_type)
VALUES
(7, true,'72','User','Web',null,null, null),
(3, true,'72','User','Mobile',null,null, null),
(8, true,'2000','John Doe','Web', '2000', 'John Doe','Web');

INSERT INTO multi_conversion_association(id,tenant_id,lead_id,entity_id,entity_type,entity_name,converted_at)
OVERRIDING SYSTEM VALUE
VALUES
(99,100,10,109,'DEAL','deal 1','2022-10-01 05:43:45.717000'),
(100,100,10,110,'DEAL','deal 2','2022-10-01 05:43:45.717000'),
(101,100,10,109,'COMPANY','company 1','2022-10-01 05:43:45.717000'),
(102,100,10,110,'COMPANY','company 2','2022-10-01 05:43:45.717000'),
(103,100,10,109,'CONTACT','contact 1','2022-10-01 05:43:45.717000');

INSERT INTO lead_gps_address(id,
address_coordinate_latitude, address_coordinate_longitude, company_address_coordinate_latitude,company_address_coordinate_longitude,lead_id)
OVERRIDING SYSTEM VALUE
VALUES
(8911,89.0078,65.5690,90.7890,56.7729,1),
(8912,89.0078,65.5690,90.7890,56.7729,4);