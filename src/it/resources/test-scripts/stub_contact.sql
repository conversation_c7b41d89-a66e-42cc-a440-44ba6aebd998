/*
tenantId=100,
*/
DELETE FROM contact_phone_number;
DELETE FROM contact where id in(5001,5002);

INSERT INTO contact
(id,tenant_id, owner_id, company, first_name, last_name, stakeholder,created_by, created_at, updated_by, updated_at,deleted, version,dnd, phone_numbers)
    OVERRIDING SYSTEM VALUE
VALUES
(5001,100,10,null,'steve','roger',true,11, '2019-10-01 05:43:45.717000', 11, '2019-10-01 05:43:45.717000',false,0,false, '[{"code": "IN", "type": "MOBILE", "value": "1231231231", "primary": true, "dialCode": "+91"}, {"code": "IN", "type": "MOBILE", "value": "1231231231", "primary": false, "dialCode": "+91"}]'),
(5002,100,10,101,'thor',null,false, 11, '2019-10-01 06:43:45.717000', 11, '2019-10-01 06:43:45.717000',false,0,false, null);

INSERT INTO contact_phone_number(id, type, code, value, dial_code, contact_id, is_primary)
 OVERRIDING SYSTEM VALUE
 VALUES(1934, 'MOBILE', 'IN', '1231231231', '+91', 5001, true);