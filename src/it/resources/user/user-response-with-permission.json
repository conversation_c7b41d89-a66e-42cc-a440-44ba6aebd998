{"createdAt": "2019-11-10T04:47:59.436+0000", "updatedAt": "2019-11-10T04:47:59.815+0000", "createdBy": 1, "updatedBy": 2, "recordActions": {"read": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false}, "metaData": {"idNameStore": {"updatedBy": {"2": "second User"}, "createdBy": {"1": "Test User"}, "profileId": {"2": "Admin"}, "salutation": {"476": "Mr"}, "ownerId": {"1": "Test User"}}}, "id": 2000, "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "updatedEmail": null, "salutation": 476, "phoneNumbers": [{"type": "MOBILE", "code": "IN", "value": "9865321254", "dialCode": "+91", "primary": true}], "designation": null, "timezone": "Asia/Calcutta", "language": null, "dateFormat": null, "currency": null, "signature": null, "title": null, "department": null, "failedAttempts": null, "locked": false, "lockedAt": null, "active": true, "deactivateReason": null, "emailVerified": true, "confirmedAt": null, "confirmationTokenSentAt": null, "resetPasswordTokenSentAt": null, "unlockTokenSentAt": null, "customFieldValues": null, "profileId": 2, "permissions": [{"id": 7, "name": "team", "description": "has", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 8, "name": "config", "description": "has", "limits": 100, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 18, "name": "company", "description": "has", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "readAll": true, "updateAll": true}}, {"id": 15, "name": "task", "description": "has", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": true, "readAll": true, "updateAll": true}}, {"id": 17, "name": "contact", "description": "has permission to contact resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "readAll": true, "updateAll": true}}, {"id": 9, "name": "searchList", "description": "has", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 2, "name": "tenant", "description": "has", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 13, "name": "profile", "description": "has", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 22, "name": "products-services", "description": "has", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 20, "name": "layout", "description": "has", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 23, "name": "report", "description": "has", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 10, "name": "pipeline", "description": "has", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 6, "name": "customField", "description": "has access to custom field resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 3, "name": "user", "description": "has access to user resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 12, "name": "deal", "description": "has permission to deal resource", "limits": 50, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "readAll": true, "updateAll": true}}, {"id": 4, "name": "lead", "description": "has access to lead resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": true, "call": true, "sms": false, "task": true, "note": true, "readAll": true, "updateAll": true}}, {"id": 11, "name": "shareRule", "description": "has permission to sharing a resource", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": false, "updateAll": false}}, {"id": 16, "name": "note", "description": "has permission to note resource", "limits": -1, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 14, "name": "conversionMapping", "description": "has permission to lead conversion mapping resource", "limits": -1, "units": "count", "action": {"read": true, "write": false, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "readAll": true, "updateAll": true}}, {"id": 24, "name": "lead-capture-forms", "description": "has access to Lead Capture Forms", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": false, "readAll": true, "updateAll": true}}, {"id": 26, "name": "workflow", "description": "has access to Workflows", "limits": 5, "units": "count", "action": {"read": false, "write": true, "update": false, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": true}}, {"id": 27, "name": "email_template", "description": "has access to Email Templates", "limits": 5, "units": "count", "action": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "readAll": true, "updateAll": true}}]}