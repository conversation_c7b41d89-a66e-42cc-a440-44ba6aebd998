[{"op": "replace", "path": "/firstName", "value": "<PERSON><PERSON>"}, {"op": "replace", "path": "/subSource", "value": "Sub Source updated by patch"}, {"op": "remove", "path": "/utmSource"}, {"op": "add", "path": "/utmMedium", "value": "UTM Medium updated by patch"}, {"op": "replace", "path": "/lastName", "value": "<PERSON>"}, {"op": "add", "path": "/phoneNumbers/0", "value": {"id": null, "type": "MOBILE", "code": "IN", "value": "8734523457", "dialCode": "+91", "primary": true}}, {"op": "add", "path": "/companyPhones/0", "value": {"id": null, "type": "MOBILE", "code": "IN", "value": "8734523457", "dialCode": "+91", "isPrimary": false}}, {"op": "add", "path": "/companyPhones/0", "value": {"id": null, "type": "MOBILE", "code": "IN", "value": "8734523459", "dialCode": "+91", "isPrimary": false}}, {"op": "add", "path": "/products/0", "value": {"id": 55, "name": "new product"}}, {"op": "add", "path": "/products/1", "value": {"id": 99, "name": "new product"}}, {"op": "remove", "path": "/customFieldValues/myMultiPickList/0"}, {"op": "replace", "path": "/customFieldValues/cfCustom", "value": "updated by patch"}, {"op": "replace", "path": "/salutation", "value": 473}, {"op": "add", "path": "/source", "value": 66}, {"op": "add", "path": "/campaign", "value": 55}, {"op": "add", "path": "/companyCountry", "value": "IN"}, {"op": "replace", "path": "/companyEmployees", "value": 27}, {"op": "replace", "path": "/companyBusinessType", "value": "real estate"}, {"op": "add", "path": "/companyIndustry", "value": "REAL_ESTATE"}, {"op": "add", "path": "/timezone", "value": "Pacific/Midway"}, {"op": "replace", "path": "/pipeline", "value": {"id": 50, "name": "Nurturing Pipeline", "stage": {"id": 800, "name": "Open stage"}}}, {"op": "replace", "path": "/ownerId", "value": 101}, {"op": "replace", "path": "/address", "value": "Address"}, {"op": "replace", "path": "/city", "value": "City"}, {"op": "replace", "path": "/state", "value": "State"}, {"op": "replace", "path": "/zipcode", "value": "900890"}, {"op": "replace", "path": "/facebook", "value": "https://fb.com"}, {"op": "replace", "path": "/twitter", "value": "https://x.com"}, {"op": "add", "path": "/linkedIn", "value": "https://li.com"}, {"op": "add", "path": "/requirementBudget", "value": 100}, {"op": "add", "path": "/requirementCurrency", "value": "EUR"}]