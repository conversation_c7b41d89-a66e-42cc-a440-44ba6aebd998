{"lead": {"address": "This is address of tony stark", "pipeline": {"id": 51, "name": "Does not matter", "stage": {"id": 901, "name": "won"}}, "ownerId": {"id": 99, "name": "My User"}, "products": {"operation": "ADD", "values": [{"id": 55, "name": "Product 55"}, {"id": 99, "name": "Product 99"}]}, "utmSource": "patch utm source", "subSource": "patch sub source", "customFieldValues": {"myMultiPickList": {"operation": "ADD", "values": [454390]}}}, "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true}