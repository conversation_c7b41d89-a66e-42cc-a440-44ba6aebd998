{"lead": {"address": "This is address of tony stark", "pipeline": {"id": 51, "name": "Does not matter", "stage": {"id": 901, "name": "OPEN"}}, "ownerId": {"id": 99, "name": "Dont know"}, "products": {"operation": "ADD", "values": [{"id": 55, "name": "CRM"}, {"id": 99, "name": "Marketing"}]}, "emails": {"operation": "REPLACE", "values": [{"type": "OFFICE", "value": "<EMAIL>"}, {"type": "OFFICE", "value": "<EMAIL>"}]}, "phoneNumbers": {"operation": "ADD", "values": [{"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "1234567000"}, {"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "9876543000"}]}, "companyPhones": {"operation": "REPLACE", "values": [{"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "7412589000"}, {"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "8523697000"}]}, "": "Budget does not match"}, "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true}