{"entity": null, "oldEntity": {"id": 91, "tenantId": 1000, "ownerId": {"id": 10, "name": "<PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "name": "<PERSON>", "salutation": {"id": null, "name": null}, "timezone": null, "address": "This is address", "city": "Pune", "state": "Maharashtra", "zipcode": null, "country": "India", "department": "Sales", "dnd": true, "phoneNumbers": [{"id": 9999, "type": "MOBILE", "code": "IN", "value": "9876543210", "dialCode": "+91", "primary": true}, {"id": 9998, "type": "MOBILE", "code": "IN", "value": "1234567890", "dialCode": "+91", "primary": false}], "photoUrls": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": false}, {"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "facebook": "http://facebook.com", "twitter": null, "linkedIn": "http://linked.in", "pipeline": {"id": 50, "name": "Nurturing Pipeline"}, "pipelineStage": {"id": 801, "name": "Won"}, "pipelineStageReason": null, "companyName": "K2v2", "companyAddress": "This is comapny address", "companyCity": "Company city", "companyState": "MH", "companyZipcode": "411044", "companyCountry": "India", "companyEmployees": {"id": 55, "name": null}, "companyAnnualRevenue": 45982.99, "companyWebsite": "http://kylas.io", "companyIndustry": "Online", "companyBusinessType": "Lead generation", "companyPhones": [{"id": 9998, "type": "MOBILE", "code": "IN", "value": "1234567891", "dialCode": "+91", "primary": false}, {"id": 9999, "type": "MOBILE", "code": "IN", "value": "9876543212", "dialCode": "+91", "primary": true}], "requirementName": "Need something to complete", "requirementCurrency": "INR", "requirementBudget": 5974.55, "expectedClosureOn": "2022-10-01T05:52:10.919Z", "products": [{"id": 2, "name": "cellphone", "tenantId": 1000}], "conversionAssociation": {"id": 91, "dealId": 110, "contactId": 109, "companyId": 109, "tenantId": 1000}, "conversionAssociations": [{"id": 101, "tenantId": 100, "entityType": "COMPANY", "entityId": 109, "entityName": "company 1", "convertedAt": "2022-10-01T05:43:45.717Z"}, {"id": 103, "tenantId": 100, "entityType": "CONTACT", "entityId": 109, "entityName": "contact 1", "convertedAt": "2022-10-01T05:43:45.717Z"}, {"id": 102, "tenantId": 100, "entityType": "COMPANY", "entityId": 110, "entityName": "company 2", "convertedAt": "2022-10-01T05:43:45.716Z"}, {"id": 100, "tenantId": 100, "entityType": "DEAL", "entityId": 110, "entityName": "deal 1", "convertedAt": "2022-10-01T05:43:45.717Z"}], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": 789, "name": null}, "forecastingType": "CLOSED_WON", "customFieldValues": {"myMultiPickList": [454389, 454388], "cfCustom": "custom text Value"}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 0, "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2019-10-01T05:52:10.919Z", "createdBy": {"id": 10, "name": "<PERSON>"}, "updatedBy": {"id": 10, "name": "<PERSON>"}, "actualClosureDate": "2020-10-01T05:52:10.919Z", "createdViaId": "72", "createdViaName": "User", "createdViaType": "Web", "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": "Sub Source", "utmSource": "UTM Source", "utmMedium": "UTM Medium", "utmCampaign": "UTM Campaign", "utmTerm": "UTM Term", "utmContent": "UTM Content", "score": null, "isNew": true, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"50": "Nurturing Pipeline"}, "updatedBy": {"10": "<PERSON>"}, "createdBy": {"10": "<PERSON>"}, "ownerId": {"10": "<PERSON>"}, "pipelineStage": {"801": "Won"}}}, "metadata": {"tenantId": 1000, "userId": 10, "entityType": "LEAD", "entityId": 91, "entityAction": "DELETED", "workflowId": null, "executedWorkflows": [], "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true, "publishUsage": true, "workflowName": null, "scoreRuleId": null, "scoreRuleName": null}}