{"entity": {"id": 1, "tenantId": 1000, "ownerId": {"id": 99, "name": null}, "firstName": "tony", "lastName": "startk", "name": "tony startk", "salutation": {"id": null, "name": null}, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "department": null, "dnd": true, "phoneNumbers": [{"id": 7, "type": "MOBILE", "code": "IN", "value": "1231231231", "dialCode": "+91", "primary": true}, {"id": 8, "type": "WORK", "code": "IN", "value": "1231231232", "dialCode": "+91", "primary": false}], "photoUrls": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "facebook": null, "twitter": null, "linkedIn": null, "pipeline": {"id": 1, "name": "nurturing pipeline"}, "pipelineStage": {"id": 13, "name": "OpenThree"}, "pipelineStageReason": null, "companyName": null, "companyAddress": null, "companyCity": null, "companyState": null, "companyZipcode": null, "companyCountry": null, "companyEmployees": {"id": null, "name": null}, "companyAnnualRevenue": null, "companyWebsite": null, "companyIndustry": null, "companyBusinessType": null, "companyPhones": null, "requirementName": null, "requirementCurrency": null, "requirementBudget": null, "expectedClosureOn": null, "products": [], "conversionAssociation": null, "conversionAssociations": [], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": null, "name": null}, "forecastingType": "OPEN", "customFieldValues": {}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 2, "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2024-09-03T17:17:14.976Z", "createdBy": {"id": 10, "name": "<PERSON>"}, "updatedBy": {"id": 10, "name": "<PERSON>"}, "actualClosureDate": null, "createdViaId": null, "createdViaName": null, "createdViaType": null, "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": null, "utmSource": null, "utmMedium": null, "utmCampaign": null, "utmTerm": null, "utmContent": null, "score": 0.0, "isNew": null, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"1": "nurturing pipeline"}, "updatedBy": {"10": "<PERSON>"}, "createdBy": {"10": "<PERSON>"}, "ownerId": {"99": null}, "pipelineStage": {"13": "OpenThree"}}, "addressCoordinate": null, "companyAddressCoordinate": null}, "oldEntity": {"id": 1, "tenantId": 1000, "ownerId": {"id": 10, "name": "<PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "name": "<PERSON>", "salutation": {"id": 473, "name": "Mr"}, "timezone": null, "addressCoordinate": null, "companyAddressCoordinate": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "department": null, "dnd": null, "phoneNumbers": null, "photoUrls": null, "emails": null, "facebook": null, "twitter": null, "linkedIn": null, "pipeline": {"id": 1, "name": "nurturing pipeline"}, "pipelineStage": {"id": 11, "name": "OpenOne"}, "pipelineStageReason": null, "companyName": null, "companyAddress": null, "companyCity": null, "companyState": null, "companyZipcode": null, "companyCountry": null, "companyEmployees": {"id": null, "name": null}, "companyAnnualRevenue": null, "companyWebsite": null, "companyIndustry": null, "companyBusinessType": null, "companyPhones": null, "requirementName": null, "requirementCurrency": null, "requirementBudget": null, "expectedClosureOn": null, "products": [], "conversionAssociation": null, "conversionAssociations": [], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": null, "name": null}, "forecastingType": null, "customFieldValues": {}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 0, "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2019-10-01T05:52:10.919Z", "createdBy": {"id": 10, "name": "<PERSON>"}, "updatedBy": {"id": 10, "name": "<PERSON>"}, "actualClosureDate": null, "createdViaId": null, "createdViaName": null, "createdViaType": null, "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": null, "utmSource": null, "utmMedium": null, "utmCampaign": null, "utmTerm": null, "utmContent": null, "score": null, "isNew": null, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"1": "nurturing pipeline"}, "updatedBy": {"10": "<PERSON>"}, "createdBy": {"10": "<PERSON>"}, "salutation": {"473": "Mr"}, "ownerId": {"10": "<PERSON>"}, "pipelineStage": {"11": "OpenOne"}}}, "metadata": {"eventId": null, "tenantId": 1000, "userId": 10, "entityType": "LEAD", "entityId": 1, "entityAction": "UPDATED", "workflowId": null, "executedWorkflows": [], "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true, "publishUsage": true, "workflowName": null, "scoreRuleId": null, "scoreRuleName": null}}