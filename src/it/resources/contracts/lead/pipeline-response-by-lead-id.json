{"id": 1, "name": "nurturing pipeline", "unqualifiedReasons": ["Budget does not match", "False enquiry", "Wrong number", "Customer already bought the product/service", "Bought product/service with competitor", "Did not pick phone", "Not interested anymore", "Duplicate Customer"], "lostReasons": ["No followup", "Not interested", "Booked with competitor", "Not answering/responding", "Bought product/service with competitor", "Low budget", "Postponed the decision of buying product/service"], "stages": [{"id": 1, "name": "Open", "description": "Open stage", "forecastingType": "OPEN", "position": 1, "status": "COMPLETED", "startedAt": "2019-10-01T05:43:45.717Z", "completedAt": "2019-10-03T05:43:45.717Z", "reason": null, "winLikelihood": 0}, {"id": 2, "name": "Won", "description": "Won stage", "forecastingType": "OPEN", "position": 2, "status": "UNEXPLORED", "startedAt": null, "completedAt": null, "reason": null, "winLikelihood": 100}, {"id": 3, "name": "Closed Unqualified", "description": "Closed Unqualified stage", "forecastingType": "OPEN", "position": 3, "status": "IN_PROGRESS", "startedAt": "2019-10-01T05:43:45.717Z", "completedAt": null, "reason": null, "winLikelihood": 0}, {"id": 4, "name": "Closed Lost", "description": "Closed Lost stage", "forecastingType": "CLOSED_LOST", "position": 4, "status": "UNEXPLORED", "startedAt": null, "completedAt": null, "reason": null, "winLikelihood": 0}]}