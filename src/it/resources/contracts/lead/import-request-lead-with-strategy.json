{"jobId": 500, "lead": {"salutation": 473, "firstName": "tony", "lastName": "startk", "dnd": true, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "phoneNumbers": [{"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "1231231231", "primary": true}, {"type": "WORK", "code": "IN", "dialCode": "+91", "value": "1231231232", "primary": false}], "timezone": "Pacific/Midway", "address": "My Address", "city": "My City", "state": "My State", "country": "IN", "zipcode": "411033", "facebook": "http://facebook.com", "twitter": "http://twitter", "linkedIn": "http://linkedin.com", "companyName": "My company", "department": "my department", "designation": "my desgination", "companyIndustry": "REAL_ESTATE", "companyBusinessType": "real estate", "companyEmployees": 27, "companyAnnualRevenue": 1, "companyWebsite": "http://website.com", "companyPhones": [{"type": "PERSONAL", "code": "IN", "dialCode": "+91", "value": "1231231233", "primary": true}], "companyAddress": "company address", "companyCity": "My City", "companyState": "My State", "companyCountry": "IN", "companyZipcode": "411045", "requirementName": "requirement", "requirementCurrency": "EUR", "requirementBudget": 123, "customFieldValues": {}, "campaign": 55, "source": 66, "pipeline": {"name": "Lead Pipeline", "stage": {"name": "Won"}}, "productName": null, "ownerEmail": "<EMAIL>", "subSource": "Sub Source", "utmSource": "UTM Source", "utmMedium": "UTM Medium", "utmCampaign": "UTM Campaign", "utmTerm": "UTM Term", "utmContent": "UTM Content", "createdByEmail": "<EMAIL>", "updatedByEmail": "<EMAIL>", "createdAt": "2020-09-25T07:23:20.092+0000", "updatedAt": "2021-03-08T07:12:35.402+0000"}, "importStrategy": {"type": "MERGE_DUPLICATE_WITH_MISSING_DATA"}, "errorInImport": false}