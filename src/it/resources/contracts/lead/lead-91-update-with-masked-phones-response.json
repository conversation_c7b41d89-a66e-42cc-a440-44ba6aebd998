{"createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2024-09-03T17:23:17.832Z", "createdBy": 10, "updatedBy": 10, "recordActions": {"read": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false, "document": false, "deleteAll": false, "quotation": false, "reassign": false}, "metaData": {"idNameStore": {"pipeline": {"51": "Nurturing Pipeline new"}, "updatedBy": {"10": "<PERSON>"}, "createdBy": {"10": "<PERSON>"}, "ownerId": {"10": "<PERSON>"}, "pipelineStage": {"900": "Open"}}}, "id": 91, "ownerId": 10, "firstName": "<PERSON>", "phoneNumbers": [{"id": 1001, "type": "MOBILE", "code": "IN", "value": "****890", "dialCode": "+91", "primary": false}, {"id": 1, "type": "WORK", "code": "IN", "value": "****210", "dialCode": "+91", "primary": true}, {"id": 2, "type": "HOME", "code": "IN", "value": "****321", "dialCode": "+91", "primary": false}], "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": false}, {"type": "OFFICE", "value": "<EMAIL>", "primary": true}], "pipeline": {"id": 51, "name": "Nurturing Pipeline new", "stage": {"id": 900, "name": "Open"}}, "forecastingType": "OPEN", "address": "This is address of tony stark", "companyPhones": [{"id": 1, "type": "WORK", "code": "IN", "value": "****333", "dialCode": "+91", "primary": true}, {"id": 2, "type": "HOME", "code": "IN", "value": "****444", "dialCode": "+91", "primary": false}, {"id": 1001, "type": "MOBILE", "code": "IN", "value": "****217", "dialCode": "+91", "primary": false}], "latestActivityCreatedAt": "2024-09-03T17:23:17.923Z", "createdViaId": "72", "createdViaName": "User", "createdViaType": "Web", "updatedViaId": "72", "updatedViaName": "User", "updatedViaType": "Web", "subSource": "Updated Sub Source", "utmSource": "Updated UTM Source", "utmMedium": "Updated UTM Medium", "utmCampaign": "Updated Sub Campaign", "utmTerm": "Updated UTM Term", "utmContent": "Updated Sub Content", "conversionDetails": [{"entityType": "DEAL", "entityId": 110, "convertedAt": "2022-10-01T05:43:45.717+0000"}, {"entityType": "CONTACT", "entityId": 109, "convertedAt": "2022-10-01T05:43:45.717+0000"}, {"entityType": "COMPANY", "entityId": 110, "convertedAt": "2022-10-01T05:43:45.716+0000"}, {"entityType": "COMPANY", "entityId": 109, "convertedAt": "2022-10-01T05:43:45.717+0000"}], "score": 677.58}