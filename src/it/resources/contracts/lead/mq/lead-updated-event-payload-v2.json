{"entity": {"id": 1, "tenantId": 1, "ownerId": {"id": 1, "name": "<PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "name": "<PERSON>", "salutation": {"id": 473, "name": null}, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "department": null, "dnd": null, "phoneNumbers": null, "photoUrls": null, "emails": null, "facebook": null, "twitter": null, "linkedIn": null, "pipeline": {"id": 1, "name": "nurturing pipeline"}, "pipelineStage": {"id": 3, "name": "Closed Unqualified"}, "pipelineStageReason": "Lost the lead due to budget", "companyName": null, "companyAddress": null, "companyCity": null, "companyState": null, "companyZipcode": null, "companyCountry": null, "companyEmployees": {"id": null, "name": null}, "companyAnnualRevenue": null, "companyWebsite": null, "companyIndustry": null, "companyBusinessType": null, "companyPhones": null, "requirementName": null, "requirementCurrency": null, "requirementBudget": null, "expectedClosureOn": null, "products": [], "conversionAssociation": null, "conversionAssociations": [], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": null, "name": null}, "forecastingType": "OPEN", "customFieldValues": {}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 1, "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2024-09-03T10:16:19.812Z", "createdBy": {"id": 1, "name": "<PERSON>"}, "updatedBy": {"id": 1, "name": "<PERSON>"}, "actualClosureDate": null, "createdViaId": null, "createdViaName": null, "createdViaType": null, "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": null, "utmSource": null, "utmMedium": null, "utmCampaign": null, "utmTerm": null, "utmContent": null, "score": null, "isNew": null, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"1": "nurturing pipeline"}, "updatedBy": {"1": "<PERSON>"}, "createdBy": {"1": "<PERSON>"}, "ownerId": {"1": "<PERSON>"}, "pipelineStage": {"3": "Closed Unqualified"}}}, "oldEntity": {"id": 1, "tenantId": 1, "ownerId": {"id": 1, "name": "<PERSON>"}, "firstName": "<PERSON>", "lastName": "<PERSON>", "name": "<PERSON>", "salutation": {"id": 473, "name": null}, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "department": null, "dnd": null, "phoneNumbers": null, "photoUrls": null, "emails": null, "facebook": null, "twitter": null, "linkedIn": null, "pipeline": {"id": 1, "name": "nurturing pipeline"}, "pipelineStage": {"id": 1, "name": "Open"}, "pipelineStageReason": null, "companyName": null, "companyAddress": null, "companyCity": null, "companyState": null, "companyZipcode": null, "companyCountry": null, "companyEmployees": {"id": null, "name": null}, "companyAnnualRevenue": null, "companyWebsite": null, "companyIndustry": null, "companyBusinessType": null, "companyPhones": null, "requirementName": null, "requirementCurrency": null, "requirementBudget": null, "expectedClosureOn": null, "products": [], "conversionAssociation": null, "conversionAssociations": [], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": null, "name": null}, "forecastingType": null, "customFieldValues": {}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 0, "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2019-10-01T05:52:10.919Z", "createdBy": {"id": 1, "name": "<PERSON>"}, "updatedBy": {"id": 2, "name": null}, "actualClosureDate": null, "createdViaId": null, "createdViaName": null, "createdViaType": null, "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": null, "utmSource": null, "utmMedium": null, "utmCampaign": null, "utmTerm": null, "utmContent": null, "score": null, "isNew": null, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"1": "nurturing pipeline"}, "updatedBy": {"2": null}, "createdBy": {"1": "<PERSON>"}, "ownerId": {"1": "<PERSON>"}, "pipelineStage": {"1": "Open"}}}, "metadata": {"tenantId": 1, "userId": 1, "entityType": "LEAD", "entityId": 1, "entityAction": "UPDATED", "workflowId": null, "executedWorkflows": [], "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true, "publishUsage": true, "workflowName": null, "scoreRuleId": null, "scoreRuleName": null}}