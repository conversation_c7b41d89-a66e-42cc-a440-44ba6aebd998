{"entity": {"id": 1, "tenantId": 1000, "ownerId": {"id": 10, "name": "<PERSON>"}, "firstName": "tony", "lastName": "startk", "name": "tony startk", "salutation": {"id": 473, "name": "Mr"}, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "addressCoordinate": null, "companyAddressCoordinate": null, "department": null, "dnd": true, "phoneNumbers": [{"id": 1, "type": "MOBILE", "code": "IN", "value": "1231231231", "dialCode": "+91", "primary": true}, {"id": 2, "type": "WORK", "code": "IN", "value": "1231231232", "dialCode": "+91", "primary": false}], "photoUrls": null, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "facebook": null, "twitter": null, "linkedIn": null, "pipeline": {"id": 1, "name": "nurturing pipeline"}, "pipelineStage": {"id": 3, "name": "Closed Unqualified"}, "pipelineStageReason": "Wrong number", "companyName": null, "companyAddress": null, "companyCity": null, "companyState": null, "companyZipcode": null, "companyCountry": null, "companyEmployees": {"id": null, "name": null}, "companyAnnualRevenue": null, "companyWebsite": null, "companyIndustry": null, "companyBusinessType": null, "companyPhones": null, "requirementName": null, "requirementCurrency": null, "requirementBudget": null, "expectedClosureOn": null, "products": null, "conversionAssociation": null, "conversionAssociations": [], "convertedAt": null, "convertedBy": {"id": null, "name": null}, "designation": null, "campaign": {"id": null, "name": null}, "source": {"id": null, "name": null}, "forecastingType": "CLOSED_UNQUALIFIED", "customFieldValues": {}, "importedBy": {"id": null, "name": null}, "deleted": false, "version": 0, "createdAt": "2024-09-03T17:02:55.629Z", "updatedAt": "2024-09-03T17:02:55.629Z", "createdBy": {"id": 10, "name": "<PERSON>"}, "updatedBy": {"id": 10, "name": "<PERSON>"}, "actualClosureDate": "2024-09-03T17:02:56.023Z", "createdViaId": "72", "createdViaName": "User", "createdViaType": "Web", "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "subSource": null, "utmSource": null, "utmMedium": null, "utmCampaign": null, "utmTerm": null, "utmContent": null, "score": 0, "isNew": true, "latestActivityCreatedAt": null, "meetingScheduledOn": null, "taskDueOn": null, "idNameStore": {"pipeline": {"1": "nurturing pipeline"}, "updatedBy": {"10": "<PERSON>"}, "createdBy": {"10": "<PERSON>"}, "salutation": {"473": "Mr"}, "ownerId": {"10": "<PERSON>"}, "pipelineStage": {"3": "Closed Unqualified"}}}, "oldEntity": null, "metadata": {"eventId": null, "tenantId": 1000, "userId": 10, "entityType": "LEAD", "entityId": 1, "entityAction": "CREATED", "workflowId": null, "executedWorkflows": [], "executeWorkflow": true, "executeScoreRule": true, "sendNotification": true, "publishUsage": true, "workflowName": null, "scoreRuleId": null, "scoreRuleName": null}}