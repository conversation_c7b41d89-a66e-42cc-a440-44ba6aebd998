{"jobId": 500, "contact": {"salutation": 546, "firstName": "<PERSON>", "lastName": "<PERSON>", "ownerEmail": null, "company": 103, "emails": [{"type": "OFFICE", "value": "<EMAIL>", "primary": true}, {"type": "OFFICE", "value": "<EMAIL>", "primary": false}], "phoneNumbers": [{"type": "MOBILE", "code": "IN", "dialCode": "+91", "value": "01231231231", "primary": true}, {"type": "WORK", "code": "IN", "dialCode": "+91", "value": "1231231232", "primary": false}], "department": "My Department", "designation": "My Designation", "stakeholder": true, "address": "My Address", "city": "My City", "state": "My State", "country": "BS", "zipcode": "411045", "facebook": "http://facebook.com", "twitter": "http://twitter.com", "linkedin": "http://linkedin.com", "timezone": "Pacific/Midway", "customFieldValues": {}, "createdByEmail": "<EMAIL>", "updatedByEmail": "<EMAIL>", "createdAt": "2019-10-01T05:52:10.919Z", "updatedAt": "2023-06-15T06:27:44.023Z"}, "importStrategy": {"type": "NONE"}}