{"content": [{"createdAt": "2019-10-01T07:43:45.717+0000", "updatedAt": "2019-10-01T07:43:45.717+0000", "createdBy": 100, "updatedBy": 700, "id": 1003, "deleted": false, "version": 0, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reassign": false}, "metaData": {"idNameStore": {"updatedBy": {"700": null}, "createdBy": {"100": null}, "ownerId": {"8": null}}}, "tenantId": 100, "ownerId": 8, "customFieldValues": {}, "salutation": null, "firstName": "natasha", "lastName": null, "phoneNumbers": null, "contactPhoneNumbers": [], "emails": null, "dnd": false, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "facebook": null, "twitter": null, "linkedin": null, "company": 101, "department": null, "designation": null, "stakeholder": false, "associatedDeals": [], "createdViaId": "72", "createdViaName": "User", "createdViaType": "Mobile", "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "campaign": null, "source": null, "contactUtms": [], "checkUniqueness": true, "operation": {"executeWorkflow": true, "executeScoreRule": true, "sendNotification": true}, "updateType": "REGULAR", "importedBy": null, "score": null, "maskedFields": null, "name": "natasha"}, {"createdAt": "2019-10-01T06:43:45.717+0000", "updatedAt": "2019-10-01T06:43:45.717+0000", "createdBy": 100, "updatedBy": 100, "id": 1002, "deleted": false, "version": 0, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reassign": false}, "metaData": {"idNameStore": {"updatedBy": {"100": null}, "createdBy": {"100": null}, "ownerId": {"10": "Black Widow"}}}, "tenantId": 100, "ownerId": 10, "customFieldValues": {}, "salutation": null, "firstName": "hulk", "lastName": null, "phoneNumbers": null, "contactPhoneNumbers": [], "emails": null, "dnd": false, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "facebook": null, "twitter": null, "linkedin": null, "company": 101, "department": null, "designation": null, "stakeholder": false, "associatedDeals": [], "createdViaId": "72", "createdViaName": "User", "createdViaType": "Mobile", "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "campaign": null, "source": null, "contactUtms": [], "checkUniqueness": true, "operation": {"executeWorkflow": true, "executeScoreRule": true, "sendNotification": true}, "updateType": "REGULAR", "importedBy": null, "score": null, "maskedFields": null, "name": "hulk"}, {"createdAt": "2019-10-01T05:43:45.717+0000", "updatedAt": "2019-10-01T05:43:45.717+0000", "createdBy": 100, "updatedBy": 100, "id": 1001, "deleted": false, "version": 0, "recordActions": {"read": true, "write": true, "update": true, "delete": true, "email": false, "call": false, "sms": false, "task": true, "note": true, "meeting": false, "document": false, "readAll": true, "updateAll": true, "deleteAll": false, "quotation": false, "reassign": false}, "metaData": {"idNameStore": {"updatedBy": {"100": null}, "createdBy": {"100": null}, "ownerId": {"10": "Black Widow"}}}, "tenantId": 100, "ownerId": 10, "customFieldValues": {}, "salutation": null, "firstName": "tony", "lastName": "stark", "phoneNumbers": null, "contactPhoneNumbers": [], "emails": null, "dnd": false, "timezone": null, "address": null, "city": null, "state": null, "zipcode": null, "country": null, "facebook": null, "twitter": null, "linkedin": null, "company": 101, "department": null, "designation": null, "stakeholder": true, "associatedDeals": [], "createdViaId": "72", "createdViaName": "User", "createdViaType": "Mobile", "updatedViaId": null, "updatedViaName": null, "updatedViaType": null, "campaign": null, "source": null, "contactUtms": [], "checkUniqueness": true, "operation": {"executeWorkflow": true, "executeScoreRule": true, "sendNotification": true}, "updateType": "REGULAR", "importedBy": null, "score": null, "maskedFields": null, "name": "tony stark"}], "last": true, "totalPages": 1, "totalElements": 3, "sort": [{"direction": "DESC", "property": "updatedAt", "ignoreCase": false, "nullHandling": "NATIVE", "ascending": false, "descending": true}], "first": true, "numberOfElements": 3, "size": 3, "number": 0}