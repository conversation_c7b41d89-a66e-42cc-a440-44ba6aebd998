{"createdBy": 1, "updatedBy": 1, "recordActions": {"read": true, "update": true, "delete": false, "email": false, "call": false, "sms": false, "task": false, "note": false, "meeting": false}, "metaData": null, "id": 1, "name": "Deal Pipeline", "entityType": "DEAL", "unqualifiedReasons": ["Wrong number", "Did not pick phone"], "lostReasons": ["Low budget", "Not interested"], "stages": [{"createdBy": 1, "updatedBy": 1, "recordActions": null, "metaData": null, "name": "Open", "description": "When a Deal is just created in the system and there is no activity initiated in order to proceed ahead with the Deal, it’s called Open Deal.", "forecastingType": "OPEN", "position": 1, "winLikelihood": 0}, {"createdBy": 1, "updatedBy": 1, "recordActions": null, "metaData": null, "name": "Won", "description": "When the sale of the product for which the Deal was created has been done successfully, a Deal is considered as Won Deal", "forecastingType": "CLOSED_WON", "position": 2, "winLikelihood": 100}, {"createdBy": 1, "updatedBy": 1, "recordActions": null, "metaData": null, "name": "Closed Unqualified", "description": "When an associated Contact with the Deal is not responding to your email with respect to the Deal or they are no longer interested in the product/service for which the Deal was created, it’s called Unqualified Deal", "forecastingType": "CLOSED_UNQUALIFIED", "position": 3, "winLikelihood": 0}, {"createdBy": 1, "updatedBy": 1, "recordActions": null, "metaData": null, "name": "Closed Lost", "description": "When an associated Contact is not interested in the Product/Service for which the Deal was created or they have finalized their purchase with competitor product/services, then a Deal can be marked as Lost Deal.", "forecastingType": "CLOSED_LOST", "position": 4, "winLikelihood": 0}], "active": false}