[{"createdAt": "2022-12-26T11:53:21.960+0000", "updatedAt": "2023-01-05T12:01:54.662+0000", "createdBy": 10, "updatedBy": 10, "id": 612855, "deleted": false, "version": 8, "recordActions": null, "metaData": null, "tenantId": 1000, "displayName": "Multiii Multiii", "description": null, "type": "MULTI_PICKLIST", "internalType": null, "name": "myMultiPickList", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": true, "length": null, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2022-12-26T11:53:21.960+0000", "updatedAt": "2022-12-26T11:53:21.960+0000", "createdBy": 10, "updatedBy": 10, "id": 33028, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "myMultiPickList", "displayName": "Multiii Multiii", "global": false, "systemDefault": false, "values": [{"createdAt": "2022-12-26T11:53:21.962+0000", "updatedAt": "2022-12-26T11:53:21.962+0000", "createdBy": 10, "updatedBy": 10, "id": 454387, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "AAA", "displayName": "aaa", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.964+0000", "updatedAt": "2022-12-26T11:53:21.964+0000", "createdBy": 10, "updatedBy": 10, "id": 454388, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "DDD", "displayName": "ddd", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.965+0000", "updatedAt": "2022-12-26T11:53:21.965+0000", "createdBy": 10, "updatedBy": 10, "id": 454389, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "FFFF", "displayName": "ffff", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.967+0000", "updatedAt": "2022-12-26T11:53:21.967+0000", "createdBy": 10, "updatedBy": 10, "id": 454390, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "GHHHH", "displayName": "ghhhh", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.967+0000", "updatedAt": "2022-12-26T11:53:21.967+0000", "createdBy": 10, "updatedBy": 10, "id": 454394, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "1991 value", "displayName": "1991 value", "disabled": false}]}, "regex": null}, {"createdAt": "2022-12-15T06:34:30.927+0000", "updatedAt": "2023-01-04T05:12:35.369+0000", "createdBy": 10, "updatedBy": 10, "id": 610722, "deleted": false, "version": 3, "recordActions": null, "metaData": null, "tenantId": 1000, "displayName": "My Custom Text", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "cfCustom", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}]