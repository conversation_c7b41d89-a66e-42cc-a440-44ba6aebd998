[{"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1194, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "First Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "firstName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1195, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Last Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "lastName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Profile Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "profileId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PROFILE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.607+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1197, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Updated By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "updatedBy", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.610+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1198, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Created By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "created<PERSON>y", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.613+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1199, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Owner Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "ownerId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1200, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Salutation", "description": null, "type": "PICK_LIST", "internalType": null, "name": "salutation", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "salutation", "values": [{"id": 473, "name": "MR", "displayName": "Mr", "disabled": false}, {"id": 4490, "name": "MRS", "displayName": "Mrs", "disabled": false}, {"id": 4491, "name": "MISS", "displayName": "Miss", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline", "description": null, "type": "LOOK_UP", "internalType": null, "name": "pipeline", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline Stage", "description": null, "type": "LOOK_UP", "internalType": null, "name": "pipelineStage", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE_STAGE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Products", "description": null, "type": "LOOK_UP", "internalType": "com.sell.entity.core.model.Product", "name": "products", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PRODUCT", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1201, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Industry", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyIndustry", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyIndustry", "values": [{"id": 477, "name": "REAL_ESTATE", "displayName": "REAL_ESTATE", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1202, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Business Type", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyBusinessType", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyBusinessType", "values": [{"id": 478, "name": "real estate", "displayName": "REAL_ESTATE", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1203, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Employees", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyEmployees", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyEmployees", "values": [{"id": 27, "name": "1-10", "displayName": "1-10", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1204, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Source", "description": null, "type": "PICK_LIST", "internalType": null, "name": "source", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "source", "values": [{"id": 66, "name": "Google", "displayName": "Google", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1205, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Campaign", "description": null, "type": "PICK_LIST", "internalType": null, "name": "campaign", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "campaign", "values": [{"id": 55, "name": "ORGANIC", "displayName": "Organic", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1206, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Country", "description": null, "type": "PICK_LIST", "internalType": null, "name": "country", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "country", "values": [{"id": 55, "name": "IN", "displayName": "India", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1207, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Time Zone", "description": null, "type": "PICK_LIST", "internalType": null, "name": "timezone", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "timeZone", "values": [{"id": 77, "name": "Pacific/Midway", "displayName": "Pacific/Midway", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1208, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Requirement Currency", "description": null, "type": "PICK_LIST", "internalType": null, "name": "requirementCurrency", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "requirementCurrency", "values": [{"id": 78, "name": "EUR", "displayName": "Euro", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1209, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Country", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyCountry", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyCountry", "values": [{"id": 79, "name": "IN", "displayName": "India", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1210, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "My MultiPicklist", "description": null, "type": "MULTI_PICKLIST", "internalType": null, "name": "cfMy<PERSON>ultiPicklist", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "MULTI_PICKLIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 108, "name": "cfMy<PERSON>ultiPicklist", "values": [{"id": 201, "name": "ONE", "displayName": "one", "disabled": false}, {"id": 202, "name": "TWO", "displayName": "Two", "disabled": false}, {"id": 203, "name": "THREE", "displayName": "Three", "disabled": true}]}, "regex": null, "active": true}]