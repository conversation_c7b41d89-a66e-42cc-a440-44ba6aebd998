[{"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Profile Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "profileId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PROFILE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.607+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1197, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Updated By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "updatedBy", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.610+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1198, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Created By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "created<PERSON>y", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.613+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1199, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Owner Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "ownerId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1200, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Salutation", "description": null, "type": "PICK_LIST", "internalType": null, "name": "salutation", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "", "values": [{"id": 473, "name": "MR", "displayName": "Mr"}, {"id": 474, "name": "MRS", "displayName": "Mrs"}, {"id": 475, "name": "MISS", "displayName": "Miss"}]}, "regex": null}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1301, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Source", "description": null, "type": "PICK_LIST", "internalType": null, "name": "source", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "", "values": [{"id": 789, "name": "GOOGLE", "displayName": "Google"}, {"id": 790, "name": "GENERIC", "displayName": "Generic"}]}, "regex": null}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1301, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "cf Multi Pick", "description": null, "type": "MULTI_PICKLIST", "internalType": null, "name": "cfMultiPick", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "", "values": [{"id": 123, "name": "GOOGLE", "displayName": "Google"}, {"id": 124, "name": "GENERIC", "displayName": "Generic"}]}, "regex": null}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline", "description": null, "type": "PIPELINE", "internalType": null, "name": "pipeline", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline Stage", "description": null, "type": "LOOK_UP", "internalType": null, "name": "pipelineStage", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE_STAGE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "My City", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "myCity", "entityType": null, "standard": false, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1198, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "My Checkbox", "description": null, "type": "CHECKBOX", "internalType": null, "name": "myCheckBox", "entityType": null, "standard": false, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}]