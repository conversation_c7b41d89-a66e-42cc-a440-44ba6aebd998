[{"createdAt": "2022-12-26T11:53:21.960+0000", "updatedAt": "2023-01-05T12:01:54.662+0000", "createdBy": 10, "updatedBy": 10, "id": 612855, "deleted": false, "version": 8, "recordActions": null, "metaData": null, "tenantId": 1000, "displayName": "Multiii Multiii", "description": null, "type": "MULTI_PICKLIST", "internalType": null, "name": "myMultiPickList", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": true, "length": null, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2022-12-26T11:53:21.960+0000", "updatedAt": "2022-12-26T11:53:21.960+0000", "createdBy": 10, "updatedBy": 10, "id": 33028, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "myMultiPickList", "displayName": "Multiii Multiii", "global": false, "systemDefault": false, "values": [{"createdAt": "2022-12-26T11:53:21.962+0000", "updatedAt": "2022-12-26T11:53:21.962+0000", "createdBy": 10, "updatedBy": 10, "id": 454387, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "AAA", "displayName": "aaa", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.964+0000", "updatedAt": "2022-12-26T11:53:21.964+0000", "createdBy": 10, "updatedBy": 10, "id": 454388, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "DDD", "displayName": "ddd", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.965+0000", "updatedAt": "2022-12-26T11:53:21.965+0000", "createdBy": 10, "updatedBy": 10, "id": 454389, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "FFFF", "displayName": "ffff", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.967+0000", "updatedAt": "2022-12-26T11:53:21.967+0000", "createdBy": 10, "updatedBy": 10, "id": 454390, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "GHHHH", "displayName": "ghhhh", "disabled": false}, {"createdAt": "2022-12-26T11:53:21.967+0000", "updatedAt": "2022-12-26T11:53:21.967+0000", "createdBy": 10, "updatedBy": 10, "id": 454394, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "name": "1991 value", "displayName": "1991 value", "disabled": false}]}, "regex": null}, {"createdAt": "2022-12-15T06:34:30.927+0000", "updatedAt": "2023-01-04T05:12:35.369+0000", "createdBy": 10, "updatedBy": 10, "id": 610722, "deleted": false, "version": 3, "recordActions": null, "metaData": null, "tenantId": 1000, "displayName": "My Custom Text", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "cfCustom", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1201, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Industry", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyIndustry", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyIndustry", "values": [{"id": 477, "name": "REAL_ESTATE", "displayName": "REAL_ESTATE", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1202, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Business Type", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyBusinessType", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyBusinessType", "values": [{"id": 478, "name": "real estate", "displayName": "REAL_ESTATE", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1203, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Company Employees", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyEmployees", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "companyEmployees", "values": [{"id": 27, "name": "1-10", "displayName": "1-10", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1204, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Source", "description": null, "type": "PICK_LIST", "internalType": null, "name": "source", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "source", "values": [{"id": 66, "name": "Google", "displayName": "Google", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1205, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Campaign", "description": null, "type": "PICK_LIST", "internalType": null, "name": "campaign", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "campaign", "values": [{"id": 55, "name": "ORGANIC", "displayName": "Organic", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1206, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Country", "description": null, "type": "PICK_LIST", "internalType": null, "name": "companyCountry", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "country", "values": [{"id": 550, "name": "IN", "displayName": "India", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1207, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Time Zone", "description": null, "type": "PICK_LIST", "internalType": null, "name": "timezone", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "timeZone", "values": [{"id": 77, "name": "Pacific/Midway", "displayName": "Pacific/Midway", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1200, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Salutation", "description": null, "type": "PICK_LIST", "internalType": null, "name": "salutation", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "salutation", "values": [{"id": 473, "name": "MR", "displayName": "Mr", "disabled": false}, {"id": 4490, "name": "MRS", "displayName": "Mrs", "disabled": false}, {"id": 4491, "name": "MISS", "displayName": "Miss", "disabled": false}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1208, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Requirement Currency", "description": null, "type": "PICK_LIST", "internalType": null, "name": "requirementCurrency", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 88, "name": "requirementCurrency", "values": [{"id": 78, "name": "EUR", "displayName": "Euro", "disabled": false}]}, "regex": null, "active": true}]