[{"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:21.205+0000", "createdBy": 7, "updatedBy": 7, "id": 220, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Salutation", "description": null, "type": "PICK_LIST", "internalType": null, "name": "salutation", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:20.078+0000", "createdBy": 7, "updatedBy": 7, "id": 24, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"id": 546, "name": "MR", "displayName": "Mr", "disabled": false}, {"id": 742, "name": "MRS", "displayName": "Mrs", "disabled": false}, {"id": 743, "name": "MISS", "displayName": "Miss", "disabled": false}]}, "regex": null}, {"createdAt": "2020-09-25T07:23:20.092+0000", "updatedAt": "2021-03-08T07:12:35.402+0000", "createdBy": 7, "updatedBy": 7, "id": 221, "deleted": false, "version": 2, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "First Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "firstName", "entityType": null, "standard": true, "sortable": true, "filterable": false, "required": false, "important": true, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.094+0000", "updatedAt": "2020-09-25T07:23:21.205+0000", "createdBy": 7, "updatedBy": 7, "id": 222, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Last Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "lastName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "important": true, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.095+0000", "updatedAt": "2020-09-25T07:23:21.205+0000", "createdBy": 7, "updatedBy": 7, "id": 223, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Phone Numbers", "description": null, "type": "PHONE", "internalType": null, "name": "phoneNumbers", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": true, "active": true, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.097+0000", "updatedAt": "2020-09-25T07:23:21.206+0000", "createdBy": 7, "updatedBy": 7, "id": 224, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Emails", "description": null, "type": "EMAIL", "internalType": null, "name": "emails", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": true, "active": true, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.098+0000", "updatedAt": "2020-09-25T07:23:21.206+0000", "createdBy": 7, "updatedBy": 7, "id": 225, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Do Not Disturb", "description": null, "type": "TOGGLE", "internalType": null, "name": "dnd", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.099+0000", "updatedAt": "2020-09-25T07:23:21.206+0000", "createdBy": 7, "updatedBy": 7, "id": 226, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Timezone", "description": null, "type": "PICK_LIST", "internalType": null, "name": "timezone", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-09-25T05:51:11.503+0000", "updatedAt": "2022-03-25T05:52:26.085+0000", "createdBy": 0, "updatedBy": 71, "id": 3, "deleted": false, "version": 3, "recordActions": null, "metaData": null, "tenantId": 0, "name": "TIMEZONE", "displayName": "Timezone", "global": true, "systemDefault": true, "values": [{"id": 317, "name": "Etc/GMT+12", "displayName": "(GMT-12:00) International Date Line West", "disabled": false}, {"id": 318, "name": "Pacific/Midway", "displayName": "(GMT-11:00) Midway Island, Samoa", "disabled": false}, {"id": 319, "name": "Pacific/Honolulu", "displayName": "(GMT-10:00) Hawaii", "disabled": false}]}, "regex": null}, {"createdAt": "2020-09-25T07:23:20.100+0000", "updatedAt": "2020-09-25T07:23:21.206+0000", "createdBy": 7, "updatedBy": 7, "id": 227, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Address", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "address", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.102+0000", "updatedAt": "2020-09-25T07:23:21.206+0000", "createdBy": 7, "updatedBy": 7, "id": 228, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "City", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "city", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.103+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 229, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "State", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "state", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.104+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 230, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Zipcode", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "zipcode", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.105+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 231, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Country", "description": null, "type": "PICK_LIST", "internalType": null, "name": "country", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-09-25T05:51:11.503+0000", "updatedAt": "2022-05-23T08:39:06.679+0000", "createdBy": 0, "updatedBy": 71, "id": 2, "deleted": false, "version": 7, "recordActions": null, "metaData": null, "tenantId": 0, "name": "COUNTRY", "displayName": "Country", "global": true, "systemDefault": true, "values": [{"id": 73, "name": "BS", "displayName": "Afghanistan", "disabled": false}, {"id": 74, "name": "AX", "displayName": "Åland Islands", "disabled": false}, {"id": 75, "name": "AL", "displayName": "Albania", "disabled": false}]}, "regex": null}, {"createdAt": "2020-09-25T07:23:20.106+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 232, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Facebook", "description": null, "type": "URL", "internalType": null, "name": "facebook", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.108+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 233, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Twitter", "description": null, "type": "URL", "internalType": null, "name": "twitter", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.109+0000", "updatedAt": "2020-09-25T07:23:21.207+0000", "createdBy": 7, "updatedBy": 7, "id": 234, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Linkedin", "description": null, "type": "URL", "internalType": null, "name": "linkedin", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.110+0000", "updatedAt": "2022-07-26T05:48:27.832+0000", "createdBy": 7, "updatedBy": 7, "id": 235, "deleted": false, "version": 5, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Company Name", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "company", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": true, "important": true, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "COMPANY", "internal": false, "lookupUrl": "/companies/lookup?q=name:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.111+0000", "updatedAt": "2020-09-25T07:23:21.208+0000", "createdBy": 7, "updatedBy": 7, "id": 236, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Department", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "department", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.112+0000", "updatedAt": "2020-09-25T07:23:21.208+0000", "createdBy": 7, "updatedBy": 7, "id": 237, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Designation", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "designation", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.113+0000", "updatedAt": "2020-09-25T07:23:21.208+0000", "createdBy": 7, "updatedBy": 7, "id": 238, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Decision maker", "description": null, "type": "TOGGLE", "internalType": null, "name": "stakeholder", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.116+0000", "updatedAt": "2020-09-25T07:23:21.309+0000", "createdBy": 7, "updatedBy": 7, "id": 241, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Owner", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "ownerId", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.117+0000", "updatedAt": "2020-09-25T07:23:21.309+0000", "createdBy": 7, "updatedBy": 7, "id": 242, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Created At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "createdAt", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.118+0000", "updatedAt": "2020-09-25T07:23:21.309+0000", "createdBy": 7, "updatedBy": 7, "id": 243, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Updated At", "description": null, "type": "DATETIME_PICKER", "internalType": null, "name": "updatedAt", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.120+0000", "updatedAt": "2020-09-25T07:23:21.309+0000", "createdBy": 7, "updatedBy": 7, "id": 244, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Created By", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "created<PERSON>y", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.121+0000", "updatedAt": "2020-09-25T07:23:21.309+0000", "createdBy": 7, "updatedBy": 7, "id": 245, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Updated By", "description": null, "type": "LOOK_UP", "internalType": "java.math.BigDecimal", "name": "updatedBy", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": "/users/lookup?q=firstName:", "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2021-03-08T07:13:57.290+0000", "updatedAt": "2022-06-16T10:52:28.700+0000", "createdBy": 7, "updatedBy": 7, "id": 17672, "deleted": false, "version": 2, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "myText", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "mytext", "entityType": null, "standard": false, "sortable": false, "filterable": false, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2021-03-08T10:23:57.699+0000", "updatedAt": "2021-03-08T10:24:30.087+0000", "createdBy": 7, "updatedBy": 7, "id": 17799, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "mytext1", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "mytext1", "entityType": null, "standard": false, "sortable": true, "filterable": false, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2021-05-17T09:21:22.452+0000", "updatedAt": "2021-05-17T09:21:22.452+0000", "createdBy": 7, "updatedBy": 7, "id": 34957, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "my field", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "my<PERSON>ield", "entityType": null, "standard": false, "sortable": false, "filterable": false, "required": false, "important": false, "active": true, "multiValue": false, "length": 255, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2021-09-27T16:04:46.833+0000", "updatedAt": "2021-09-27T16:04:46.833+0000", "createdBy": 7, "updatedBy": 7, "id": 64293, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "My Picklist", "description": null, "type": "PICK_LIST", "internalType": null, "name": "myPicklist", "entityType": null, "standard": false, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2021-09-27T16:04:46.833+0000", "updatedAt": "2021-09-27T16:04:46.833+0000", "createdBy": 7, "updatedBy": 7, "id": 4713, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "myPicklist", "displayName": "My Picklist", "global": false, "systemDefault": false, "values": [{"createdAt": "2021-09-27T16:04:46.836+0000", "updatedAt": "2022-08-10T05:10:11.775+0000", "createdBy": 7, "updatedBy": 7, "id": 18461, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "name": "ONE", "displayName": "one", "disabled": true}, {"createdAt": "2021-09-27T16:04:46.839+0000", "updatedAt": "2021-09-27T16:04:46.839+0000", "createdBy": 7, "updatedBy": 7, "id": 18462, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "TWO", "displayName": "two", "disabled": false}, {"createdAt": "2021-09-27T16:04:46.840+0000", "updatedAt": "2021-09-27T16:04:46.840+0000", "createdBy": 7, "updatedBy": 7, "id": 18463, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "THREE", "displayName": "three", "disabled": false}]}, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 429973, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Created Via Id", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 430832, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Created Via Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 432321, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Created Via Type", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "createdViaType", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 433578, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Updated Via Id", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 436181, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Updated Via Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"createdAt": "2022-06-24T08:26:09.639+0000", "updatedAt": "2022-06-24T08:26:09.639+0000", "createdBy": 7, "updatedBy": 7, "id": 437175, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Updated Via Type", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "updatedViaType", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": true, "lookupUrl": null, "skipIdNameResolution": true, "picklist": null, "regex": null}, {"deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 1000, "displayName": "myName", "description": "my<PERSON>ame added", "type": "TEXT_FIELD", "internalType": null, "name": "myName", "entityType": null, "standard": false, "sortable": false, "filterable": false, "required": false, "active": true, "important": false, "multiValue": false, "length": null, "isUnique": false, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null}, {"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:21.205+0000", "createdBy": 7, "updatedBy": 7, "id": 437176, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Campaign", "description": null, "type": "PICK_LIST", "internalType": null, "name": "campaign", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:20.078+0000", "createdBy": 7, "updatedBy": 7, "id": 25, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"id": 55, "name": "ORGANIC", "displayName": "Organic", "disabled": false}, {"id": 33, "name": "VALUE", "displayName": "Value", "disabled": false}]}, "regex": null}, {"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:21.205+0000", "createdBy": 7, "updatedBy": 7, "id": 437177, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 7, "displayName": "Source", "description": null, "type": "PICK_LIST", "internalType": null, "name": "source", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "important": false, "active": true, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-09-25T07:23:20.078+0000", "updatedAt": "2020-09-25T07:23:20.078+0000", "createdBy": 7, "updatedBy": 7, "id": 26, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 7, "name": "", "displayName": null, "global": false, "systemDefault": false, "values": [{"id": 66, "name": "FACEBOOK", "displayName": "Facebook", "disabled": false}, {"id": 22, "name": "COLD-CALLING", "displayName": "Cold-calling", "disabled": false}]}, "regex": null}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 437178, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "My MultiPicklist", "description": null, "type": "MULTI_PICKLIST", "internalType": null, "name": "cfMy<PERSON>ultiPicklist", "entityType": null, "standard": false, "sortable": false, "filterable": true, "required": false, "multiValue": true, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "MULTI_PICKLIST", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"id": 27, "name": "cfMy<PERSON>ultiPicklist", "values": [{"id": 1001, "name": "HERALD", "displayName": "Herald", "disabled": false}, {"id": 1002, "name": "ANTHONY", "displayName": "<PERSON>", "disabled": false}, {"id": 1003, "name": "LEO", "displayName": "<PERSON>", "disabled": true}]}, "regex": null, "active": true}]