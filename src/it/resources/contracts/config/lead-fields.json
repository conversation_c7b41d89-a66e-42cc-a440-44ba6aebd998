[{"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1194, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "First Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "firstName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1195, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Last Name", "description": null, "type": "TEXT_FIELD", "internalType": null, "name": "lastName", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": 255, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Profile Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "profileId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PROFILE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.607+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1197, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Updated By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "updatedBy", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.610+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1198, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Created By", "description": null, "type": "LOOK_UP", "internalType": null, "name": "created<PERSON>y", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.613+0000", "updatedAt": "2020-02-27T11:08:09.612+0000", "createdBy": 7, "updatedBy": 7, "id": 1199, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Owner Id", "description": null, "type": "LOOK_UP", "internalType": null, "name": "ownerId", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "USER", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.616+0000", "updatedAt": "2020-02-27T11:08:09.613+0000", "createdBy": 7, "updatedBy": 7, "id": 1200, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Salutation", "description": null, "type": "PICK_LIST", "internalType": null, "name": "salutation", "entityType": null, "standard": true, "sortable": false, "filterable": true, "required": false, "multiValue": null, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PICK_LIST", "internal": true, "lookupUrl": "/users/lookup?q=name:", "skipIdNameResolution": false, "picklist": {"id": 88, "name": "salutation", "picklistValues": [{"id": 4489, "name": null, "displayName": "Mr"}, {"id": 4490, "name": null, "displayName": "Mrs"}, {"id": 4491, "name": null, "displayName": "Miss"}]}, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline", "description": null, "type": "LOOK_UP", "internalType": null, "name": "pipeline", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Pipeline Stage", "description": null, "type": "LOOK_UP", "internalType": null, "name": "pipelineStage", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PIPELINE_STAGE", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1196, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Products", "description": null, "type": "LOOK_UP", "internalType": null, "name": "products", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": "PRODUCT", "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}, {"createdAt": "2020-04-27T06:28:03.909+0000", "updatedAt": "2020-04-27T06:28:03.909+0000", "createdBy": 9998, "updatedBy": 9998, "id": 1197, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 9998, "displayName": "Country", "description": "", "type": "PICK_LIST", "internalType": null, "name": "country", "entityType": "LEAD", "standard": true, "sortable": false, "filterable": false, "required": false, "important": false, "active": true, "multiValue": false, "length": null, "isUnique": false, "greaterThan": "", "lessThan": "", "lookupForEntity": "", "internal": true, "lookupUrl": null, "skipIdNameResolution": false, "picklist": {"createdAt": "2020-04-27T06:28:03.909+0000", "updatedAt": "2020-04-27T06:28:03.909+0000", "createdBy": 9998, "updatedBy": 9998, "id": 100, "deleted": false, "version": 0, "recordActions": null, "metaData": null, "tenantId": 9998, "name": "COUNTRY", "displayName": "Country", "global": false, "systemDefault": true, "values": [{"createdAt": "2020-04-27T06:28:03.909+0000", "updatedAt": "2020-04-27T06:28:03.909+0000", "createdBy": 9998, "updatedBy": 9998, "id": 4891, "deleted": false, "version": 1, "recordActions": null, "metaData": {"idNameStore": {}}, "tenantId": 9998, "name": "IN", "displayName": "India", "disabled": false}, {"createdAt": "2020-04-27T06:28:03.909+0000", "updatedAt": "2020-04-27T06:28:03.909+0000", "createdBy": 9998, "updatedBy": 9998, "id": 4892, "deleted": false, "version": 1, "recordActions": null, "metaData": {"idNameStore": {}}, "tenantId": 9998, "name": "FR", "displayName": "France", "disabled": false}]}, "regex": ""}, {"createdAt": "2020-02-27T11:08:06.604+0000", "updatedAt": "2020-02-27T11:08:09.475+0000", "createdBy": 7, "updatedBy": 7, "id": 1198, "deleted": false, "version": 1, "recordActions": null, "metaData": null, "tenantId": 8, "displayName": "Phone", "description": null, "type": "PHONE", "internalType": null, "name": "phoneNumbers", "entityType": null, "standard": true, "sortable": true, "filterable": true, "required": true, "multiValue": false, "length": null, "isUnique": null, "greaterThan": null, "lessThan": null, "lookupForEntity": null, "internal": false, "lookupUrl": null, "skipIdNameResolution": false, "picklist": null, "regex": null, "active": true}]