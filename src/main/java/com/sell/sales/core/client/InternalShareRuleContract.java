package com.sell.sales.core.client;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.PermissionAction;
import com.sell.sales.core.dto.AccessDTO;
import javax.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * The interface Internal share rule client.
 */
@RequestMapping("/v1/internal/share")
public interface InternalShareRuleContract {

  @PostMapping(value = "/{entityType}/{entityId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  ShareRuleResponse createShareRule(@PathVariable("entityType") EntityType entityType, @PathVariable("entityId") Long entityId,
                                    @Valid @RequestBody CreateShareRuleRequest createShareRuleRequest);

  @PostMapping(value = "/{entityType}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  ShareRuleResponse createShareRuleForAllEntityTypeRecords(@PathVariable("entityType") EntityType entityType,
                                                           @Valid @RequestBody CreateShareRuleRequest createShareRuleRequest);

  @GetMapping(value = "/{entityType}/{shareRuleId}", produces = MediaType.APPLICATION_JSON_VALUE)
  ShareRuleResponse getShareRule(@PathVariable("entityType") EntityType entityType, @PathVariable("shareRuleId") Long shareRuleId);

  @PutMapping(value = "/{entityType}/{shareRuleId}/{entityId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  ShareRuleResponse updateShareRule(@PathVariable("entityType") EntityType entityType, @PathVariable("entityId") Long entityId,
                                    @PathVariable("shareRuleId") Long shareRuleId,
                                    @Valid @RequestBody UpdateShareRuleRequest updateShareRuleRequest);


  @PutMapping(value = "/{entityType}/{shareRuleId}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  ShareRuleResponse updateShareRuleForAllEntityTypeRecords(@PathVariable("entityType") EntityType entityType,
                                                           @PathVariable("shareRuleId") Long shareRuleId,
                                                           @Valid @RequestBody UpdateShareRuleRequest updateShareRuleRequest);

  @DeleteMapping(value = "/{entityType}/{shareRuleId}")
  void deleteShareRule(@PathVariable("entityType") EntityType entityType, @PathVariable(value = "shareRuleId") Long shareRuleId);

  @GetMapping(value = "/access/{entityType}/{permissionAction}", produces = MediaType.APPLICATION_JSON_VALUE)
  AccessDTO resolveAccess(@PathVariable("entityType") EntityType entityType, @PathVariable("permissionAction") PermissionAction permissionAction);
}
