package com.sell.sales.core.client;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.PermissionAction;
import com.sell.sales.core.dto.AccessDTO;
import com.sell.sales.core.exception.APIException;
import com.sell.sales.core.utils.LogMarker;
import com.sell.sales.domain.Contact;
import com.sell.sales.domain.Lead;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.repository.ContactRepository;
import com.sell.sales.repository.LeadRepository;
import feign.Feign.Builder;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Hidden
public class InternalShareRuleService implements InternalShareRuleContract, EntityShareAccessResolver {

  private final InternalShareRuleContract client;

  private final LeadRepository leadRepository;

  private final ContactRepository contactRepository;

  public InternalShareRuleService(String clientBasePath, Builder clientBuilder, LeadRepository leadRepository, ContactRepository contactRepository) {
    this.leadRepository = leadRepository;
    this.contactRepository = contactRepository;
    client = clientBuilder.target(InternalShareRuleContract.class, clientBasePath);
  }

  @Override
  public ShareRuleResponse createShareRule(EntityType entityType, Long entityId, CreateShareRuleRequest createShareRuleRequest) {
    if (entityId == null) {
      return client.createShareRuleForAllEntityTypeRecords(entityType, createShareRuleRequest);
    }
    validateShareToOwner(entityType, entityId, createShareRuleRequest.getToId());
    return client.createShareRule(entityType, entityId, createShareRuleRequest);
  }

  @Override
  public ShareRuleResponse createShareRuleForAllEntityTypeRecords(EntityType entityType, CreateShareRuleRequest createShareRuleRequest) {
    return null;
  }

  public ShareRuleResponse getShareRule(EntityType entityType, Long shareRuleId) {
    return client.getShareRule(entityType, shareRuleId);
  }

  public ShareRuleResponse updateShareRule(EntityType entityType, Long entityId, Long shareRuleId, UpdateShareRuleRequest updateShareRuleRequest) {
    validateShareToOwner(entityType, entityId, updateShareRuleRequest.getToId());
    return client.updateShareRule(entityType, entityId, shareRuleId, updateShareRuleRequest);
  }

  public ShareRuleResponse updateShareRuleForAllEntityTypeRecords(EntityType entityType, Long shareRuleId,
      UpdateShareRuleRequest updateShareRuleRequest) {
    return client.updateShareRuleForAllEntityTypeRecords(entityType, shareRuleId, updateShareRuleRequest);
  }


  public void deleteShareRule(EntityType entityType, Long shareRuleId) {
    client.deleteShareRule(entityType, shareRuleId);
  }

  @Override
  public AccessDTO resolveAccess(EntityType entityType, PermissionAction permissionAction) {
    try {
      return client.resolveAccess(entityType, permissionAction);
    } catch (APIException e) {
      log.error(LogMarker.INTER_SERVICE_CALL, "Could not resolve shareRule access, shareRule would not apply", e);
      return null;
    }
  }

  void validateShareToOwner(EntityType entityType, Long entityId, Long toId) {
    if (entityType.isSharable() && entityType.getLabel().equalsIgnoreCase("Lead")) {
      Lead lead = leadRepository.findOne(entityId);
      if (Objects.equals(lead.getOwnerId(), toId)) {
        throw new SalesException(SalesErrorCodes.SHARE_TO_OWNER_NOT_POSSIBLE);
      }
    }

    if (entityType.isSharable() && entityType.getLabel().equalsIgnoreCase("Contact")) {
      Contact contact = contactRepository.findById(entityId);
      if (Objects.equals(contact.getOwnerId(), toId)) {
        throw new SalesException(SalesErrorCodes.SHARE_TO_OWNER_NOT_POSSIBLE);
      }
    }
  }
}
