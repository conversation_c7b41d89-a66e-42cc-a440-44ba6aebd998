package com.sell.sales.core.client;

import com.sell.sales.core.domain.EntityType;

import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

public abstract class SharableEntityController {
  
  private InternalShareRuleService client;

  protected abstract EntityType getEntityType();

  protected SharableEntityController(InternalShareRuleService client) {
    this.client = client;
  }
  
  @Operation(summary = "Create an ACL share rule for all entityType records")
  @PostMapping(value = "/share", produces = MediaType.APPLICATION_JSON_VALUE)
  public ShareRuleResponse createShareRuleForAllEntityTypeRecord(@Valid @RequestBody CreateShareRuleRequest createShareRuleRequest) {
    return client.createShareRule(getEntityType(), null, createShareRuleRequest);
  }

  @Operation(summary = "Create an ACL share rule for record Id")
  @PostMapping(value = "/{entityId}/share", produces = MediaType.APPLICATION_JSON_VALUE)
  public ShareRuleResponse createShareRule(@Valid @RequestBody CreateShareRuleRequest createShareRuleRequest,
                                            @PathVariable("entityId") Long entityId) {
    return client.createShareRule(getEntityType(), entityId, createShareRuleRequest);
  }

  @Operation(summary = "Get an ACL share rule")
  @GetMapping(value = "/share/{shareRuleId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public ShareRuleResponse getShareRule(@PathVariable("shareRuleId") Long shareRuleId) {
    return client.getShareRule(getEntityType(), shareRuleId);
  }

  @Operation(summary = "Update a shareRule")
  @PutMapping(value = "/{shareRuleId}/share/{entityId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public ShareRuleResponse updateShareRule(@PathVariable("shareRuleId") Long shareRuleId, @PathVariable("entityId") Long entityId,
                                            @Valid @RequestBody UpdateShareRuleRequest updateShareRuleRequest) {
    return client.updateShareRule(getEntityType(), shareRuleId, entityId, updateShareRuleRequest);
  }

  @Operation(summary = "Update a shareRule for All entityType records")
  @PutMapping(value = "/share/{shareRuleId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public ShareRuleResponse updateShareRuleForAllEntityTypeRecords(@PathVariable("shareRuleId") Long shareRuleId,
                                                                   @Valid @RequestBody UpdateShareRuleRequest updateShareRuleRequest) {
    return client.updateShareRuleForAllEntityTypeRecords(getEntityType(), shareRuleId, updateShareRuleRequest);
  }

  @Operation(summary = "Delete a shareRule")
  @DeleteMapping(value = "/share/{shareRuleId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public void deleteShareRule(@PathVariable(value = "shareRuleId") Long shareRuleId) {
    client.deleteShareRule(getEntityType(), shareRuleId);
  }
}
