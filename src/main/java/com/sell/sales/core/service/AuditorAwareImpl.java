package com.sell.sales.core.service;


import com.sell.sales.core.exception.BaseException;
import com.sell.sales.core.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.AuditorAware;

/**
 * Created by hemants on 08/03/19.
 * This class will be used to add createdBy and modifiedBy field for the entities.
 */
@Slf4j
public class AuditorAwareImpl implements AuditorAware<Long> {

    /**
     * Return currently logged in user.
     * @return String
     */
    @Override
    public Long getCurrentAuditor() {
        try {
            return SecurityUtil.getUserId();
        }catch(BaseException e){
            log.warn("Error while fetching logged-in user. This message ideally only comes during user sign-up");
        }
        return null;
    }
}
