package com.sell.sales.core.filter;


import com.sell.sales.core.domain.CoreAccessToken;
import com.sell.sales.core.domain.CoreAuthentication;
import com.sell.sales.core.utils.JwtTokenConverter;
import io.jsonwebtoken.MalformedJwtException;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Created by hemants on 04/01/19.
 */
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

  private JwtTokenConverter jwtTokenConverter;

  public void setJwtTokenConverter(JwtTokenConverter jwtTokenConverter) {
    this.jwtTokenConverter = jwtTokenConverter;
  }

  public JwtAuthenticationTokenFilter jwtTokenConverter(JwtTokenConverter jwtTokenConverter) {
    this.jwtTokenConverter = jwtTokenConverter;
    return this;
  }

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
    if (!request.getRequestURI().contains("/v1/tokens")) {
      String jwtToken = null;
      if (request.getParameter("token") != null) {
        log.debug("token is present in query parameter");
        jwtToken = request.getParameter("token");
      }

      String authorizationHeader = request.getHeader("Authorization");
      if (authorizationHeader != null && authorizationHeader.length() > 0) {
        try {
          jwtToken = authorizationHeader.split("Bearer ")[1];
          log.debug("token is present in header");
        } catch (Exception e) {
          log.warn("got error while parsing token in header for request " + request.getRequestURI(), e);
        }
      }

      if (jwtToken != null) {
        try {
          CoreAccessToken accessToken = jwtTokenConverter.getAccessTokenFromJwt(jwtToken);
          CoreAuthentication oauth2Authentication = new CoreAuthentication(accessToken.getUserId(),
              accessToken.getTenantId(),
              accessToken.getPermissions(),
              jwtToken);
          SecurityContextHolder.getContext().setAuthentication(oauth2Authentication);
          addToLogContext(accessToken);
        } catch (MalformedJwtException exception) {
          log.warn("jwt token is malformed so ignoring it for request " + request.getRequestURI(), exception);
        }
      }
    }
    chain.doFilter(request, response);
  }

  private void addToLogContext(CoreAccessToken accessToken) {
    MDC.put("user.tenant.id", accessToken.getTenantId());
    MDC.put("user.id", accessToken.getUserId());
  }
}
