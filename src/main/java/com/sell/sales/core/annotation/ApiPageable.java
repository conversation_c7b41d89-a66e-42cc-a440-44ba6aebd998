package com.sell.sales.core.annotation;

import io.swagger.v3.oas.annotations.Parameter;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.METHOD, ElementType.ANNOTATION_TYPE, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiPageable {

  @Parameter(name = "page", description = "Results page you want to retrieve (0..N)", required = false, example = "0")
  int page() default 0;

  @Parameter(name = "size", description = "Number of records per page", required = false, example = "20")
  int size() default 20;

  @Parameter(name = "sort", description = "Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.",
      required = false, example = "property,asc")
  String[] sort() default {};
}

