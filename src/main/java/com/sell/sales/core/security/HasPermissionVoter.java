package com.sell.sales.core.security;

import com.sell.sales.core.domain.CoreAuthentication;
import com.sell.sales.core.utils.PermissionUtil;
import java.util.Collection;
import java.util.List;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.core.Authentication;

/**
 * Created by hemants on 11/01/19. The voter class which based upon user permission and attribute present on method's @secure, will vote if user
 * should allow to access the resource or not.
 */
public class HasPermissionVoter implements AccessDecisionVoter<Object> {

  @Override
  public boolean supports(ConfigAttribute attribute) {
    return true;
  }

  @Override
  public boolean supports(Class<?> clazz) {
    return true;
  }

  /**
   * It checks the user's permission and permission declared on method. If user has valid permission he is allowed to access the method
   *
   * @param authentication
   * @param object
   * @param attributes
   * @return
   */
  @Override
  public int vote(Authentication authentication, Object object, Collection<ConfigAttribute> attributes) {
    if (authentication == null)
      return ACCESS_DENIED;

    if (authentication instanceof CoreAuthentication) {
      CoreAuthentication auth = (CoreAuthentication) authentication;
      List<String> userScopes = PermissionUtil.getAllValidPermissionScopes(auth);

      for (ConfigAttribute attribute : attributes) {
        if ((attribute instanceof SecurityConfig) && this.supports(attribute)) {
          // Attempt to find a matching granted authority
          for (String scope : userScopes) {
            if (attribute.getAttribute().equals(scope))
              return ACCESS_GRANTED;
          }
        }
      }
    }

    return ACCESS_ABSTAIN;
  }
}
