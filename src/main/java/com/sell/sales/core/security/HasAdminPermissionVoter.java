package com.sell.sales.core.security;

import com.sell.sales.core.domain.CoreAuthentication;
import com.sell.sales.core.utils.PermissionUtil;
import java.util.Collection;
import java.util.List;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.core.Authentication;

/**
 * The voter class which based upon user permission and attribute present on method's @secure, will vote if user should allow to access the resource.
 */
public class Has<PERSON>d<PERSON>Per<PERSON>Voter implements AccessDecisionVoter<Object> {

  @Override
  public boolean supports(ConfigAttribute attribute) {
    return attribute.getAttribute().matches(".*_read$") || attribute.getAttribute().matches(".*_update$");
  }

  @Override
  public boolean supports(Class<?> clazz) {
    return true;
  }

  /**
   * It checks the user's permission and permission declared on method. If user has valid permission he is allowed to access the method
   *
   * @param authentication
   * @param object
   * @param attributes
   * @return
   */
  @Override
  public int vote(Authentication authentication, Object object, Collection<ConfigAttribute> attributes) {
    if (authentication == null)
      return ACCESS_DENIED;

    int result = ACCESS_ABSTAIN;
    if (authentication instanceof CoreAuthentication) {
      CoreAuthentication auth = (CoreAuthentication) authentication;
      List<String> userScopes = PermissionUtil.getAllValidPermissionScopes(auth);

      for (ConfigAttribute attribute : attributes) {
        if ((attribute instanceof SecurityConfig) && this.supports(attribute)) {
          result = ACCESS_DENIED;
          // Attempt to find a matching granted authority
          for (String scope : userScopes) {
            if (attribute.getAttribute().concat("All").equals(scope))
              return ACCESS_GRANTED;
          }
        }
      }
    }

    return result;
  }
}
