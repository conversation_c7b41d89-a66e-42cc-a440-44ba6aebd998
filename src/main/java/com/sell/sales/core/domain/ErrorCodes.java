package com.sell.sales.core.domain;

/**
 * Created by hemants on 18/01/19. Standard errorcodes for managing error code and message centrally.
 */
public class ErrorCodes {
  private static final String CODE_PREFIX = "000";

  public static final ErrorResource COMMON_INTERNAL_ERROR = new ErrorResource(CODE_PREFIX + "000", "common.internal.error");

  public static final ErrorResource COMMON_INVALID_TOKEN = new ErrorResource(CODE_PREFIX + "001", "common.invalid.token");
  public static final ErrorResource TOKEN_EXPIRED_ERROR = new ErrorResource(CODE_PREFIX + "002", "token.expired");

  public static final ErrorResource COMMON_UNAUTH_ACTION = new ErrorResource(CODE_PREFIX + "003", "common.unauth.action");
  public static final ErrorResource COMMON_PERMISSION_ERROR = new ErrorResource(CODE_PREFIX + "004", "common.permission.error");
  public static final ErrorResource COMMON_RECORD_PERMISSION_ERROR = new ErrorResource(CODE_PREFIX + "005", "common.record.permission.error");

  public static final ErrorResource COMMON_PERMISSION_ERROR_STATE = new ErrorResource(CODE_PREFIX + "006", "common.permission.error.state");
  public static final ErrorResource COMMON_OWNER_NOT_APPLICABLE = new ErrorResource(CODE_PREFIX + "007", "common.owner.not.applicable");
  public static final ErrorResource COMMON_METHOD_NOT_SUPPORTED = new ErrorResource(CODE_PREFIX + "008", "common.method.not.supported");
  public static final ErrorResource COMMON_VALIDATION_ERROR = new ErrorResource(CODE_PREFIX + "009", "common.validation.error");

  public static final ErrorResource COMMON_RESOURCE_NOT_FOUND = new ErrorResource(CODE_PREFIX + "010", "common.resource.not.found");
  public static final ErrorResource COMMON_DUPLICATE_RESOURCE = new ErrorResource(CODE_PREFIX + "011", "common.duplicate.resource");
  public static final ErrorResource COMMON_DUPLICATE_RESOURCE_DETAILED =
      new ErrorResource(COMMON_DUPLICATE_RESOURCE.getCode(), "common.duplicate.resource.detailed");
  public static final ErrorResource COMMON_INVALID_RESOURCE_TYPE = new ErrorResource(CODE_PREFIX + "012", "common.invalid.resource.type");
  public static final ErrorResource COMMON_INACTIVE_RESOURCE = new ErrorResource(CODE_PREFIX + "013", "common.inactive.resource");
  public static final ErrorResource COMMON_INVALID_RESOURCE = new ErrorResource(CODE_PREFIX + "014", "common.invalid.resource");
  public static final ErrorResource COMMON_INVALID_SHARE_RECORD = new ErrorResource(CODE_PREFIX + "015", "common.invalid.share.record");
  public static final ErrorResource COMMON_UNIQUENESS_CONFIG_DUPLICATES =
      new ErrorResource(CODE_PREFIX + "016", "common.uniqueness.config.duplicates");
  public static final ErrorResource COMMON_READ_PERMISSION_ERROR = new ErrorResource(CODE_PREFIX + "017", "common.read.permission.error");

  public static final ErrorResource INVALID_PERMISSION_ACTION = new ErrorResource(CODE_PREFIX + "018", "invalid.permission.action");
  public static final ErrorResource INVALID_PHONE_NUMBER = new ErrorResource(CODE_PREFIX + "019", "phone.number.with.id.not.present");

}
