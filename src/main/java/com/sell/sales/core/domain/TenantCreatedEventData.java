package com.sell.sales.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.core.domain.PermissionDTO;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * Created by hemants on 03/05/19.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TenantCreatedEventData {
  @NotNull
  private Long userId;
  @NotNull
  private Long tenantId;

  private String firstName;
  private String lastName;
  private Set<PermissionDTO> permissions;
  private String jwtToken;


  public com.sell.sales.core.domain.TenantCreatedEventData userId(Long userId) {
    this.userId = userId;
    return this;
  }

  public com.sell.sales.core.domain.TenantCreatedEventData tenantId(Long tenantId) {
    this.tenantId = tenantId;
    return this;
  }

  public com.sell.sales.core.domain.TenantCreatedEventData permissions(Set<PermissionDTO> permissions) {
    this.permissions = permissions;
    return this;
  }


  public com.sell.sales.core.domain.TenantCreatedEventData jwtToken(String jwtToken) {
    this.jwtToken = jwtToken;
    return this;
  }


  public com.sell.sales.core.domain.TenantCreatedEventData firstName(String firstName) {
    this.firstName = firstName;
    return this;
  }

  public com.sell.sales.core.domain.TenantCreatedEventData lastName(String lastName) {
    this.lastName = lastName;
    return this;
  }
}
