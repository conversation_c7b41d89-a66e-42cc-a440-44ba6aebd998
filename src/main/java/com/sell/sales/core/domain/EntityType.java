package com.sell.sales.core.domain;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;

@Getter
public enum EntityType {//@formatter:off
  // TODO: Move this entity configurations to DB with a strategy to make some things tenant configurable as well.

  // Productivity
  NOTE("Note", "note", "note", true, null, null, "Notes"),
  TASK("Task", "task", "task", true, null, getCollection(EntityType.NOTE), "Tasks"),
  MEETING("Meeting", "meeting", "meeting", true, null, getCollection(EntityType.NOTE), "Meetings"),

  // IAM
  USER("User", "users", "user", true, getCollection(PermissionAction.READ), null, "Users"),
  TEAM("Team", "team", "team", true, getCollection(PermissionAction.READ), null, "Teams"),
  AVAILABILITY("Availability", "availability", "availability", false, null, null, "Availabilities"),

  PROFILE("Profile", "profile", "profile", false, null, null, "Profiles"),
  PROFILE_PERMISSION("Profile Permission", "profile_permission", EntityType.PROFILE.getPermissionName(), false, null, null, ""),
  PROFILE_PRODUCT("Profile Product", "profile_product", EntityType.PROFILE.getPermissionName(), false, null, null, ""),
  PROFILE_MODULE("Profile Module", "profile_module", EntityType.PROFILE.getPermissionName(), false, null, null, ""),

  // Sales
  LEAD("Lead", "lead", "lead", true, null, getCollection(
      EntityType.NOTE, EntityType.TASK, EntityType.MEETING), "Leads"),
  DEAL("Deal", "deal", "deal", true, null, getCollection(
      EntityType.NOTE, EntityType.TASK, EntityType.MEETING), "Deals"),
  CONTACT("Contact", "contact", "contact", true, null, null, "Contacts"),
  COMPANY("Company", "company", "company", true, null, null, "Companies"),

  PIPELINE("Pipeline", "pipeline", "pipeline", false, getCollection(PermissionAction.READ), null, "Pipelines"),
  PIPELINE_STAGE("Pipeline Stage", EntityType.PIPELINE.getPermissionName(), "pipeline_stage", false, null, null, ""),

  // Tenant Configuration
  TENANT_CONFIGURATION("Tenant Configuration", "config", "tenant_configuration", false, null, null, ""),
  TENANT_PLAN("Tenant Plan", "config", "tenant_plan", false, null, null, ""),
  CONVERSION_MAPPING("Conversion Mapping", "conversionMapping", "conversion_mapping", false, null, null, ""),

  // Entity Customization
  ENTITY_DEF("Entity Definition", "entity_def", "entityCustomization", false, null, null, ""),
  FIELD("Field", "field", "entityCustomization", false,
      getCollection(PermissionAction.READ, PermissionAction.READ, PermissionAction.UPDATE, PermissionAction.DELETE), null, "Fields"),
  LAYOUT("Layout", "layout", "layout", false, null, null, ""),
  LAYOUT_FIELD("Layout Field", "layout_field", EntityType.LAYOUT.getPermissionName(), false, null, null, ""),
  LAYOUT_ITEM("Layout Item", "layout_item", EntityType.LAYOUT.getPermissionName(), false, null, null, ""),
  SECTION("Section", "section", EntityType.LAYOUT.getPermissionName(), false, null, null, ""),

  SHARE_RULE("ShareRule", "shareRule", "shareRule", false, null, null, "Share Rules"),

  // Product
  PRODUCT("Product", "", "products-services", false, null, null, "Products"),

  REPORT("Report", "", "report", false, null, null, "Reports"),
  MARKETING("Marketing", "", "marketing", false, null, null, "Marketing"),
  LEAD_CAPTURE_FORM("Lead Capture Form", "", "lead-capture-forms", false, null, null, "Lead Capture Forms"),
  WORKFLOW("Workflow", "", "workflow", false, null, null, "Workflows"),
  COMMUNICATION("Communication", "", "communication", false, null, null, "Communication"),
  EMAIL("Email", "email", "email", true, null, null, "Emails");

  //@formatter:on
  private String label;
  private String tableName;
  private String permissionName;
  private boolean sharable;
  private Set<PermissionAction> tenantAccess;
  private Set<EntityType> childEntities;
  private String pluralLabel;

  private EntityType(String label, String tableName, String permissionName, boolean sharable, Set<PermissionAction> tenantAccess,
      Set<EntityType> childEntities, String pluralLabel) {
    this.label = label;
    this.tableName = tableName;
    this.permissionName = permissionName;
    this.sharable = sharable;
    this.tenantAccess = tenantAccess;
    this.childEntities = childEntities;
    this.pluralLabel = pluralLabel;
  }

  @SafeVarargs
  private static <T> Set<T> getCollection(T... values) {
    return new HashSet<>(Arrays.asList(values));
  }
}
