package com.sell.sales.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by hemants on 11/01/19. Action class which is stored as json in database column. This defines an action available on a resource for the
 * permission.
 */
@Getter
@Setter
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class Action implements Serializable {

  private static final long serialVersionUID = 1L;
  // standard
  private boolean read;
  private boolean write;
  private boolean update;
  private boolean delete;

  // communication
  private boolean email;
  private boolean call;
  private boolean sms;

  // productivity
  private boolean task;
  private boolean note;
  private boolean meeting;
  private boolean document;

  // admin
  private boolean readAll;
  private boolean updateAll;
  private boolean deleteAll;

  //order management
  private boolean quotation;
  private boolean reassign;

  public com.sell.sales.core.domain.Action read(boolean read) {
    this.read = read;
    return this;
  }

  public com.sell.sales.core.domain.Action write(boolean write) {
    this.write = write;
    return this;
  }

  public com.sell.sales.core.domain.Action update(boolean update) {
    this.update = update;
    return this;
  }

  public com.sell.sales.core.domain.Action delete(boolean delete) {
    this.delete = delete;
    return this;
  }

  public com.sell.sales.core.domain.Action email(boolean email) {
    this.email = email;
    return this;
  }

  public com.sell.sales.core.domain.Action call(boolean call) {
    this.call = call;
    return this;
  }

  public com.sell.sales.core.domain.Action sms(boolean sms) {
    this.sms = sms;
    return this;
  }

  public com.sell.sales.core.domain.Action note(boolean note) {
    this.note = note;
    return this;
  }

  public com.sell.sales.core.domain.Action task(boolean task) {
    this.task = task;
    return this;
  }

  public com.sell.sales.core.domain.Action meeting(boolean meeting) {
    this.meeting = meeting;
    return this;
  }

  public com.sell.sales.core.domain.Action readAll(boolean readAll) {
    this.readAll = readAll;
    return this;
  }

  public com.sell.sales.core.domain.Action updateAll(boolean updateAll) {
    this.updateAll = updateAll;
    return this;
  }

  public com.sell.sales.core.domain.Action deleteAll(boolean deleteAll) {
    this.deleteAll = deleteAll;
    return this;
  }

  public com.sell.sales.core.domain.Action quotation(boolean quotation) {
    this.quotation = quotation;
    return this;
  }

  public com.sell.sales.core.domain.Action document(boolean document) {
    this.document = document;
    return this;
  }

  public com.sell.sales.core.domain.Action reassign(boolean reassign) {
    this.reassign = reassign;
    return this;
  }

}

