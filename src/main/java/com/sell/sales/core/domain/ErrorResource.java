package com.sell.sales.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.core.domain.FieldErrorResource;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by hemants on 11/01/19. Error Response class which will be sent to the user of REST api
 * call.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErrorResource {

  private String code;
  private String message;

  @JsonProperty("errorDetails")
  private List<FieldErrorResource> fieldErrors;

  public ErrorResource(String code, String message) {
    this.code = code;
    this.message = message;
    fieldErrors = new ArrayList<>();
  }

  public com.sell.sales.core.domain.ErrorResource message(String message) {
    this.message = message;
    return this;
  }

  public com.sell.sales.core.domain.ErrorResource fieldErrors(List<FieldErrorResource> fieldErrors) {
    this.fieldErrors = fieldErrors;
    return this;
  }

  public com.sell.sales.core.domain.ErrorResource code(String code) {
    this.code = code;
    return this;
  }
}
