package com.sell.sales.core.domain;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 02/01/19.
 * The permission DTO for permission model.
 */
@Getter
@Setter
public class PermissionDTO implements Serializable{
	private static final long serialVersionUID = 1L;

	private long id;
    private String name;
    private String description;
    private Integer limits;
    private String units;
    private Action action;

    public com.sell.sales.core.domain.PermissionDTO id(long id) {
        this.id = id;
        return this;
    }

    public com.sell.sales.core.domain.PermissionDTO name(String name) {
        this.name = name;
        return this;
    }

    public com.sell.sales.core.domain.PermissionDTO description(String description) {
        this.description = description;
        return this;
    }

    public com.sell.sales.core.domain.PermissionDTO limits(Integer limits) {
        this.limits = limits;
        return this;
    }

    public com.sell.sales.core.domain.PermissionDTO units(String units) {
        this.units = units;
        return this;
    }

    public com.sell.sales.core.domain.PermissionDTO action(Action action) {
        this.action = action;
        return this;
    }
}
