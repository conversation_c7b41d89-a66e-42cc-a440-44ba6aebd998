package com.sell.sales.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.core.domain.Source;
import java.util.ArrayList;
import java.util.Set;
import lombok.Getter;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreAuthentication extends UsernamePasswordAuthenticationToken {

  private static final long serialVersionUID = 1L;
  private final Set<PermissionDTO> permissions;
  private final String tenantId;
  private final String jwtToken;
  private String userId;
  private Source source;

  public CoreAuthentication(String userId, String tenantId, Set<PermissionDTO> permissions, String jwtToken) {
    super(userId, "[Protected]", new ArrayList<GrantedAuthority>());
    this.tenantId = tenantId;
    this.permissions = permissions;
    this.userId = userId;
    this.jwtToken = jwtToken;
  }

  public com.sell.sales.core.domain.CoreAuthentication setSource(Source source){
    this.source=source;
    return this;
  }

  public String getUserId() {
    return userId;
  }
}
