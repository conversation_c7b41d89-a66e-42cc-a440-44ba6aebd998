package com.sell.sales.core.config;


/**
 * Created by hemants on 11/01/19.
 * The configuration file for swagger documentation.
 * Can get more information from here: https://dzone.com/articles/swagger-generation-with-spring-boot
 * Reference documentation for spring fox: https://springfox.github.io/springfox/docs/current/
 */
public class CoreSwaggerConfig {
  /*  *//**
     * This needs to be overwrite by every service and supply its name.
     *
     * @return
     *//*
    protected String getServiceName() {
        return "Core";
    }

    *//**
     * This needs to be overwrite by every service and supply its current version.
     *
     * @return
     *//*
    protected String getVersion() {
        return "0.0.1-SNAPSHOT";
    }

    *//**
     * Set Contact info
     *
     * @return
     *//*
    protected Contact getContact() {
        return new Contact(
                "Amura",
                "https://amura.com",
                "<EMAIL>");
    }

    *//**
     * Return api inforamtion
     * @return
     *//*
    protected ApiInfo getApiInfo() {
        return new ApiInfo(
                getServiceName(),
                String.format("This is the %s API documentation", getServiceName()),
                getVersion(),
                "https://amura.com/termsofservice",
                getContact(),
                "License Info",
                "https://amura.com/license",
                Collections.emptyList()
        );
    }

    *//**
     * This actually build docket for generating swagger documenation.
     * @return
     *//*
    @Bean
    public Docket api() {
        TypeResolver typeResolver = new TypeResolver();
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(getApiInfo())

                .forCodeGeneration(true)
                //.pathMapping("/")
                .genericModelSubstitutes(ResponseEntity.class)
                .select()
                .apis(RequestHandlerSelectors.any())
                .paths(PathSelectors.regex("/error").negate())
                .build()
                .additionalModels(typeResolver.resolve(ErrorResource.class))
                .securityContexts(Lists.newArrayList(securityContext()))
                .securitySchemes(Lists.newArrayList(apiKey()))
                .globalOperationParameters(Arrays.asList(
                        new ParameterBuilder()
                                .name("Authorization")
                                .description("Description of header")
                                .modelRef(new ModelRef("string"))
                                .parameterType("header")
                                .required(false)
                                .build()
                ))
                .globalResponseMessage(RequestMethod.GET, getErrorResponseMapping())
                .globalResponseMessage(RequestMethod.POST, getErrorResponseMapping())
                .globalResponseMessage(RequestMethod.DELETE, getErrorResponseMapping())
                .globalResponseMessage(RequestMethod.PUT, getErrorResponseMapping())
                .globalResponseMessage(RequestMethod.PATCH, getErrorResponseMapping())
                .useDefaultResponseMessages(false)
                .ignoredParameterTypes(Pageable.class);
    }

    *//**
     * As most of our APIs are protected under JWT token using @Secure
     * @return
     *//*
    private ApiKey apiKey() {
        return new ApiKey("JWT", "Authorization", "header");
    }

    *//**
     * Return security context which is required for swagger for including security context like jwt
     * @return
     *//*
    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(defaultAuth())
                //.forPaths(PathSelectors.regex(DEFAULT_INCLUDE_PATTERN))
                .build();
    }

    *//**
     * Configure JWT token capability
     * @return
     *//*
    List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope
                = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Lists.newArrayList(
                new SecurityReference("JWT", authorizationScopes));
    }
    //Need to investigate further below code.
    private List<ResponseMessage> getErrorResponseMapping(){
        List<ResponseMessage> errorResponseMapping = new ArrayList<>();

        errorResponseMapping.add(new ResponseMessageBuilder()
                .code(500)
                .message("500 message")
                .responseModel(new ModelRef("ErrorResource"))
                .build());
        errorResponseMapping.add(new ResponseMessageBuilder()
                .code(406)
                .message("406 message")
                .responseModel(new ModelRef("ErrorResource"))
                .build());
        return errorResponseMapping;
    }*/
}
