package com.sell.sales.core.config;

import org.springframework.beans.factory.config.MethodInvokingFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.context.SecurityContextHolder;

public abstract class CoreSecurityContextPropagationConfig {

  /**
   * Presence of this in application context facilitates {@link org.springframework.security.core.context.SecurityContext SecurityContext} propagation
   * across async threads/tasks
   *
   * @return Updated {@link MethodInvokingFactoryBean}
   */
  @Bean
  public MethodInvokingFactoryBean methodInvokingFactoryBean() {
    MethodInvokingFactoryBean methodInvokingFactoryBean = new MethodInvokingFactoryBean();
    methodInvokingFactoryBean.setTargetClass(SecurityContextHolder.class);
    methodInvokingFactoryBean.setTargetMethod("setStrategyName");
    methodInvokingFactoryBean.setArguments(new String[]{SecurityContextHolder.MODE_INHERITABLETHREADLOCAL});
    return methodInvokingFactoryBean;
  }

}
