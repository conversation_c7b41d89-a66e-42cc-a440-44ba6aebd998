package com.sell.sales.core.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * Created by hemants on 14/01/19.
 * The configuration file for managing i18n message property file.
 * The message key is first looked into common.messages.properties file which is in core module,
 * then it is looked into service specific messages.properties file.
 */
public class CoreMessageSourceConfig {
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource =
                new ReloadableResourceBundleMessageSource();
        messageSource.setBasenames("classpath:messages", "classpath:common.messages", "classpath:ValidationMessages");
        return messageSource;
    }
}
