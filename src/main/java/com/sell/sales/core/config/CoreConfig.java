package com.sell.sales.core.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.sell.sales.core.service.AuditorAwareImpl;
import com.sell.sales.core.utils.EnvProfileUtil;
import java.time.ZoneId;
import java.util.TimeZone;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.data.web.config.SpringDataWebConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.stereotype.Component;


/**
 * Created by hemants on 27/03/19.
 */
public class CoreConfig {

    @Configuration
    public class MessageSourceConfig extends CoreMessageSourceConfig {
    }

    /**
     * The configuration file responsible for checking if logged in user has valid permission or not.
     * This file is being called whenever there is @secure annotation to the method.
     */
    @Configuration
    @EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
    public class MethodSecurityConfig extends CoreMethodSecurityConfig {
    }

    /**
     *   This enables swagger configuration
     */
//    @Configuration
    public class SwaggerConfig extends CoreSwaggerConfig {
    }


    /**
     * This will enable setUseTrailingSlashMatch
     */
    @Configuration
    public class WebMvcConfigurerAdapter extends CoreWebMvcConfigurerAdapter {
    }


    /**
     * This creates AuditorAware implementation which is used to add createdBy and modifiedBy field for those entities
     * who extends {@link com.sell.sales.core.domain.BaseEntity}
     */
    @Bean
    public AuditorAware<Long> auditorAware() {
        return new AuditorAwareImpl();
    }


    /**
     * This allows to store all dates in UTC in database
     */
    @Component
    public class DBInitializer {
        @PostConstruct
        public void initHibernateTZ() {
            TimeZone.setDefault(TimeZone.getTimeZone(ZoneId.of("UTC")));
        }
    }

    /**
     * This will allows to write date as standard ISO 8601 format
     * @param objectMapper
     */
    @Autowired
    public void configureObjectMapper(ObjectMapper objectMapper){
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    @Bean
    @Autowired
    public EnvProfileUtil getEnvProfileUtil(Environment environment){
        return new EnvProfileUtil(environment);
    }

  @Value("${max.page.size:1000}")
  private int MAX_PAGE_SIZE;

    @Configuration
    @EnableConfigurationProperties
    public class PaginationConfiguration extends SpringDataWebConfiguration {

        @Bean
        public PageableHandlerMethodArgumentResolver pageableResolver() {
            PageableHandlerMethodArgumentResolver pageableHandlerMethodArgumentResolver =
                new PageableHandlerMethodArgumentResolver(sortResolver());

            pageableHandlerMethodArgumentResolver.setMaxPageSize(MAX_PAGE_SIZE);

            return pageableHandlerMethodArgumentResolver;
        }

    }

    /**
     * This configuration facilitates the security context propagation accross async threads
     */
    @Configuration
    public class ContextPropagationConfig extends CoreSecurityContextPropagationConfig {

    }
}
