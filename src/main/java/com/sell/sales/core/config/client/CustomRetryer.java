package com.sell.sales.core.config.client;

import com.sell.sales.core.exception.APIException;
import feign.RetryableException;
import feign.Retryer;

public class CustomRetryer extends Retryer.Default {
  private final int maxAttempts;
  private final long period;
  private final long maxPeriod;

  public CustomRetryer(long period, long maxPeriod, int maxAttempts) {
    super(period, maxPeriod, maxAttempts);
    this.period = period;
    this.maxPeriod = maxPeriod;
    this.maxAttempts = maxAttempts;
  }

  @Override
  public void continueOrPropagate(RetryableException e) {
    try {
      super.continueOrPropagate(e);
    } catch (RetryableException re) {
      throw new APIException(re.getMessage(), re);
    }
  }

  @Override
  public Retryer clone() {
    return new CustomRetryer(period, maxPeriod, maxAttempts);
  }
}
