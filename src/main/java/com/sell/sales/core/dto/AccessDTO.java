package com.sell.sales.core.dto;

import com.sell.sales.core.domain.Action;
import com.sell.sales.core.utils.PermissionUtil;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AccessDTO {
  private Map<Long, Action> accessByOwners;
  private Map<Long, Action> accessByRecords;

  public Set<Long> fetchOwnerIds() {
    return accessByOwners != null ? accessByOwners.keySet() : new HashSet<>();
  }

  public Set<Long> fetchRecordIds() {
    return accessByRecords != null ? accessByRecords.keySet() : new HashSet<>();
  }

  /**
   * returns access for the specific record based on its owner & recordId for current loggedIn user
   *
   * @param ownerId
   * @param recordId
   * @return
   */
  public Optional<Action> fetchPermission(Long ownerId, Long recordId) {
    Optional<Action> optionalOwnerPermissions = fetchPermission(accessByOwners, ownerId);
    Optional<Action> optionalRecordPermissions = fetchPermission(accessByRecords, recordId);

    if(optionalOwnerPermissions.isPresent() && optionalRecordPermissions.isPresent()) {
      return Optional.of(PermissionUtil.commulativeMergePermissionActions(optionalOwnerPermissions.get(), optionalRecordPermissions.get()));
    }
    if(optionalOwnerPermissions.isPresent()) {
      return optionalOwnerPermissions;
    }
    if(optionalRecordPermissions.isPresent()) {
      return optionalRecordPermissions;
    }

    return Optional.empty();
  }

  private Optional<Action> fetchPermission(Map<Long, Action> accessById, Long id) {
    return accessById != null && accessById.get(id) != null ? Optional.of(accessById.get(id)) : Optional.empty();
  }
}
