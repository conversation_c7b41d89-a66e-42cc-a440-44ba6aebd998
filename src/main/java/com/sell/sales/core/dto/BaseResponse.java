package com.sell.sales.core.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.core.domain.Action;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class BaseResponse implements Serializable {
  private static final long serialVersionUID = 1L;

  private Date createdAt;
  private Date updatedAt;
  private Long createdBy;
  private Long updatedBy;

  @JsonIgnoreProperties(value = {"write", "readAll", "updateAll"})
  private Action recordActions;

  private Map<String, Object> metaData;
}
