package com.sell.sales.core.dto;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CacheDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private Map<Long, Set<Long>> usersByTeam;
  private Map<Long, Set<Long>> teamsByUser;
}
