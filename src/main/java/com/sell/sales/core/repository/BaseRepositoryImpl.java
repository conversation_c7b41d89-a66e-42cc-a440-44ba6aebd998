package com.sell.sales.core.repository;

import com.sell.sales.core.annotation.IgnoreTenantFilter;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.BaseEntity;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.core.domain.ICustomFindSpec;
import com.sell.sales.core.domain.PermissionAction;
import com.sell.sales.core.dto.AccessDTO;
import com.sell.sales.core.exception.BaseException;
import com.sell.sales.core.utils.ClassUtil;
import com.sell.sales.core.utils.PermissionUtil;
import com.sell.sales.core.utils.SecurityUtil;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.util.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * Created by hemants on 26/03/19.
 * <p>
 * This class overrides some of methods which is given by {@link SimpleJpaRepository}. The purpose is to append tenant id to every fetch request. The
 * appending of tenant id is handled centrally by this class. To make this class available as default implementation for
 * {@link org.springframework.data.jpa.repository.JpaRepository} have look at main application class.
 */
@Slf4j
public class BaseRepositoryImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements BaseJpaRepository<T, ID> {

  private static final String ID_MUST_NOT_BE_NULL = "The given id must not be null!";
  private static final String FIELD_PARAM_RECORD_ID = "id";
  private static final String FIELD_PARAM_TENANT_ID = "tenantId";
  private static final String FIELD_PARAM_IS_DELETED = "deleted";

  private static final String FIELD_GETTER_TENANT_ID = "getTenantId";
  private static final String FIELD_GETTER_IS_DELETED = "isDeleted";

  protected final EntityManager em;
  protected final JpaEntityInformation<T, ?> entityInformation;

  protected final String applicableAccessPermission;
  private final boolean isTenantIdApplicable;
  private final boolean isDeletedApplicable;
  private final boolean validateTenantUser;

  /**
   * Constructor used by repository factory to create implementation instance for all jpa repositories
   */
  public BaseRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
    super(entityInformation, entityManager);
    this.entityInformation = entityInformation;
    this.em = entityManager;
    validateTenantUser = !ClassUtil.hasAnnotation(getDomainClass(), IgnoreTenantFilter.class);
    isTenantIdApplicable = ClassUtil.hasField(getDomainClass(), FIELD_GETTER_TENANT_ID) && validateTenantUser;
    isDeletedApplicable = ClassUtil.hasField(getDomainClass(), FIELD_GETTER_IS_DELETED);

    applicableAccessPermission = PermissionUtil.getEntityAccessPermission(getDomainClass());
    if (applicableAccessPermission == null)
      log.warn(
          "Access Permission not found for the Entity: {}, please mark the class with @AccessPermission for repository to return basic permissions with records!",
          getDomainClass().getSimpleName());
  }

  /**
   * Validate if user has required permissions before updating a record <br>
   * permission for create should be handled at service method layer with {@link @Secure} annotation
   */
  @Override
  @Transactional
  public <S extends T> S save(S entity) {
    if (entityInformation.isNew(entity)) {
      em.persist(entity);
      return (S) appendUserPermissionAction(entity, null);
    } else {
      // user should have update permission for the entity record
      validatePermissionAction(entity, PermissionAction.UPDATE);

      return em.merge(entity);
    }
  }

  /**
   * Method which skips record level permission checks and update the entity. <br>
   * Record tenant is validated.
   */
  @Override
  @Transactional
  public <S extends T> S forceUpdate(S entity) {
    Assert.notNull(entityInformation.getId(entity), ID_MUST_NOT_BE_NULL);

    // user should have update permission for the entity record
    validateTenantAccess(entity);

    return em.merge(entity);
  }

  /**
   * Special method for updating owner<br>
   * ** this is required since simple permission authorization is done against current ownerId
   *
   * @param entity
   * @param ownerIdToUpdate
   * @return
   */
  @Override
  @Transactional
  public <S extends T> S updateEntityOwner(S entity, Long ownerIdToUpdate) {
    throw new BaseException(ErrorCodes.COMMON_OWNER_NOT_APPLICABLE);
  }

  @Override
  public T findOneWithoutTenantId(ID id) {
    return super.findOne(id);
  }

  /**
   * If first checks if the entity is extends by {@link BaseEntity} or not. If yes, use tenant specific where condition else find the entity by id.
   */
  @Override
  public T findOne(ID id) {
    return customFindOne(null, id);
  }

  /**
   * Return list of entities for the tenant (fetched from spring security context)
   */
  @Override
  public List<T> findAll() {
    return customFindAll(null);
  }

  /**
   * Return list of entities for the tenant (fetched from spring security context)
   */
  @Override
  public Page<T> findAll(Pageable pageable) {
    return customFindAll(null, pageable);
  }

  @Override
  public List<T> customFindAll(ICustomFindSpec<T> customFindSpec) {
    log.debug("inside custom base repository for findAll");
    Pair<Specification<T>, AccessDTO> queryResponsePair = resolveSelectSpecification(customFindSpec);
    return super.getQuery(queryResponsePair.getFirst(), new Sort("id")).getResultList().stream()
        .map(entity -> appendUserPermissionAction(entity, queryResponsePair.getSecond())).collect(Collectors.toList());
  }

  @Override
  public Page<T> customFindAll(ICustomFindSpec<T> customFindSpec, Pageable pageable) {
    log.debug("inside custom base repository for findAll pageable");

    Pair<Specification<T>, AccessDTO> queryResponsePair = resolveSelectSpecification(customFindSpec);

    return readPage(super.getQuery(queryResponsePair.getFirst(), pageable), getDomainClass(), pageable, queryResponsePair.getFirst())
        .map(entity -> appendUserPermissionAction(entity, queryResponsePair.getSecond()));
  }

  @Override
  public T customFindOne(ICustomFindSpec<T> customFindSpec, ID id) {
    log.debug("inside custom base repository for findOne");
    Assert.notNull(id, ID_MUST_NOT_BE_NULL);

    try {
      Pair<Specification<T>, AccessDTO> queryResponsePair = resolveSelectSpecification(id, customFindSpec);
      return appendUserPermissionAction(super.getQuery(queryResponsePair.getFirst(), new Sort("id")).getSingleResult(),
          queryResponsePair.getSecond());
    } catch (NoResultException e) {
      // consuming the exception as we would like to return null in case there is no entry found for that id in the tenant.
      log.warn("No record found for the id");
      return null;
    } catch (IllegalArgumentException e) {
      log.error("Found IllegalArgumentException while appending tenantId");
      throw new BaseException(ErrorCodes.COMMON_INTERNAL_ERROR);
    }
  }

  /*
   * Override Delete functionality to provide soft-delete using deleted flag for entities
   */
  @Override
  @Transactional
  public void delete(T entity) {
    Assert.notNull(entity, "The entity must not be null!");

    // user should have delete permission for the entity record
    validatePermissionAction(entity, PermissionAction.DELETE);

    try {
      entity.getClass().getMethod("setDeleted", boolean.class).invoke(entity, true);
      em.persist(entity);
    } catch (SecurityException | IllegalArgumentException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      log.error("Soft deleted field not applicable for the entity: {}", getDomainClass().getSimpleName());
      throw new BaseException(ErrorCodes.COMMON_INTERNAL_ERROR);
    }
  }

  /*
   * Original delete functionality to remove something from the system.
   */
  @Override
  @Transactional
  public void hardDelete(T entity) {
    Assert.notNull(entity, "The entity must not be null!");

    // user should have delete permission for the entity record
    validatePermissionAction(entity, PermissionAction.DELETE);

    em.remove(em.contains(entity) ? entity : em.merge(entity));
  }

  /**
   * The method build typedQuery in which tenant_id is appended in where condition.
   */
  private Pair<Specification<T>, AccessDTO> resolveSelectSpecification(ICustomFindSpec<T> customFindSpec) {
    return resolveSelectSpecification(null, customFindSpec);
  }

  /**
   * The method builds select typedQuery for entities which extend BaseEntity.<br>
   * - adds tenantId filter<br>
   * - resolves shared rules for READ permissions<br>
   * - returns Query for all if id is null<br>
   */
  protected Pair<Specification<T>, AccessDTO> resolveSelectSpecification(ID id, ICustomFindSpec<T> customFindSpec) {
    Specification<T> querySpec = (Root<T> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder builder) -> {
      List<Predicate> filterPredicates = new ArrayList<>();

      // filter soft deleted records
      if (isDeletedApplicable)
        filterPredicates.add(builder.isFalse(root.get(FIELD_PARAM_IS_DELETED)));

      // tenantId check
      if (isTenantIdApplicable)
        filterPredicates.add(builder.equal(root.get(FIELD_PARAM_TENANT_ID), SecurityUtil.getTenantId()));

      // if the lookup is for only one record add id where clause at top level.
      if (id != null)
        filterPredicates.add(builder.equal(root.get(FIELD_PARAM_RECORD_ID), id));

      if (customFindSpec != null) {
        Predicate mergedSharedCustomPredicate = customFindSpec.specification((rooti, query, cbi) -> {
          return builder.conjunction();
        }).toPredicate(root, criteriaQuery, builder);

        filterPredicates.add(mergedSharedCustomPredicate);
      }

      return builder.and(filterPredicates.toArray(new Predicate[filterPredicates.size()]));
    };

    return Pair.of(querySpec, new AccessDTO());
  }

  private void validatePermissionAction(T entity, PermissionAction permissionAction) {
    validateTenantAccess(entity);
    validateSharedPermissionAction(entity, permissionAction);
  }

  private void validateTenantAccess(T entity) {
    // No one should be allowed to access other tenant's data
    Object tenantId = ClassUtil.getObjectField(entity, FIELD_GETTER_TENANT_ID);
    if (validateTenantUser && SecurityUtil.isLoggedInUser() && !ObjectUtils.nullSafeEquals(SecurityUtil.getTenantId(), tenantId))
      throw new BaseException(ErrorCodes.COMMON_UNAUTH_ACTION);
  }

  private T appendUserPermissionAction(T entity, AccessDTO accessDTO) {
    try {
      // Set the current logged-in users access
      entity.getClass().getMethod("setRecordActions", Action.class).invoke(entity,
          PermissionUtil.resolveRecordUserAction((Long) entityInformation.getId(entity), (Long) ClassUtil.getObjectField(entity, "getOwnerId"),
              applicableAccessPermission, accessDTO, getTenantAccess()));
    } catch (SecurityException | IllegalArgumentException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      log.debug("cannot append recordActions in entity {}", getDomainClass().getSimpleName());
    }
    return entity;
  }

  protected Set<PermissionAction> getTenantAccess() {
    return null;
  }

  protected void validateSharedPermissionAction(T entity, PermissionAction permissionAction) {
    //
  }
}
