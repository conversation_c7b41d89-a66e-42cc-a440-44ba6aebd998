package com.sell.sales.core.repository;

import com.sell.sales.core.domain.ICustomFindSpec;
import java.io.Serializable;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * Created by hemants on 05/04/19.
 * TODO: Should be part of core module
 */
@NoRepositoryBean
public interface BaseJpaRepository<T, ID extends Serializable> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {
  T findOneWithoutTenantId(ID id);

  <S extends T> S forceUpdate(S entity);
  <S extends T> S updateEntityOwner(S entity, Long ownerIdToUpdate);

  void hardDelete(T entity);

  List<T> customFindAll(ICustomFindSpec<T> customFind);

  Page<T> customFindAll(ICustomFindSpec<T> customFind, Pageable pageable);

  T customFindOne(ICustomFindSpec<T> customFind, ID id);
}
