package com.sell.sales.core.event;

import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.retry.RepublishMessageRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.interceptor.RetryOperationsInterceptor;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.annotation.Async;


@Slf4j
public abstract class CoreEventEmitter implements EventEmitter {

    private static final String SERVICE_PREFIX = "ex.";
    private static final String QUEUE_PREFIX = "q.";
    @Value("${core.rabbitmq.initial.backoff.internal:2000}")
    private int initialBackoffIntervalInMilliseconds = 2000;
    @Value("${core.rabbitmq.max.backoff.internal:30000}")
    private int maxBackoffIntervalInMilliseconds = 30000;
    @Value("${core.rabbitmq.backoff.multiplier:2}")
    private long backoffMultiplier = 2;
    private static final int MAX_ATTEMPT = 8;

    @Value("${core.rabbitmq.host:127.0.0.1}")
    private String rabbitmqHost;

    @Value("${core.rabbitmq.virtualHost:/}")
    private String rabbitmqVirtualHost;

    @Value("${core.rabbitmq.port:5672}")
    private String rabbitmqPort;

    @Value("${core.rabbitmq.username:guest}")
    private String username;

    @Value("${core.rabbitmq.password:guest}")
    private String password;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private AmqpAdmin rabbitAdmin;

    @Autowired
    private ErrorMessageRecoveryFacade errorMessageRecoveryFacade;

    /**
     * This creates a connect to rabbitmq server in async fashion
     *
     * @return
     */
    @Bean
    @Async
    protected ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(rabbitmqHost, Integer.valueOf(rabbitmqPort));
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(rabbitmqVirtualHost);
        return connectionFactory;
    }

    /**
     * The rabbitmqtemplate is configured with {@link ExponentialBackOffPolicy} so that when client try to send an event and
     * server is unavailable, due to any reason, The {@link ExponentialBackOffPolicy} will pitch in and retry the connection
     * to the server with configured number of time.
     *
     * @return
     */
    @Bean
    public RabbitTemplate coreExchangeTemplate() {
        rabbitTemplate = new RabbitTemplate(connectionFactory());

        RetryTemplate retry = new RetryTemplate();
        ExponentialBackOffPolicy policy = new ExponentialBackOffPolicy();
        policy.setInitialInterval(initialBackoffIntervalInMilliseconds);
        policy.setMultiplier(backoffMultiplier);
        policy.setMaxInterval(maxBackoffIntervalInMilliseconds);
        rabbitTemplate.setRetryTemplate(retry);

        Jackson2JsonMessageConverter converter = new Jackson2JsonMessageConverter();
        converter.setCreateMessageIds(true);
        rabbitTemplate.setMessageConverter(converter);
        return rabbitTemplate;
    }

    /**
     * This will configure the statelessRetry interceptor which will be used when message receiver is raising exception or
     * not able to process the message due to some reason. Rabbitmq will try to resend the message according to this advice.
     * Dead letter is configured: 'ex.error' as exchange and 'errors' as routing key
     *
     * @return
     */
    @Bean
    public RetryOperationsInterceptor statelessRetryOperationsInterceptor() {
        return RetryInterceptorBuilder.stateless()
                .backOffOptions(initialBackoffIntervalInMilliseconds, backoffMultiplier, maxBackoffIntervalInMilliseconds)
                .maxAttempts(MAX_ATTEMPT)
                .recoverer(errorMessageRecoveryFacade)
                .build();
    }

    /**
     * Main method to send an event.
     *
     * @param event
     * @param context
     */
    @Override
    public void emit(CoreEvent event, Object context) {
        new Thread(() -> {
            try {
        log.info("emitting event: {}", event.getEventName());
                rabbitTemplate.convertAndSend(
                    getExchangeName(),
                    event.getEventName(),
                    context,
                    message -> {
                        message.getMessageProperties().getHeaders().remove("__TypeId__");
                        return message;
                    });
                log.info("event emitted successfully");
            } catch (Exception e) {
        log.error("error while emitting event", e);
            }
        }
        ).start();
    }

    /**
     * This method is used to send direct message to the service.
     *
     * @param serviceName
     * @param eventName
     * @param context
     */
    @Override
    public void sendDirect(String serviceName, String eventName, Object context) {
        new Thread(() -> {
            try {
                rabbitTemplate.convertAndSend(SERVICE_PREFIX + serviceName, eventName, context);
                log.info("event emitted successfully");
            } catch (Exception e) {
                log.error("error while emitting event", e);
            }
        }
        ).start();
    }

    /**
     * Constructor method, which will declare exchange and queue for the service with following name convention:
     * exchange : {servicePrepend}.{serviceName}, here serviceName is return by protected method getServiceName()
     * queue : {queuePrepend}.{serviceName}
     */
    @PostConstruct
    public void customInit() {
        try {
            rabbitAdmin.declareQueue(new Queue(getQueueName(), true));
            rabbitAdmin.declareExchange(new TopicExchange(getExchangeName(), true, false));
            rabbitAdmin.declareExchange(new TopicExchange("ex.error", true, false));
        } catch (Exception e) {
      log.error("Error while declaring the queue and exchange for the service emitted successfully", e);
        }
    }

    /**
     * Main method to handle the event receiver.
     *
     * @param serviceName name of the service which listener is interested in to consume the events.
     * @param eventName   evenName in form {service-name}.{resource-name}.{action}
     * @param listener    the actually listener code which will take action against the event
     */
    @Override
    public void on(String serviceName, String eventName, MessageListener listener) {
        try {
            rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(getQueueName())).to(new TopicExchange(SERVICE_PREFIX + serviceName)).with(eventName));
            SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
            container.setConnectionFactory(rabbitTemplate.getConnectionFactory());
            container.setQueueNames(getQueueName());
            container.setMessageListener(listener);
            container.setMessageConverter(new Jackson2JsonMessageConverter());
            container.setAdviceChain(statelessRetryOperationsInterceptor());
            container.start();
        } catch (Exception e) {
      log.error("Error while listening to the event", e);
        }
    }

    @Override
    public void on(String queueName, String serviceName, String eventName, MessageListener listener) {
        try {
            Queue queue = new Queue(queueName, true);
            TopicExchange exchange = new TopicExchange(SERVICE_PREFIX + serviceName);
            rabbitAdmin.declareQueue(queue);
            rabbitAdmin.declareExchange(exchange);
            rabbitAdmin.declareBinding(BindingBuilder.bind(queue).to(exchange).with(eventName));
            SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
            container.setConnectionFactory(rabbitTemplate.getConnectionFactory());
            container.setQueueNames(queueName);
            container.setMessageListener(listener);
            container.setMessageConverter(new Jackson2JsonMessageConverter());
            container.setAdviceChain(statelessRetryOperationsInterceptor());
            container.start();
        } catch (Exception e) {
            log.error("Error while listening to the event", e);
        }
    }

    private String getExchangeName() {
        return SERVICE_PREFIX + getServiceName();
    }

    private String getQueueName() {
        return QUEUE_PREFIX + getServiceName();
    }

    //public abstract String getServiceName();
}
