package com.sell.sales.core.event;

import java.util.Date;
import lombok.Getter;

@Getter
public class ExecutionLogStatusEvent {

  private final Long eventId;
  private final String status;
  private final int statusCode;
  private final Date statusUpdatedAt;
  private final String errorCode;
  private final String errorMessage;

  public ExecutionLogStatusEvent(Long eventId, String status, int statusCode, Date statusUpdatedAt, String errorCode, String errorMessage) {
    this.eventId = eventId;
    this.status = status;
    this.statusCode = statusCode;
    this.statusUpdatedAt = statusUpdatedAt;
    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
  }
}