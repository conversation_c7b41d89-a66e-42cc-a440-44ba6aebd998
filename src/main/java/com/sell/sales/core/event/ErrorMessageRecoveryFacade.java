package com.sell.sales.core.event;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.infra.mq.WorkflowExecutionLogStatusEventPublisher;
import java.util.Date;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.retry.MessageRecoverer;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ErrorMessageRecoveryFacade implements MessageRecoverer {

  private final ObjectMapper objectMapper;
  private final WorkflowExecutionLogStatusEventPublisher workflowExecutionLogStatusEventPublisher;

  public ErrorMessageRecoveryFacade(ObjectMapper objectMapper,
      WorkflowExecutionLogStatusEventPublisher workflowExecutionLogStatusEventPublisher) {
    this.objectMapper = objectMapper;
    this.workflowExecutionLogStatusEventPublisher = workflowExecutionLogStatusEventPublisher;
  }

  @Override
  public void recover(Message message, Throwable throwable) {
    String messageBody = new String(message.getBody());
    Long eventId = 0L;
    String errorCode = "000000";

    try {
      Object customMessage = message.getMessageProperties().getHeaders().get("customMessage");
      Object replyToExchange = message.getMessageProperties().getHeaders().get("replyToExchange");
      Object replyToEvent = message.getMessageProperties().getHeaders().get("replyToEvent");
      HashMap<String, Object> payload = objectMapper.readValue(messageBody, HashMap.class);
      if(replyToExchange==null || replyToEvent==null){
        return;
      }
      String errorMessage = resolveErrorMessage(throwable);
      eventId = extractEventId(payload);

      if (customMessage instanceof ErrorResource) {
        errorCode = ((ErrorResource) customMessage).getCode();
        errorMessage = ((ErrorResource) customMessage).getMessage();
      }
      publishExecutionLog(eventId, errorCode, errorMessage, (String) replyToExchange, (String) replyToEvent);
    } catch (Exception e) {
      log.error("Error while publishing workflow execution log for eventId {} with message payload {}, error: {}",
          eventId, messageBody, e.getMessage(), e);
    }
  }

  private String resolveErrorMessage(Throwable throwable) {
    if (throwable.getCause() != null) {
      return throwable.getCause().getMessage();
    }
    return throwable.getMessage();
  }

  private Long extractEventId(HashMap<String, Object> payload) throws Exception {
    Object metadata = payload.get("metadata");
    if (metadata != null) {
      HashMap<String, Object> metadataMap = objectMapper.readValue(objectMapper.writeValueAsString(metadata), HashMap.class);
      return Long.parseLong(metadataMap.getOrDefault("eventId",0).toString());
    }
    return 0L;
  }

  private void publishExecutionLog(Long eventId, String errorCode, String errorMessage, String replyToExchange, String replyToEvent) {
    log.info("Publishing execution log status update event from error recovery facade with error code {} and error message {} and eventId {}",  errorCode, errorMessage, eventId);
    workflowExecutionLogStatusEventPublisher.publishExecutionLogStatusUpdateEvent(
        new ExecutionLogStatusEvent(eventId, "FAILED", 400, new Date(), errorCode, errorMessage), replyToExchange, replyToEvent
    );
  }

}
