package com.sell.sales.core.utils;


import com.sell.sales.core.domain.Env;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;

/**
 * Created by hemants on 06/04/19.
 */
@RequiredArgsConstructor
public class EnvProfileUtil {

  private final Environment environment;

  public boolean isDevProfile() {
    return checkProfile(Env.DEV);
  }
  public boolean isStagingProfile() {
    return checkProfile(Env.STAGING);
  }
  public boolean isProductionProfile() {
    return checkProfile(Env.PRODUCTION);
  }

  private boolean checkProfile(Env env) {
    if (environment.getActiveProfiles().length > 0) {
      return environment.getActiveProfiles()[0].equals(env.toString());
    }
    return false;
  }
}
