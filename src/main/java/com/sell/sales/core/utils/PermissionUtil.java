package com.sell.sales.core.utils;


import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.CoreAuthentication;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.core.domain.PermissionAction;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.core.dto.AccessDTO;
import com.sell.sales.core.exception.BaseException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Slf4j
public class PermissionUtil {

  private PermissionUtil() {
    //
  }

  /**
   * Validates for provided permission set should not create an error state in the system.
   *
   * ** an UPDATE/DELETE access without READ is an Error state.
   *
   * @param permissions
   */
  public static void validatePermissionErrorState(Action permissionActions) {
    List<String> validPermissionActions = getAllValidPermissionActions(permissionActions);
    if (!permissionActions.isRead() && !permissionActions.isReadAll() && !validPermissionActions.isEmpty()) {
      throw new BaseException(ErrorCodes.COMMON_PERMISSION_ERROR_STATE);
    }
  }

  /**
   * Return all valid scope of the user as list of {resource}_{action} example: lead_read. Wherever action is true it is append to {resource} with
   * '_'.
   *
   * @param auth
   * @return
   */
  public static List<String> getAllValidPermissionScopes(CoreAuthentication auth) {
    Set<PermissionDTO> permissions = auth.getPermissions();

    List<List<String>> scopes = permissions.stream().map(p -> {
      List<String> resultScope = new ArrayList<>();
      if (p.getAction() != null) {
        List<String> scope = getAllValidPermissionActions(p.getAction());
        if (!scope.isEmpty()) {
          for (String s : scope) {
            resultScope.add(p.getName() + "_" + s);
          }
        }
      } else {
        resultScope.add(p.getName());
      }
      return resultScope;
    }).collect(Collectors.toList());

    List<String> resultScopes = new ArrayList<>();

    for (List<String> s : scopes) {
      resultScopes.addAll(s);
    }
    return resultScopes;
  }

  /**
   * Using reflection, iterate all action declared on Action class.
   *
   * @param action
   * @return
   */
  // TODO: Store it in a cache.
  public static List<String> getAllValidPermissionActions(Action action) {
    Field[] fields = Action.class.getDeclaredFields();
    List<String> permission = new ArrayList<>();
    for(Field field : fields){
      if("serialVersionUID".equalsIgnoreCase(field.getName())){
        continue;
      }
      field.setAccessible(true);
      try {
        boolean b = (boolean) field.get(action);
        if (b) {
          permission.add(field.getName());
        }
      } catch (IllegalAccessException | ClassCastException e) {
        log.error("error while casting to action DTO for eval permissions", action, e);
        throw new BaseException(ErrorCodes.INVALID_PERMISSION_ACTION);
      }
    }
    return permission.stream().filter(s -> s.length() > 0).collect(Collectors.toList());
  }

  public static String getEntityAccessPermission(Class clazz) {
    AccessPermission[] annotation = (AccessPermission[]) clazz.getAnnotationsByType(AccessPermission.class);
    return !ObjectUtils.isEmpty(annotation) ? annotation[0].value() : null;
  }

  public static Action resolveRecordUserAction(Long recordId, Long recordOwnerId, String permission, AccessDTO sharedAccessDTO,
      Set<PermissionAction> tenantAccess) {
    if (!SecurityUtil.isLoggedInUser()) {
      return null;
    }

    Action profileAction = SecurityUtil.getUserPermissionAction(permission);
    // No permissions to user
    if (profileAction == null) {
      return profileAction;
    }

    // Skip shareRule resolution if ownerId is not applicable OR there shareRule accessDTO is not available.
    // Skip shareRule if the current user is owner of the record.
    if (sharedAccessDTO == null || recordOwnerId == null || SecurityUtil.getUserId().equals(recordOwnerId)) {
      return resolveAdminPermissionsForRecordAction(profileAction, profileAction);
    }

    // A user cannot have more permission actions than their profile even if shared through shareRule
    Optional<Action> optionalSharedAction = sharedAccessDTO.fetchPermission(recordOwnerId, recordId);
    if (tenantAccess != null) {
      Action tenantAction = new Action();
      tenantAccess.forEach(permissionAction -> {
        addTenantAccessPermissions(tenantAction, permissionAction);
      });


      return optionalSharedAction
              .map(sharedAction -> {
                  tenantAccess.forEach(permissionAction -> {
                    addTenantAccessPermissions(sharedAction, permissionAction);
                  });

                  Action sharedTenantAction = restrictiveMergePermissionActions(sharedAction, tenantAction);
                  return resolveAdminPermissionsForRecordAction(profileAction, restrictiveMergePermissionActions(profileAction, sharedTenantAction));
              })
              .orElse(resolveAdminPermissionsForRecordAction(profileAction, restrictiveMergePermissionActions(profileAction, tenantAction)));

    }

    return optionalSharedAction
            .map(sharedAction ->
                    resolveAdminPermissionsForRecordAction(profileAction, restrictiveMergePermissionActions(profileAction, sharedAction)))
            .orElse(resolvedAccessBasedOnPermission(profileAction, recordOwnerId));
  }

  private static Action resolvedAccessBasedOnPermission(Action profileAction, Long recordOwnerId){
    Action recordActions = mergePermissionActions(profileAction, new Action(), (b1, b2) -> b1 || b2);

    if (profileAction.isReadAll()) {
      recordActions.read(true).readAll(true);
    }else{
      recordActions.read(recordOwnerId.equals(SecurityUtil.getUserId()));
    }
    if(profileAction.isUpdateAll()){
      recordActions.update(true).updateAll(true);
    }else {
      recordActions.update(recordOwnerId.equals(SecurityUtil.getUserId()));
    }
    return recordActions;
  }

  // Admin read & update will apply irrespective of shareRule
  private static Action resolveAdminPermissionsForRecordAction(Action profileAction, Action recordAction) {
    if (profileAction.isReadAll()) {
      recordAction.read(true).readAll(true);
    }

    if (profileAction.isUpdateAll()) {
      recordAction.update(true).updateAll(true);
    }

    if (profileAction.isReassign()){
      recordAction.reassign(true);
    }

    return recordAction;
  }

  private static void addTenantAccessPermissions(Action mergedAction, PermissionAction action) {
    try {
      mergedAction.getClass().getMethod(action.getAction(), boolean.class).invoke(mergedAction, true);
    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      log.error("Error while adding tenant level access permissions");
    }
  }

  /**
   * Merge permissionActions and return a restrictive permissionActions
   *
   * @param a1
   * @param a2
   */
  public static Action restrictiveMergePermissionActions(Action a1, Action a2) {
    return mergePermissionActions(a1, a2, (b1, b2) -> b1 && b2);
  }

  /**
   * Merge permissionActions and return a commulative permissionActions
   *
   * @param a1
   * @param a2
   */
  public static Action commulativeMergePermissionActions(Action a1, Action a2) {
    return mergePermissionActions(a1, a2, (b1, b2) -> b1 || b2);
  }

  @FunctionalInterface
  private interface IMergePermission {

    public boolean merge(boolean b1, boolean b2);
  }

  private static Action mergePermissionActions(Action a1, Action a2, IMergePermission mergePermission) {
    if (a1 == null && a2 == null) {
      return new Action();
    } else if (a1 == null) {
      return a2;
    } else if (a2 == null) {
      return a1;
    }

    Field[] fields = Action.class.getDeclaredFields();
    Action mergedAction = new Action();
    for(Field f: fields){
      if("serialVersionUID".equalsIgnoreCase(f.getName())){
        continue;
      }
      f.setAccessible(true);
      try {
        f.set(mergedAction, mergePermission.merge((boolean) f.get(a1), (boolean) f.get(a2)));
      } catch (IllegalAccessException | ClassCastException e) {
        log.error("error while casting to action DTO for merging actions: {}, {}", a1, a2, e);
        throw new BaseException(ErrorCodes.INVALID_PERMISSION_ACTION);
      }
    }

    return mergedAction;
  }
}
