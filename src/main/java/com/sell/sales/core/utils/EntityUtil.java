package com.sell.sales.core.utils;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.core.exception.BaseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;

/**
 * Created by hemants on 08/04/19.
 */
public class EntityUtil {
  private static final Pattern TITLE_CASE_REGEX_PATTERN = Pattern.compile("([A-Z]+|[A-Z]?[a-z]+)(?=[A-Z]|\\b)");

  private EntityUtil() {
    //
  }

  public static EntityType getEntityType(@NotNull String entityType) {
    try {
      return EntityType.valueOf(entityType.toUpperCase());
    } catch (Exception e) {
      throw new BaseException(ErrorCodes.COMMON_INVALID_RESOURCE_TYPE, "entity");
    }
  }

  /**
   * Resolves EntityType from the entity class based on a common logic on className
   *
   * @return @{link EntityType}
   */
  public static <T> EntityType getEntityType(Class<T> clazz) {
    String className = clazz.getSimpleName();

    try {
      return EntityType.valueOf(parseTitleCaseToConstant(className));
    } catch (Exception e) {
      return null;
    }
  }

  /**
   * Parse title-case text to constant.<br>
   * Eg. UserProfile converts to USER_PROFILE
   *
   * @param text
   * @return
   */
  public static String parseTitleCaseToConstant(String text) {
    if (text == null)
      return text;

    List<String> splitText = new ArrayList<>();
    Matcher matcher = TITLE_CASE_REGEX_PATTERN.matcher(text);
    while (matcher.find()) {
      splitText.add(matcher.group(0).toUpperCase());
    }

    return splitText.stream().collect(Collectors.joining("_"));
  }
}
