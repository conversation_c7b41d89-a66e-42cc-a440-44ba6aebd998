package com.sell.sales.core.exception;

import com.sell.sales.core.domain.ErrorResource;
import lombok.Data;

/**
 * Created by hemants on 11/01/19.
 */
@Data
public class BaseException extends RuntimeException {
  private static final long serialVersionUID = 1L;

  private ErrorResource errorResource;
  private Object[] args;

  public BaseException(ErrorResource errorResource) {
    super(errorResource.getMessage());
    this.errorResource = errorResource;
  }

  /**
   * The constructor to provide control for dynamic error message generation. It is achieved by replacing placeholders with corresponding object
   * arguments.
   *
   * @param errorResource It comprises of corresponding error code and error message
   * @param args The arguments to the error message. These arguments will be replaced by corresponding placeholders at runtime
   */
  public BaseException(ErrorResource errorResource, Object... args) {
    super(errorResource.getMessage());
    this.errorResource = errorResource;
    this.args = args;
  }

  public String getCode() {
    return errorResource.getCode();
  }
}
