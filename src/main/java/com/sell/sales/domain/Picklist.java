package com.sell.sales.domain;

import com.sell.sales.core.domain.EntityType;
import java.util.List;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
public class Picklist {

    @Id
    private Long id;
    private String name;
    private String displayName;
    private long tenantId;
    private boolean standard;

    @OneToMany(mappedBy = "picklist", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PicklistValue> picklistValues;

    public Picklist(Long id,String name, String displayName, long tenantId, boolean standard, List<PicklistValue> picklistValues) {
        this.id=id;
        this.name = name;
        this.displayName = displayName;
        this.tenantId = tenantId;
        this.standard = standard;
        this.picklistValues = picklistValues;
    }
}
