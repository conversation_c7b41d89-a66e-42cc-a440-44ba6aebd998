package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.core.domain.Source;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MetaInfo {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private boolean isNew;
  private Date latestActivityCreatedAt;
  private Date meetingScheduledOn;
  private Date taskDueOn;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lead_id")
  @JsonIgnore
  private Lead lead;

  public MetaInfo(Boolean isNew, Date latestActivityCreatedAt, Date meetingScheduledOn,
      Date taskDueOn, Lead lead, String createdViaId, String createdViaName, String createdViaType, String updatedViaId,
      String updatedViaName, String updatedViaType) {
    this.isNew = isNew;
    this.latestActivityCreatedAt = latestActivityCreatedAt;
    this.taskDueOn = taskDueOn;
    this.meetingScheduledOn = meetingScheduledOn;
    this.lead = lead;
    this.createdViaId=createdViaId;
    this.createdViaName=createdViaName;
    this.createdViaType=createdViaType;
    this.updatedViaId=updatedViaId;
    this.updatedViaName=updatedViaName;
    this.updatedViaType=updatedViaType;
  }

  public static MetaInfo create(Lead lead, Source source) {
    if(source==null){
      return new MetaInfo(
          true,
          null,
          null,
          null,
          lead,
          null, null, null, null,null,null
      );
    }
    return new MetaInfo(
        true,
        null,
        null,
        null,
        lead,
        source.getId(), source.getName(), source.getType(), null,null,null
    );
  }

  public MetaInfo update(Date latestActivityCreatedAt, Date meetingScheduledOn, Date taskDueOn) {
    this.setNew(false);
    this.setLatestActivityCreatedAt(latestActivityCreatedAt);
    this.setMeetingScheduledOn(meetingScheduledOn);
    this.setTaskDueOn(taskDueOn);
    return this;
  }

  public MetaInfo update(Date latestActivityCreatedAt, Date meetingScheduledOn, Date taskDueOn, Source source, MetaInfo metaInfo) {
    this.setNew(false);
    this.setLatestActivityCreatedAt(latestActivityCreatedAt);
    this.setMeetingScheduledOn(meetingScheduledOn);
    this.setTaskDueOn(taskDueOn);
    if (source != null) {
      this.updatedViaId = source.getId();
      this.updatedViaName = source.getName();
      this.updatedViaType= source.getType();
    }
    return this;
  }
}
