package com.sell.sales.domain;

import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class LeadSampleData {

  @Id
  private long id;
  private long tenantId;
  private long userId;
  private String firstName;
  private String lastName;

}
