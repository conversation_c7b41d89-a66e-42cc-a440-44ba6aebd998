package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.controller.request.lead.LeadRequest;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lead_gps_address")
public class LeadGPSAddress {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "lat", column = @Column(name = "address_coordinate_latitude")),
      @AttributeOverride(name = "lon", column = @Column(name = "address_coordinate_longitude"))
  })
  private GPSCoordinate addressCoordinate;


  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "lat", column = @Column(name = "company_address_coordinate_latitude")),
      @AttributeOverride(name = "lon", column = @Column(name = "company_address_coordinate_longitude"))
  })
  private GPSCoordinate companyAddressCoordinate;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "lead_id")
  @JsonIgnore
  private Lead lead;


  public LeadGPSAddress(GPSCoordinate addressCoordinate, GPSCoordinate companyAddressCoordinate, Lead lead) {
    this.addressCoordinate = addressCoordinate;
    this.companyAddressCoordinate = companyAddressCoordinate;
    this.lead = lead;
  }

  public static LeadGPSAddress create(LeadRequest leadRequest, Lead lead) {
    if (leadRequest.getAddressCoordinate() == null && leadRequest.getCompanyAddressCoordinate() == null) {
      return null;
    }
    return new LeadGPSAddress(leadRequest.getAddressCoordinate(), leadRequest.getCompanyAddressCoordinate(), lead);
  }

  public static LeadGPSAddress update(LeadGPSAddress existingLeadGPSAddress, LeadGPSAddress newLeadGPSAddress) {
    if (newLeadGPSAddress == null) {
      return null;
    }
    existingLeadGPSAddress.addressCoordinate = newLeadGPSAddress.getAddressCoordinate();
    existingLeadGPSAddress.companyAddressCoordinate = newLeadGPSAddress.getCompanyAddressCoordinate();
    return existingLeadGPSAddress;
  }
}
