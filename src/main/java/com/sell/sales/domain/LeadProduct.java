package com.sell.sales.domain;

import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;

@Entity
@Getter
@NoArgsConstructor
@IdClass(ProductId.class)
public class LeadProduct {
  @Id
  @Column(name = "lead_id")
  private long leadId;

  @Id
  @Column(name = "product_id")
  private long productId;

  public LeadProduct(long leadId, long productId) {
    this.leadId = leadId;
    this.productId = productId;
  }
}
