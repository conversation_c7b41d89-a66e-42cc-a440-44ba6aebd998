package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "users")
public class User{

  @Id
  private Long id;
  private long tenantId;
  private String name;


  @JsonCreator
  public User(
      @JsonProperty("id") long id,
      @JsonProperty("tenantId") long tenantId,
      @JsonProperty("firstName") String firstName,
      @JsonProperty("lastName") String lastName,
      @JsonProperty("name") String name) {
    this.id = id;
    this.tenantId = tenantId;
    this.name = StringUtils.isBlank(name)
        ? computeName(firstName, lastName)
        : name;
  }

  private String computeName(String firstName, String lastName) {
    return StringUtils.isBlank(firstName) ? lastName : firstName + " " + lastName;
  }


  public static String getCreatedEventName(){
    return "iam.user.created";
  }
}
