package com.sell.sales.domain;

import com.sell.sales.controller.request.lead.LeadUpdateRequestV2;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
public class LeadUtm{

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  private String subSource;
  private String utmSource;
  private String utmMedium;
  private String utmCampaign;
  private String utmTerm;
  private String utmContent;
  private Date createdAt;
  private Date updatedAt;
  private long createdBy;
  private long updatedBy;


  // Create New Lead UTM
  public LeadUtm(String subSource, String utmSource, String utmMedium, String utmCampaign, String utmTerm, String utmContent,long createdBy,long updatedBy) {
    this.subSource = subSource;
    this.utmSource = utmSource;
    this.utmMedium = utmMedium;
    this.utmCampaign = utmCampaign;
    this.utmTerm = utmTerm;
    this.utmContent = utmContent;
    this.createdAt=new Date();
    this.updatedAt=new Date();
    this.createdBy=createdBy;
    this.updatedBy=updatedBy;
  }


  public LeadUtm(LeadUpdateRequestV2 leadUpdateRequestV2,LeadUtm leadUtm,long userId) {
    this.subSource = leadUpdateRequestV2.getSubSource()!=null? leadUpdateRequestV2.getSubSource() : leadUtm.getSubSource();
    this.utmSource = leadUpdateRequestV2.getUtmSource()!=null? leadUpdateRequestV2.getUtmSource() : leadUtm.getUtmSource();;
    this.utmMedium = leadUpdateRequestV2.getUtmMedium()!=null? leadUpdateRequestV2.getUtmMedium() : leadUtm.getUtmMedium();;
    this.utmCampaign = leadUpdateRequestV2.getUtmCampaign()!=null? leadUpdateRequestV2.getUtmCampaign() : leadUtm.getUtmCampaign();;
    this.utmTerm = leadUpdateRequestV2.getUtmTerm()!=null? leadUpdateRequestV2.getUtmTerm() : leadUtm.getUtmTerm();;
    this.utmContent = leadUpdateRequestV2.getUtmContent()!=null? leadUpdateRequestV2.getUtmContent() : leadUtm.getUtmContent();;
    this.createdAt=leadUtm.getCreatedAt();
    this.updatedAt=new Date();
    this.createdBy= leadUtm.getCreatedBy();
    this.updatedBy=userId;
  }

  public LeadUtm(LeadUtm utmToUpdate,LeadUtm existingUtm,long userId) {
    this.subSource = utmToUpdate.getSubSource()!=null? utmToUpdate.getSubSource() : existingUtm.getSubSource();
    this.utmSource = utmToUpdate.getUtmSource()!=null? utmToUpdate.getUtmSource() : existingUtm.getUtmSource();;
    this.utmMedium = utmToUpdate.getUtmMedium()!=null? utmToUpdate.getUtmMedium() : existingUtm.getUtmMedium();;
    this.utmCampaign = utmToUpdate.getUtmCampaign()!=null? utmToUpdate.getUtmCampaign() : existingUtm.getUtmCampaign();;
    this.utmTerm = utmToUpdate.getUtmTerm()!=null? utmToUpdate.getUtmTerm() : existingUtm.getUtmTerm();;
    this.utmContent = utmToUpdate.getUtmContent()!=null? utmToUpdate.getUtmContent() : existingUtm.getUtmContent();;
    this.createdAt=existingUtm.getCreatedAt();
    this.updatedAt=new Date();
    this.createdBy= existingUtm.getCreatedBy();
    this.updatedBy=userId;
  }

  // Update Existing Lead UTM
  public LeadUtm updateWith(LeadUtm requestedLeadUtm) {
    return new LeadUtm(this.id,requestedLeadUtm.getSubSource(),
        requestedLeadUtm.getUtmSource(),
        requestedLeadUtm.getUtmMedium(),
        requestedLeadUtm.getUtmCampaign(),
        requestedLeadUtm.getUtmTerm(),
        requestedLeadUtm.getUtmContent(),
        this.createdAt,
        new Date(),
        this.createdBy,
        requestedLeadUtm.getUpdatedBy());
    /*this.subSource= requestedLeadUtm.getSubSource();
    this.utmSource= requestedLeadUtm.getUtmSource();
    this.utmCampaign= requestedLeadUtm.getUtmCampaign();
    this.utmContent= requestedLeadUtm.getUtmContent();
    this.utmMedium= requestedLeadUtm.getUtmMedium();
    this.utmTerm= requestedLeadUtm.getUtmTerm();
    this.updatedBy=requestedLeadUtm.getUpdatedBy();
    this.updatedAt=new Date();*/
  }
}
