package com.sell.sales.domain;

import static java.util.stream.Collectors.toList;

import com.sell.sales.controller.request.pipeline.UpdatePipelineRequest;
import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.TenantOwnerAwareBaseEntity;
import com.sell.sales.entity.annotation.Eventable;
import com.sell.sales.entity.annotation.Internal;
import com.sell.sales.entity.annotation.LookupField;
import com.sell.sales.entity.service.IdNameEntry;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table
@Where(clause = "deleted=false")
@Setter
@Getter
@AccessPermission(value = "pipeline")
@Eventable
@AllArgsConstructor
@NoArgsConstructor
public class Pipeline extends TenantOwnerAwareBaseEntity implements IdNameEntry {

  @Enumerated(EnumType.STRING)
  private EntityType entityType;

  @NotEmpty
  private String name;

  private String[] unqualifiedReasons;
  private String[] lostReasons;

  @OneToMany(mappedBy = "pipeline", cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
  @OrderBy("position ASC")
  @Where(clause = "deleted=false")
  private List<PipelineStage> stages;

  private boolean active;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  public Pipeline update(UpdatePipelineRequest updateRequest, long userId) {
    this.setName(updateRequest.getName());
    this.setUnqualifiedReasons(getTrimmedReason(updateRequest.getUnqualifiedReasons()));
    this.setLostReasons(getTrimmedReason(updateRequest.getLostReasons()));
    List<PipelineStage> stages = buildStagesFrom(updateRequest, userId);
    this.stages.clear();
    this.stages.addAll(stages);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(userId);
    return this;
  }

  private List<PipelineStage> buildStagesFrom(UpdatePipelineRequest updateRequest, Long userId) {
    return updateRequest.getStages().stream()
        .map(stage -> {
          if (stage.getName().chars().count() > 255) {
            throw new SalesException(SalesErrorCodes.PIPELINE_STAGE_NAME_EXCEEDS_CHAR_LIMIT);
          }
          return this.getStages().stream()
                    .filter(requestStage -> requestStage.getId().equals(stage.getId()))
                    .findFirst()
                    .map(matchedRequestStage -> matchedRequestStage.update(stage, userId))
              .orElseGet(() -> PipelineStage.createFrom(stage, this, userId));
        })
            .collect(toList());
  }

  public void populateHistoricalFieldsForCreate(Long userId) {
    this.setCreatedAt(new Date());
    this.setCreatedBy(userId);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(userId);
  }

  public void trimReason() {
    this.setLostReasons(getTrimmedReason(this.getLostReasons()));
    this.setUnqualifiedReasons(getTrimmedReason(this.getUnqualifiedReasons()));
  }
  private String[] getTrimmedReason(String[] reason){
    return Arrays.stream(reason).map(s -> s.trim()).collect(toList()).toArray(new String[0]);
  }
}
