package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.entity.entity.Picklist;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
public class PicklistValue {
    @Id
    private Long id;
    private String name;
    private String displayName;
    private Long tenantId;
    private boolean disabled;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "picklist_id", nullable = false, insertable = false, updatable = false)
    @JsonIgnore
    private com.sell.sales.domain.Picklist picklist;

    @Column(name = "picklist_id")
    private Long picklistId;

    public PicklistValue(Long id, String name, String displayName, Long tenantId, boolean disabled, long picklistId) {
        this.id=id;
        this.name = name;
        this.displayName = displayName;
        this.tenantId = tenantId;
        this.disabled = disabled;
        this.picklistId = picklistId;
    }


    public static List<PicklistValue> getPicklistValues(Picklist picklist){
        List<com.sell.sales.entity.entity.PicklistValue> picklistValues = picklist.getValues();
        return picklistValues.stream()
            .map(picklistValue -> new PicklistValue(picklistValue.getId(),picklistValue.getName(),picklistValue.getDisplayName(),picklist.getTenantId(),picklistValue.isDisabled(),
                picklist.getId()))
            .collect(Collectors.toList());
    }
}
