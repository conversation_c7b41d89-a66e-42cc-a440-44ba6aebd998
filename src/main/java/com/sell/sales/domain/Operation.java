package com.sell.sales.domain;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class Operation {
  private boolean executeWorkflow;
  private boolean sendNotification;
  private boolean executeScoreRule;

  public Operation(boolean executeWorkflow, boolean sendNotification, boolean executeScoreRule) {
    this.executeWorkflow = executeWorkflow;
    this.sendNotification = sendNotification;
    this.executeScoreRule = executeScoreRule;
  }
}
