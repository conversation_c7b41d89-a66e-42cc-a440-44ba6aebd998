package com.sell.sales.domain.layout;

import static java.util.Arrays.asList;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class Layout {

  private List<LayoutItem> layoutItems;

  public Layout(List<LayoutItem> layoutItems) {
    this.layoutItems = layoutItems;
  }

  public static Layout from(List<LayoutItem> requiredFields, List<LayoutItem> otherFields) {
    return new Layout(
        asList(
            new LayoutItem("REQUIRED_FIELDS", requiredFields),
            new LayoutItem("OTHER_FIELDS", otherFields)));
  }
}
