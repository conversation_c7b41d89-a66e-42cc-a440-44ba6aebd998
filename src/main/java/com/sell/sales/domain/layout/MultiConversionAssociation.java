package com.sell.sales.domain.layout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.sell.sales.core.domain.EntityType;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@NoArgsConstructor
@EqualsAndHashCode
public class MultiConversionAssociation {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Setter
  private Long id;

  private Long tenantId;

  @Enumerated(EnumType.STRING)
  private EntityType entityType;

  private Long entityId;

  private String entityName;

  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date convertedAt;

  public MultiConversionAssociation(Long tenantId, EntityType entityType, Long entityId, String entityName, Date convertedAt) {
    this.tenantId = tenantId;
    this.entityType = entityType;
    this.entityId = entityId;
    this.entityName = entityName;
    this.convertedAt = convertedAt;
  }
}
