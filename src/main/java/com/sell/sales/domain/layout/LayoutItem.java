package com.sell.sales.domain.layout;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@Setter
public class LayoutItem {

  private LayoutItemType type;
  private String heading;
  private List<LayoutItem> layoutItems;
  private Item item;
  private int row;
  private int column;
  private int width;
  private Long id;

  public LayoutItem(String heading, List<LayoutItem> layoutItems) {
    this.heading = heading;
    this.layoutItems = layoutItems;
  }

  public enum LayoutItemType {
    SECTION,
    FIELD;
  }

}
