package com.sell.sales.domain.layout;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.entity.model.FieldType;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Item {

  private Long id;
  private Long sectionId;

  private FieldType type;
  private EntityType entity;
  private String displayName;
  private String internalName;
  private List<Object> pickLists;
  private String description;

  private boolean isRequired;
  private boolean isReadOnly;
  private boolean isStandard;
  private boolean isSortable;
  private boolean isFilterable;
  private boolean isMultiValue;
  private boolean isUnique;
  private boolean isInternal;
  private boolean isActive;

  private Integer length;
  private String greaterThan;
  private String lessThan;

  private String lookupUrl;
  private String regex;
  private boolean showDefaultOptions;
}
