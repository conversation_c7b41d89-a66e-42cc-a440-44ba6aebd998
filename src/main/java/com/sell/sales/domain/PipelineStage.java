package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.constants.InputSize;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import com.sell.sales.entity.service.IdNameEntry;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table
@Where(clause = "deleted=false")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@AccessPermission(value = "pipeline")
public class PipelineStage extends TenantAwareBaseEntity implements IdNameEntry {

  @NotBlank
  private String name;

  @Column(length = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  private String description;

  @Enumerated(EnumType.STRING)
  private ForecastingType forecastingType;
  private int position;

  @Range(min = 0, max = 100, message = "Please select a number between 0 & 100 (both inclusive)")
  private int winLikelihood;

  @ManyToOne(fetch = FetchType.LAZY)
  @JsonIgnore
  @JoinColumn(name = "pipeline_id")
  private Pipeline pipeline;

  public static PipelineStage createFrom(PipelineStage stage, Pipeline pipeline, Long userId) {
    PipelineStage pipelineStage = new PipelineStage(
        stage.getName(),
        stage.getDescription(),
        stage.getForecastingType(),
        stage.getPosition(),
        stage.getWinLikelihood(),
        pipeline);
    populateHistoricalFieldsForCreate(pipelineStage, userId);
    return pipelineStage;
  }

  public PipelineStage update(PipelineStage stage, Long userId) {
    this.setName(stage.getName());
    this.setPosition(stage.getPosition());
    this.setDescription(stage.getDescription());
    this.setWinLikelihood(stage.getWinLikelihood());
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(userId);
    return this;
  }

  private static void populateHistoricalFieldsForCreate(PipelineStage pipelineStage, Long userId) {
    pipelineStage.setCreatedAt((new Date()));
    pipelineStage.setCreatedBy(userId);
    pipelineStage.setUpdatedAt((new Date()));
    pipelineStage.setUpdatedBy(userId);
  }

  public PipelineStage populateHistoricalFieldsForCreate(Long userId) {
    this.setCreatedAt((new Date()));
    this.setCreatedBy(userId);
    this.setUpdatedAt((new Date()));
    this.setUpdatedBy(userId);
    return this;
  }
}
