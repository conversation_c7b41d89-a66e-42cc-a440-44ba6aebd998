package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.controller.request.contact.ContactCreateRequest;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "contact_gps_address")
public class ContactGPSAddress {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "lat", column = @Column(name = "address_coordinate_latitude")),
      @AttributeOverride(name = "lon", column = @Column(name = "address_coordinate_longitude"))
  })
  private GPSCoordinate addressCoordinate;


  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "contact_id")
  @JsonIgnore
  private Contact contact;


  private ContactGPSAddress(GPSCoordinate addressCoordinate,Contact contact) {
    this.addressCoordinate = addressCoordinate;
    this.contact = contact;
  }

  public static ContactGPSAddress create(GPSCoordinate gpsCoordinate,Contact contact) {
    return new
        ContactGPSAddress(gpsCoordinate, contact);
  }

  public ContactGPSAddress update(ContactGPSAddress updateGPSAddress) {
    if (ObjectUtils.isEmpty(updateGPSAddress)){
      return null;
    }
    this.addressCoordinate = updateGPSAddress.getAddressCoordinate();
    return this;
  }
}
