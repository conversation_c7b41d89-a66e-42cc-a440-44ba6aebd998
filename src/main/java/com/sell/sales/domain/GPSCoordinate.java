package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Embeddable
@Getter
public class GPSCoordinate {

  private Double lat;
  private Double lon;

  @JsonCreator
  public GPSCoordinate(@JsonProperty("lat") Double latitude,
      @JsonProperty("lon") Double longitude) {
    this.lat = latitude;
    this.lon = longitude;
  }

}
