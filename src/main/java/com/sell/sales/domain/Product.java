package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.ToString;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(exclude = {"tenantId"})
public class Product {
  @Id private long id;
  private String name;
  private long tenantId;

  public Product(long id, String name, long tenantId) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;
  }

  @JsonCreator
  public Product(@JsonProperty("id") long id, @JsonProperty("name") String name) {
    this.id = id;
    this.name = name;
  }

  public Product withName(String name) {
    return new Product(id, name, tenantId);
  }

  public Product withTenantId(long tenantId) {
    return new Product(id, name, tenantId);
  }
}
