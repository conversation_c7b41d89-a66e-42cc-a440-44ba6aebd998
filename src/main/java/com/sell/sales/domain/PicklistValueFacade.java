package com.sell.sales.domain;

import com.sell.sales.repository.PicklistRepository;
import com.sell.sales.security.UserFacade;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Selection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PicklistValueFacade {

  private final EntityManager entityManager;
  private final UserFacade userFacade;
  private final PicklistRepository picklistRepository;
  private static final String TENANT_ID = "tenantId";

  @Autowired
  public PicklistValueFacade(EntityManager entityManager, UserFacade userFacade, PicklistRepository picklistRepository) {
    this.entityManager = entityManager;
    this.userFacade = userFacade;
    this.picklistRepository = picklistRepository;
  }

  public <T> Boolean isAssociatedWithPicklistValue(String columnName, String value, Class<T> clazz) {
    log.info("started fetching ids for {}", clazz.getSimpleName());
    long tenantId = userFacade.getTenantId();
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<Tuple> query = criteriaBuilder.createQuery(Tuple.class);
    Root<T> root = query.from(clazz);
    List<Selection<?>> selectedColumns = new ArrayList<>();
    selectedColumns.add(root.get("id"));
    query.multiselect(selectedColumns).where(criteriaBuilder
        .and(criteriaBuilder.equal(root.get(TENANT_ID), tenantId), criteriaBuilder.equal(root.get(columnName), value)));
    List<Long> entityIds = entityManager.createQuery(query).getResultList()
        .stream()
        .map(tuple -> tuple.get(0, Long.class))
        .collect(Collectors.toList());
    log.info("successfully fetched ids for {}", clazz.getSimpleName());
    return !entityIds.isEmpty();
  }

  public <T> Boolean isAssociatedCustomPicklistValue(String picklistName, String value,
      Class<T> clazz) {
    log.info("started fetching ids for {}", clazz.getSimpleName());
    long tenantId = userFacade.getTenantId();

    StringBuilder sb = new StringBuilder()
        .append("select exists( select id from ").append(clazz.getSimpleName()).append(" where tenant_id =").append(tenantId).append(" and (")
        .append("jsonb_extract_path_text(custom_field, '").append(picklistName).append("') in('").append(value).append("')")
        .append(" or ")
        .append(" jsonb_extract_path(custom_field, '").append(picklistName).append("')@>'").append(value).append("' ")
        .append(" ) ")
        .append(")");


    return (Boolean) entityManager.createNativeQuery(sb.toString()).getSingleResult();
  }

  public void savePicklist(List<Picklist> picklists){
    picklistRepository.save(picklists);
  }
}
