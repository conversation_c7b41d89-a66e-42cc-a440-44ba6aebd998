package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Reassign {
    private final long entityId;
    private final long ownerId;

    @JsonCreator
    public Reassign(
            @JsonProperty("entityId") long entityId,
            @JsonProperty("ownerId") long ownerId){
        this.entityId=entityId;
        this.ownerId=ownerId;
    }
}