package com.sell.sales.domain;

import static com.sell.sales.domain.ForecastingType.CLOSED_LOST;
import static com.sell.sales.domain.ForecastingType.CLOSED_UNQUALIFIED;
import static com.sell.sales.domain.ForecastingType.CLOSED_WON;
import static com.sell.sales.domain.ForecastingType.OPEN;
import static com.sell.sales.exception.SalesErrorCodes.INACTIVE_PRODUCT;
import static com.sell.sales.exception.SalesErrorCodes.INVALID_PHONE_PAYLOAD;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.controller.request.lead.LeadUpdateRequestV2;
import com.sell.sales.controller.request.lead.PhoneNumberRequest;
import com.sell.sales.controller.request.lead.Pipeline;
import com.sell.sales.controller.request.lead.PipelineStage;
import com.sell.sales.controller.request.lead.patch.FieldOperation;
import com.sell.sales.controller.request.lead.patch.MultiPicklistField;
import com.sell.sales.controller.response.lead.PhoneNumberResponse;
import com.sell.sales.controller.response.pipeline.PipelineResponse;
import com.sell.sales.controller.response.pipeline.PipelineStageResponse;
import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.annotation.FieldAttribute;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.core.domain.ImportErrorResource;
import com.sell.sales.core.domain.Source;
import com.sell.sales.core.utils.StringUtil;
import com.sell.sales.domain.layout.MultiConversionAssociation;
import com.sell.sales.entity.annotation.Eventable;
import com.sell.sales.entity.annotation.Indexable;
import com.sell.sales.entity.annotation.Internal;
import com.sell.sales.entity.annotation.LookupField;
import com.sell.sales.entity.annotation.PicklistField;
import com.sell.sales.entity.annotation.UrlField;
import com.sell.sales.entity.model.CampaignActivity;
import com.sell.sales.entity.model.CustomFieldAwareBaseEntity;
import com.sell.sales.entity.model.Email;
import com.sell.sales.entity.model.EmailJsonUserType;
import com.sell.sales.entity.model.PhoneJsonUserType;
import com.sell.sales.entity.model.PhoneNumber;
import com.sell.sales.entity.service.IdNameEntry;
import com.sell.sales.exception.LeadConversionNotPermitted;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.infra.mq.command.RuleFieldValue;
import com.sell.sales.infra.mq.command.RuleFieldValue.RuleType;
import com.sell.sales.infra.mq.command.ScoreValueDetail;
import com.sell.sales.pipeline.domain.LeadPipeline;
import com.sell.sales.pipeline.domain.LeadPipelineStage;
import com.sell.sales.security.User;
import com.sell.sales.service.lead.UpdateType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.beans.FeatureDescriptor;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Transient;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

@Entity
@Where(clause = "deleted=false")
@Eventable
@Indexable
@AccessPermission(value = "lead")
@TypeDefs({
    @TypeDef(name = "PhoneJsonUserType", typeClass = PhoneJsonUserType.class),
    @TypeDef(name = "EmailJsonUserType", typeClass = EmailJsonUserType.class),
    @TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
})
@Getter
@Setter
@Slf4j
public class Lead extends CustomFieldAwareBaseEntity implements IdNameEntry {

  private String firstName;
  private String lastName;

  @Type(type = "PhoneJsonUserType")
  @Column(name = "phone_numbers", columnDefinition = "jsonb")
  private PhoneNumber[] phoneNumbers;

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "lead")
  private Set<LeadPhoneNumber> leadPhoneNumbers = new HashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "lead")
  private Set<LeadCompanyPhoneNumber> leadCompanyPhoneNumbers = new HashSet<>();

  // TODO: Add validation to the following
  @PicklistField(value = {"Mr", "Mrs", "Miss"})
  private Long salutation;

  private String[] photoUrls; // Needs to handle via JSONB, explore

  @Type(type = "EmailJsonUserType")
  @Column(name = "emails", columnDefinition = "jsonb")
  private Email[] emails;

  @Internal
  @PicklistField(name = "TIMEZONE")
  private String timezone;

  private String city;
  private String state;
  private String zipcode;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String country;

  // End todo add validation
  private String department;

  @FieldAttribute(displayName = "Do Not Disturb")
  private Boolean dnd;

  @UrlField
  private String facebook;
  @UrlField
  private String twitter;
  @UrlField
  private String linkedIn;

  private String address;

  private Long pipeline;
  private Long pipelineStage;
  private String pipelineStageReason;

  @OneToOne(mappedBy = "lead", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  private LeadPipeline leadPipeline;
  // company
  private String companyName;
  private String companyAddress;
  private String companyCity;
  private String companyState;
  private String companyZipcode;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String companyCountry;

  @PicklistField(name = "COMPANY_EMPLOYEES")
  private Integer companyEmployees;
  private Double companyAnnualRevenue;

  @UrlField
  private String companyWebsite;

  @Type(type = "PhoneJsonUserType")
  @Column(columnDefinition = "jsonb")
  private PhoneNumber[] companyPhones;

  @Internal
  @PicklistField(name = "INDUSTRY")
  private String companyIndustry;

  @PicklistField(name = "BUSINESS_TYPE")
  private String companyBusinessType;

  // requirement
  @FieldAttribute(displayName = "Name")
  private String requirementName;

  @Internal
  @PicklistField(name = "CURRENCY")
  @FieldAttribute(displayName = "Currency")
  private String requirementCurrency;

  @FieldAttribute(displayName = "Budget")
  private Double requirementBudget;
  private Date expectedClosureOn;

  private Date actualClosureDate;

  @OneToMany(fetch = FetchType.EAGER)
  @JoinTable(
      name = "lead_product",
      joinColumns = @JoinColumn(name = "lead_id"),
      inverseJoinColumns = @JoinColumn(name = "product_id"))
  private List<Product> products;

  @Transient
  private ConversionAssociation conversionAssociation;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "lead", orphanRemoval = true)
  private MetaInfo metaInfo;

  @Internal
  private Date convertedAt;

  @Internal
  @LookupField(EntityType.USER)
  private Long convertedBy;

  private String designation;

  @PicklistField(name = "CAMPAIGN")
  @FieldAttribute(displayName = "Campaign")
  private Long campaign;

  @PicklistField(name = "SOURCE")
  @FieldAttribute(displayName = "Source")
  private Long source;

  @Enumerated(EnumType.STRING)
  private ForecastingType forecastingType;

  private Long importedBy;

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "lead_id", nullable = false)
  @OrderBy("id DESC")
  private Set<LeadUtm> leadUtms = new LinkedHashSet<>();

  @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "lead_id", nullable = false)
  private Set<MultiConversionAssociation> multiConversionAssociations = new LinkedHashSet<>();

  private Double score;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "lead", orphanRemoval = true)
  private LeadGPSAddress leadGPSAddress;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private Set<CampaignActivity> campaignActivities;

  @Getter(AccessLevel.NONE)
  @Transient
  private Operation operation;

  @Transient
  private boolean checkUniqueness = true;

  @Transient
  private boolean requestFromImport = false;

  @Transient
  private long importJobId;

  @Transient
  private UpdateType updateType = UpdateType.REGULAR;

  @Transient
  private long oldOwnerId;

  @Transient
  private boolean errorInImport = false;

  @Transient
  private List<ImportErrorResource> errors = new ArrayList<>();

  @Transient
  private List<String> maskedFields;

  @Transient
  private boolean publishV2=true;

  public static Lead createNew(Lead leadToCreate, List<Product> products, PipelineResponse leadPipeline, User creator,
      Source source) {
    Lead lead = new Lead();
    long ownerId = getOwnerId(leadToCreate.getOwnerId(), creator.getUserId());
    lead.setOwnerId(ownerId);
    lead.setOldOwnerId(creator.getUserId());
    if (lead.isOwnerChange()) {
      lead.setExplicitOwnerId(true);
    }
    lead.setCustomFieldValues(leadToCreate.getTrimmedCustomFieldValues(leadToCreate.getCustomFieldValues()));
    lead.setFirstName(leadToCreate.getFirstName());
    lead.setLastName(leadToCreate.getLastName());

    lead.setPhoneNumbers(leadToCreate.getUpdatedPhone(leadToCreate.getPhoneNumbers()));
    lead.addLeadPhones(lead.getPhoneNumbers());

    lead.setEmails(leadToCreate.getUpdatedEmail(leadToCreate.getEmails()));
    lead.setSalutation(leadToCreate.getSalutation());
    lead.setPhotoUrls(leadToCreate.getPhotoUrls());

    lead.setTimezone(leadToCreate.getTimezone());
    lead.setAddress(leadToCreate.getAddress());
    lead.setCity(leadToCreate.getCity());
    lead.setState(leadToCreate.getState());
    lead.setZipcode(leadToCreate.getZipcode());
    lead.setCountry(leadToCreate.getCountry());

    lead.setDepartment(leadToCreate.getDepartment());
    lead.setDnd(leadToCreate.getDnd());

    lead.setFacebook(leadToCreate.getFacebook());
    lead.setTwitter(leadToCreate.getTwitter());
    lead.setLinkedIn(leadToCreate.getLinkedIn());

    populateNewPipeline(lead, leadToCreate.getPipeline(), leadToCreate.getPipelineStage(), leadToCreate.getPipelineStageReason(),
        leadToCreate.getActualClosureDate(), leadPipeline, creator);

    lead.setCompanyName(leadToCreate.getCompanyName());
    lead.setCompanyAddress(leadToCreate.getCompanyAddress());
    setLeadGpsAddress(lead,leadToCreate);
    lead.setCompanyCity(leadToCreate.getCompanyCity());
    lead.setCompanyState(leadToCreate.getCompanyState());
    lead.setCompanyZipcode(leadToCreate.getCompanyZipcode());
    lead.setCompanyCountry(leadToCreate.getCompanyCountry());
    lead.setCompanyEmployees(leadToCreate.getCompanyEmployees());
    lead.setCompanyAnnualRevenue(leadToCreate.getCompanyAnnualRevenue());
    lead.setCompanyWebsite(leadToCreate.getCompanyWebsite());
    lead.setCompanyPhones(leadToCreate.getUpdatedPhone(leadToCreate.getCompanyPhones()));
    lead.addLeadCompanyPhones(lead.getCompanyPhones());
    lead.setCompanyIndustry(leadToCreate.getCompanyIndustry());
    lead.setCompanyBusinessType(leadToCreate.getCompanyBusinessType());

    lead.setRequirementCurrency(leadToCreate.getRequirementCurrency());
    lead.setRequirementName(leadToCreate.getRequirementName());
    lead.setRequirementBudget(leadToCreate.getRequirementBudget());

    lead.setExpectedClosureOn(leadToCreate.getExpectedClosureOn());

    lead.setProducts(products);

    MetaInfo metaInfo = MetaInfo.create(lead, source);
    lead.setMetaInfo(metaInfo);

    lead.setDesignation(leadToCreate.getDesignation());
    lead.setSource(leadToCreate.getSource());
    lead.setCampaign(leadToCreate.getCampaign());
    lead.setImportedBy(leadToCreate.getImportedBy());
    lead.setLeadUtms(leadToCreate.getLeadUtms());
    lead.setCreatedAt(leadToCreate.getCreatedAt());
    lead.setUpdatedAt(leadToCreate.getUpdatedAt());
    lead.setUpdatedBy(leadToCreate.getUpdatedBy());
    lead.setCreatedBy(leadToCreate.getCreatedBy());
    lead.setRequestFromImport(leadToCreate.isRequestFromImport());
    lead.setErrorInImport(leadToCreate.isErrorInImport());
    lead.setErrors(leadToCreate.getErrors());
    lead.setScore(ObjectUtils.isEmpty(leadToCreate.getScore()) ? 0.00
        : BigDecimal.valueOf(leadToCreate.getScore()).setScale(2, RoundingMode.HALF_DOWN).doubleValue());

    return lead;
  }

  private static void setLeadGpsAddress(Lead lead, Lead leadToCreate) {
    if(leadToCreate.getLeadGPSAddress()!=null){
      lead.setLeadGPSAddress(new LeadGPSAddress(leadToCreate.getLeadGPSAddress().getAddressCoordinate(),leadToCreate.getLeadGPSAddress().getCompanyAddressCoordinate(),lead));
    }
  }

  private void addLeadCompanyPhones(PhoneNumber[] companyPhones) {
    if (ObjectUtils.isEmpty(companyPhones)) {
      return;
    }
    leadCompanyPhoneNumbers = toLeadCompanyPhoneNumber(companyPhones);
  }

  private void addLeadPhones(PhoneNumber[] phoneNumbers) {
    if (ObjectUtils.isEmpty(phoneNumbers)) {
      return;
    }
    leadPhoneNumbers = toLeadPhoneNumber(phoneNumbers);
  }

  public PhoneNumberResponse[] getLeadPhones() {
    if (ObjectUtils.isEmpty(leadPhoneNumbers)) {
      return null;
    }
    boolean maskValue = ObjectUtils.isNotEmpty(this.maskedFields) && this.maskedFields.contains("phoneNumbers");
    return leadPhoneNumbers.stream().map(
            leadPhoneNumber -> {
              PhoneNumberResponse leadPhoneNumberResponse = new PhoneNumberResponse(leadPhoneNumber.getId(), leadPhoneNumber.getType(),
                  leadPhoneNumber.getCode(),
                  leadPhoneNumber.getValue(), leadPhoneNumber.getDialCode(), leadPhoneNumber.isPrimary());
              if (maskValue) {
                leadPhoneNumberResponse.mask();
              }
              return leadPhoneNumberResponse;
            })
        .toArray(PhoneNumberResponse[]::new);
  }

  public PhoneNumberResponse[] getLeadCompanyPhones() {
    if (ObjectUtils.isEmpty(leadCompanyPhoneNumbers)) {
      return null;
    }
    boolean maskValue = ObjectUtils.isNotEmpty(this.maskedFields) && this.maskedFields.contains("companyPhones");
    return leadCompanyPhoneNumbers.stream().map(
            leadPhoneNumber -> {
              PhoneNumberResponse leadPhoneNumberResponse = new PhoneNumberResponse(leadPhoneNumber.getId(), leadPhoneNumber.getType(),
                  leadPhoneNumber.getCode(),
                  leadPhoneNumber.getValue(), leadPhoneNumber.getDialCode(), leadPhoneNumber.isPrimary());
              if (maskValue) {
                leadPhoneNumberResponse.mask();
              }
              return leadPhoneNumberResponse;
            })
        .toArray(PhoneNumberResponse[]::new);
  }

  private static long getOwnerId(Long requestedOwnerId, long loggedInUserId) {
    if (requestedOwnerId == null) {
      return loggedInUserId;
    }
    return requestedOwnerId;
  }

  private static void populateNewPipeline(Lead lead, Long pipelineId, Long pipelineStageId, String pipelineStageReason, Date actualClosureDate,
      PipelineResponse pipelineResponse, User creator) {
    if (pipelineId == null) {
      lead.setPipeline(null);
      lead.setPipelineStage(null);
      lead.setPipelineStageReason(null);
      lead.setActualClosureDate(null);
      lead.setForecastingType(null);
      return;
    }
    if (pipelineStageId == null) {
      LeadPipeline leadPipeline = LeadPipeline.create(creator.getTenantId(), lead, pipelineResponse).activateFirstStage();
      LeadPipelineStage currentStage = leadPipeline.getCurrentStage();
      lead.setPipeline(leadPipeline.getPipelineId());
      lead.setPipelineStage(currentStage.getPipelineStageId());
      lead.setForecastingType(currentStage.getForecastingType());
      lead.setActualClosureDate(null);
      lead.setPipelineStageReason(null);
      lead.setLeadPipeline(leadPipeline);
      return;
    }

    LeadPipeline leadPipeline = LeadPipeline.create(creator.getTenantId(), lead, pipelineResponse)
        .setActiveStage(pipelineStageId, pipelineStageReason);
    LeadPipelineStage currentStage = leadPipeline.getCurrentStage();
    lead.setPipeline(leadPipeline.getPipelineId());
    lead.setPipelineStage(currentStage.getPipelineStageId());
    lead.setForecastingType(currentStage.getForecastingType());
    lead.setActualClosureDate(getActualClosureDate(currentStage.getForecastingType(), actualClosureDate));
    lead.setPipelineStageReason(getReason(currentStage.getForecastingType(), pipelineStageReason));
    lead.setLeadPipeline(leadPipeline);

  }

  private static Date getActualClosureDate(ForecastingType forecastingType, Date actualClosureDate) {
    if (OPEN.equals(forecastingType)) {
      return null;
    }
    if (CLOSED_WON.equals(forecastingType) && ObjectUtils.isNotEmpty(actualClosureDate)) {
      return actualClosureDate;
    }
    if (ObjectUtils.isEmpty(actualClosureDate)) {
      return new Date();
    }
    return actualClosureDate;
  }

  private static String getReason(ForecastingType forecastingType, String pipelineStageReason) {
    if (OPEN.equals(forecastingType) || CLOSED_WON.equals(forecastingType)) {
      return null;
    }
    return pipelineStageReason;
  }

  private Email[] getUpdatedEmail(Email[] emails) {
    if (ObjectUtils.isEmpty(emails)) {
      return null;
    }
    if (emails.length == 1) {
      Email email = Email.createNew(emails[0]);
      email.markAsPrimary();
      return new Email[]{email};
    }
    List<Email> emailList = Arrays.stream(emails)
        .map(email -> Email.createNew(email))
        .collect(Collectors.toList());
    List<Email> collect = emailList.stream()
        .filter(email -> email.isPrimary()).collect(Collectors.toList());

    if (collect.size() != 1) {
      this.processException("emails", SalesErrorCodes.INVALID_EMAIL_PAYLOAD);
      return null;
    }
    return Arrays.stream(emails).toArray(Email[]::new);
  }

  private PhoneNumber[] getUpdatedPhone(PhoneNumber[] phoneNumbers) {
    if (ObjectUtils.isEmpty(phoneNumbers)) {
      return null;
    }
    Set<PhoneNumber> phoneNumberSet = new HashSet<>(Arrays.asList(phoneNumbers));
    if (phoneNumberSet.size() != phoneNumbers.length) {
      this.processException("phoneNumbers", SalesErrorCodes.DUPLICATE_PHONES);
      return null;
    }
    if (phoneNumbers.length == 1) {
      PhoneNumber phone = PhoneNumber.createNew(phoneNumbers[0]);
      phone.markAsPrimary();
      return new PhoneNumber[]{phone};
    }
    List<PhoneNumber> phoneNumberList = Arrays.stream(phoneNumbers)
        .map(phoneNumber -> PhoneNumber.createNew(phoneNumber))
        .collect(Collectors.toList());
    List<PhoneNumber> collect = phoneNumberList.stream()
        .filter(phoneNumber -> phoneNumber.isPrimary()).collect(Collectors.toList());

    if (collect.size() != 1) {
      this.processException("phoneNumbers", SalesErrorCodes.INVALID_PHONE_PAYLOAD);
      return null;
    }
    return Arrays.stream(phoneNumbers).toArray(PhoneNumber[]::new);
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }

  @Override
  public String getName() {
    return StringUtil.join(" ", getFirstName(), getLastName());
  }

  public void convertLead(User loggedInUser, ConversionAssociation conversionAssociation,
      Set<MultiConversionAssociation> multiConversionAssociation) {
    if (loggedInUser.canConvertLead(this.getOwnerId())) {
      this.convertedBy = loggedInUser.getUserId();
      this.convertedAt = this.actualClosureDate = new Date();
      this.setUpdatedBy(loggedInUser.getUserId());
      this.setUpdatedAt(new Date());
      this.conversionAssociation = conversionAssociation;
      addMultiConversionAssociations(multiConversionAssociation);
      if (ObjectUtils.isNotEmpty(this.leadPipeline)) {
        this.leadPipeline = this.leadPipeline.closePipelineForConvertedLead()
            .map(leadPipeline1 -> {
              leadPipeline1.findClosedWonStageId()
                  .ifPresent(aLong -> {
                    this.setPipelineStage(aLong);
                    this.setForecastingType(CLOSED_WON);
                  });
              return leadPipeline1;
            }).orElseThrow(() -> {
              log.error("Unable to close pipeline for leadId {}", this.getId());
              return new SalesException(SalesErrorCodes.PIPELINE_STAGE_NOT_EXISTS);
            });
      }
    } else {
      log.error("User " + loggedInUser.getUserId() + " does not have permissions for converting lead " + this.getId());
      throw new LeadConversionNotPermitted();
    }
  }

  @JsonIgnore
  public boolean isConverted() {
    return this.convertedBy != null;
  }

  public Operation getOperation() {
    return operation == null ? new Operation(true, true, true) : operation;
  }

  public void patchIdName(LeadUpdateRequestV2 entity) {
    if (!ObjectUtils.isEmpty(entity.getOwnerId())) {
      this.setOwnerId(entity.getOwnerId().getId());
    }

    if (!ObjectUtils.isEmpty(entity.getPipeline())) {
      this.setPipeline(entity.getPipeline().getId());
      this.setPipelineStage(entity.getPipeline().getStage().getId());
    }
  }

  public void addLeadUtmValues(Set<LeadUtm> leadUtms) {
    this.leadUtms.clear();
    this.leadUtms.addAll(leadUtms);
  }

  public void addMultiConversionAssociations(Set<MultiConversionAssociation> conversionAssociations) {
    this.multiConversionAssociations.clear();
    this.multiConversionAssociations.addAll(conversionAssociations);
  }

  public void update(Lead entity, Map<Long, com.sell.sales.service.client.product.response.Product> productResponse,
      User loggedInUser, PipelineResponse pipelineResponse) {
    if (!this.canUpdate()) {
      entity.processException("User's Permission", ErrorCodes.COMMON_RECORD_PERMISSION_ERROR);
    }
    this.setOwnerId(entity.getOwnerId());
    this.setCustomFieldValues(getTrimmedCustomFieldValues(entity.getCustomFieldValues()));

    this.firstName = entity.getFirstName();
    this.lastName = entity.getLastName();
    this.phoneNumbers = entity.getPhoneNumbers();
    this.salutation = entity.getSalutation();
    this.photoUrls = entity.getPhotoUrls();
    this.emails = entity.getEmails();
    this.timezone = entity.getTimezone();
    this.city = entity.getCity();
    this.state = entity.getState();
    this.zipcode = entity.getZipcode();
    this.country = entity.getCountry();
    this.department = entity.getDepartment();
    this.dnd = entity.getDnd();
    this.facebook = entity.getFacebook();
    this.twitter = entity.getTwitter();
    this.linkedIn = entity.getLinkedIn();
    this.address = entity.getAddress();
    this.companyName = entity.getCompanyName();
    this.companyAddress = entity.getCompanyAddress();
    this.companyCity = entity.getCompanyCity();
    this.companyState = entity.getCompanyState();
    this.companyZipcode = entity.getCompanyZipcode();
    this.companyCountry = entity.getCompanyCountry();
    this.companyEmployees = entity.getCompanyEmployees();
    this.companyAnnualRevenue = entity.getCompanyAnnualRevenue();
    this.companyWebsite = entity.getCompanyWebsite();
    this.companyPhones = entity.getCompanyPhones();
    this.companyIndustry = entity.getCompanyIndustry();
    this.companyBusinessType = entity.getCompanyBusinessType();
    this.requirementName = entity.getRequirementName();
    this.requirementCurrency = entity.getRequirementCurrency();
    this.requirementBudget = entity.getRequirementBudget();
    this.expectedClosureOn = entity.getExpectedClosureOn();
    this.actualClosureDate = entity.getActualClosureDate();
    this.products = getProductDetails(productResponse, this.getProducts());
    this.designation = entity.getDesignation();
    this.campaign = entity.getCampaign();
    this.source = entity.getSource();
    this.errorInImport = entity.isErrorInImport();
    this.errors = entity.getErrors();
    this.score = ObjectUtils.isEmpty(entity.getScore()) ? 0.00
        : BigDecimal.valueOf(entity.getScore()).setScale(2, RoundingMode.HALF_DOWN).doubleValue();
    this.setUpdatedBy(loggedInUser.getUserId());
    this.setUpdatedAt(new Date());
    populateLeadPipelineDetails(entity, loggedInUser, pipelineResponse);
    updateUtmForRegularUpdate(entity);
    updateLeadGpsAddress(entity);
    updateLeadPhoneNumbers(entity);
    updateLeadCompanyPhoneNumbers(entity);
    updatePrimaryOfEmailIfNeeded();
    updatePrimaryOfPhoneIfNeeded();
    validateCompanyPhoneNumber();
  }


  private void updateLeadPhoneNumbers(Lead entity) {
    if (ObjectUtils.isEmpty(entity.getLeadPhoneNumbers())) {
      this.getLeadPhoneNumbers().clear();
      return;
    }

    Map<Long, LeadPhoneNumber> existingLeadPhoneNumberMap = ObjectUtils.isEmpty(this.getLeadPhoneNumbers()) ? null
        : this.getLeadPhoneNumbers().stream().collect(
            Collectors.toMap(LeadPhoneNumber::getId, leadPhoneNumber -> leadPhoneNumber));
    boolean isMaskingEnabled = ObjectUtils.isNotEmpty(this.maskedFields) && this.maskedFields.contains("phoneNumbers");
    Set<LeadPhoneNumber> updatedLeadPhoneNumbers = entity.getLeadPhoneNumbers().stream().map(
            leadPhoneNumber -> {
              LeadPhoneNumber leadPhoneNumber1;
              if (leadPhoneNumber.getId() != null) {
                validateExistingPhoneNumbers(leadPhoneNumber, existingLeadPhoneNumberMap, entity);
                leadPhoneNumber1 = existingLeadPhoneNumberMap.get(leadPhoneNumber.getId());
                leadPhoneNumber1.setCode(leadPhoneNumber.getCode());
                if (!(isMaskingEnabled && leadPhoneNumber.getValue().contains("****"))) {
                  leadPhoneNumber1.setValue(leadPhoneNumber.getValue());
                }
                leadPhoneNumber1.setPrimary(leadPhoneNumber.isPrimary());
                leadPhoneNumber1.setDialCode(leadPhoneNumber.getDialCode());
                leadPhoneNumber1.setType(leadPhoneNumber.getType());
                return leadPhoneNumber1;
              }
              leadPhoneNumber1 = new LeadPhoneNumber(leadPhoneNumber.getType(), leadPhoneNumber.getCode(),
                  leadPhoneNumber.getValue(),
                  leadPhoneNumber.getDialCode(), this, leadPhoneNumber.isPrimary());
              return leadPhoneNumber1;
            })
        .collect(Collectors.toSet());

    HashSet<PhoneNumber> phoneNumberSet = new HashSet<>(Arrays.asList(PhoneNumber.fromLeadPhoneNumbers(updatedLeadPhoneNumbers)));
    if (phoneNumberSet.size() != updatedLeadPhoneNumbers.size()){
      throw new SalesException(SalesErrorCodes.DUPLICATE_PHONES);
    }

    this.getLeadPhoneNumbers().clear();
    this.getLeadPhoneNumbers().addAll(updatedLeadPhoneNumbers);
    this.setPhoneNumbers(PhoneNumber.fromLeadPhoneNumbers(this.leadPhoneNumbers));
  }

  private void validateExistingPhoneNumbers(LeadPhoneNumber leadPhoneNumber, Map<Long, LeadPhoneNumber> existingLeadPhoneNumberMap,
      Lead lead) {
    if (MapUtils.isEmpty(existingLeadPhoneNumberMap) || !existingLeadPhoneNumberMap.containsKey(leadPhoneNumber.getId())) {
      lead.processException("phoneNumber", SalesErrorCodes.INVALID_MOBILE_NUMBER);
    }
  }

  private void updateLeadCompanyPhoneNumbers(Lead entity) {
    if (ObjectUtils.isEmpty(entity.getLeadCompanyPhoneNumbers())) {
      this.getLeadCompanyPhoneNumbers().clear();
      return;
    }
    boolean isMaskingEnabled = ObjectUtils.isNotEmpty(this.maskedFields) && this.maskedFields.contains("companyPhones");
    Map<Long, LeadCompanyPhoneNumber> existingLeadCompanyPhoneNumberMap = ObjectUtils.isEmpty(this.getLeadCompanyPhoneNumbers()) ? null
        : this.getLeadCompanyPhoneNumbers().stream().collect(
            Collectors.toMap(LeadCompanyPhoneNumber::getId, leadCompanyPhoneNumber -> leadCompanyPhoneNumber));
    Set<LeadCompanyPhoneNumber> updatedLeadCompanyPhoneNumbers = entity.getLeadCompanyPhoneNumbers().stream().map(
            leadCompanyPhoneNumber -> {
              LeadCompanyPhoneNumber leadCompanyPhoneNumber1;
              if (leadCompanyPhoneNumber.getId() != null) {
                validateExistingLeadCompanyPhoneNumber(leadCompanyPhoneNumber, existingLeadCompanyPhoneNumberMap, entity);
                leadCompanyPhoneNumber1 = existingLeadCompanyPhoneNumberMap.get(leadCompanyPhoneNumber.getId());
                leadCompanyPhoneNumber1.setCode(leadCompanyPhoneNumber.getCode());
                if (!(isMaskingEnabled && leadCompanyPhoneNumber.getValue().contains("****"))) {
                  leadCompanyPhoneNumber1.setValue(leadCompanyPhoneNumber.getValue());
                }
                leadCompanyPhoneNumber1.setPrimary(leadCompanyPhoneNumber.isPrimary());
                leadCompanyPhoneNumber1.setDialCode(leadCompanyPhoneNumber.getDialCode());
                leadCompanyPhoneNumber1.setType(leadCompanyPhoneNumber.getType());
                return leadCompanyPhoneNumber1;

              }
              leadCompanyPhoneNumber1 = new LeadCompanyPhoneNumber(leadCompanyPhoneNumber.getType(), leadCompanyPhoneNumber.getCode(),
                  leadCompanyPhoneNumber.getValue(),
                  leadCompanyPhoneNumber.getDialCode(), this, leadCompanyPhoneNumber.isPrimary());
              return leadCompanyPhoneNumber1;
            })
        .collect(Collectors.toSet());

    HashSet<PhoneNumber> phoneNumberSet = new HashSet<>(Arrays.asList(PhoneNumber.fromLeadCompanyPhoneNumbers(updatedLeadCompanyPhoneNumbers)));
    if (phoneNumberSet.size() != updatedLeadCompanyPhoneNumbers.size()){
      throw new SalesException(SalesErrorCodes.DUPLICATE_COMPANY_PHONES);
    }

    this.getLeadCompanyPhoneNumbers().clear();
    this.getLeadCompanyPhoneNumbers().addAll(updatedLeadCompanyPhoneNumbers);
    this.setCompanyPhones(PhoneNumber.fromLeadCompanyPhoneNumbers(this.leadCompanyPhoneNumbers));
  }

  private void validateExistingLeadCompanyPhoneNumber(LeadCompanyPhoneNumber leadCompanyPhoneNumber,
      Map<Long, LeadCompanyPhoneNumber> existingLeadCompanyPhoneNumberMap, Lead lead) {
    if (MapUtils.isEmpty(existingLeadCompanyPhoneNumberMap) || !existingLeadCompanyPhoneNumberMap.containsKey(leadCompanyPhoneNumber.getId())) {
      lead.processException("companyPhones", SalesErrorCodes.INVALID_MOBILE_NUMBER);
    }
  }

  private boolean canUpdate() {
    return this.getRecordActions().isUpdate();
  }

  private void updateUtmForRegularUpdate(Lead entity) {
    if (UpdateType.REGULAR.equals(entity.updateType)) {
      if (ObjectUtils.isNotEmpty(this.getLeadUtms())) {
        Set<LeadUtm> updatedUtm = getUpdatedUtm(this.getLeadUtms(), entity.getLeadUtms());
        this.addLeadUtmValues(updatedUtm);
        return;
      }
      this.addLeadUtmValues(entity.getLeadUtms());
    }
  }

  private void updateLeadGpsAddress(Lead entity) {
      if (this.leadGPSAddress!=null) {
        LeadGPSAddress updatedGpsAddress = getUpdatedGpsAddress(this.leadGPSAddress, entity.getLeadGPSAddress());
        this.setLeadGPSAddress(updatedGpsAddress);
        return;
      }
      this.setLeadGPSAddress(entity.getLeadGPSAddress());
  }

  private LeadGPSAddress getUpdatedGpsAddress(LeadGPSAddress existingGpsAddress, LeadGPSAddress newGpsAddress) {
    return LeadGPSAddress.update(existingGpsAddress,newGpsAddress);
  }

  private void populateLeadPipelineDetails(Lead entity, User loggedInUser, PipelineResponse pipelineResponse) {
    if (entity.getPipeline() == null) {
      if (this.leadPipeline != null) {
        this.leadPipeline.setLead(null);
      }
      this.pipeline = null;
      this.leadPipeline = null;
      this.pipelineStage = null;
      this.pipelineStageReason = null;
      this.actualClosureDate = null;
      this.forecastingType = null;
      return;
    }
    if (this.pipeline != null && (this.pipeline.equals(entity.getPipeline()) && this.pipelineStage.equals(entity.getPipelineStage()))) {
      return;
    }
    if (this.pipeline != null && this.pipeline.equals(entity.getPipeline())) {
      LeadPipeline existingLeadPipeline = this.leadPipeline;
      LeadPipeline pipelineWithUpdatedStages = existingLeadPipeline.withStages(pipelineResponse.getStages());
      this.leadPipeline = pipelineWithUpdatedStages.setActiveStage(entity.getPipelineStage(), entity.getPipelineStageReason());
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.pipeline = leadPipeline.getPipelineId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, entity.getActualClosureDate());
      return;
    }
    if (entity.getPipeline() != null && entity.getPipelineStage() != null) {
      this.leadPipeline = LeadPipeline.create(loggedInUser.getTenantId(), this, pipelineResponse)
          .setActiveStage(entity.getPipelineStage(), entity.getPipelineStageReason());
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.pipeline = leadPipeline.getPipelineId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, entity.getActualClosureDate());
      return;
    }
    if (entity.getPipeline() != null && entity.getPipelineStage() == null) {
      this.leadPipeline = LeadPipeline.create(loggedInUser.getTenantId(), this, pipelineResponse)
          .activateFirstStage();
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.pipeline = leadPipeline.getPipelineId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, entity.getActualClosureDate());
      return;
    }
  }

  private Map<String, Object> getTrimmedCustomFieldValues(Map<String, Object> customFieldValues) {
    if (customFieldValues == null) {
      return emptyMap();
    }
    return customFieldValues
        .entrySet()
        .stream()
        .map(stringObjectEntry -> this.getStringToObjectEntry(stringObjectEntry))
        .filter(stringObjectEntry -> stringObjectEntry != null)
        .collect(
            Collectors.toMap(
                Entry::getKey, Entry::getValue
            )
        );
  }

  private Map.Entry<String, Object> getStringToObjectEntry(Entry<String, Object> customFieldEntry) {
    if (Objects.nonNull(customFieldEntry.getValue()) && customFieldEntry.getValue() instanceof String) {
      String trimmedValue = String.valueOf(customFieldEntry.getValue()).trim();
      return ObjectUtils.isEmpty(trimmedValue) ? null : new SimpleEntry<>(customFieldEntry.getKey(), trimmedValue);
    }
    if (ObjectUtils.isEmpty(customFieldEntry.getValue()) && customFieldEntry.getValue() instanceof ArrayList) {
      return null;
    }
    if (ObjectUtils.isEmpty(customFieldEntry.getValue())) {
      this.processException(customFieldEntry.getKey(), SalesErrorCodes.INVALID_CUSTOM_FIELD_VALUE);
      return null;
    }
    return customFieldEntry;
  }

  private void updatePrimaryOfEmailIfNeeded() {
    Email[] emails = this.getEmails();
    if (ArrayUtils.isEmpty(emails)) {
      return;
    }
    if (emails.length == 1) {
      Arrays.stream(emails).findFirst().ifPresent(email -> email.setPrimary(true));
    } else {
      long primaryCount = Arrays.stream(emails).filter(Email::isPrimary).count();
      if (primaryCount != 1) {
        log.error("At least and at most one email should be marked as primary");
        this.processException("email", SalesErrorCodes.INVALID_EMAIL_PAYLOAD);
      }
    }
  }

  private void updatePrimaryOfPhoneIfNeeded() {
    PhoneNumber[] phoneNumbers = this.getPhoneNumbers();
    if (ArrayUtils.isEmpty(phoneNumbers)) {
      return;
    }
    if (phoneNumbers.length == 1) {
      Arrays.stream(phoneNumbers)
          .findFirst()
          .ifPresent(phoneNumber -> phoneNumber.setPrimary(true));
      leadPhoneNumbers.stream()
          .findFirst()
          .ifPresent(phoneNumber -> phoneNumber.setPrimary(true));
    } else {
      long primaryCount = Arrays.stream(phoneNumbers).filter(PhoneNumber::isPrimary).count();
      if (primaryCount != 1) {
        log.error("At least and at most one phone number should be marked as primary");
        this.processException("phoneNumber", SalesErrorCodes.INVALID_PHONE_PAYLOAD);
      }
    }
  }

  private void validateCompanyPhoneNumber() {
    if (ArrayUtils.isEmpty(companyPhones)) {
      return;
    }
    if (companyPhones.length == 1) {
      Arrays.stream(companyPhones)
          .findFirst()
          .ifPresent(phoneNumber -> phoneNumber.setPrimary(true));
      leadCompanyPhoneNumbers.stream()
          .findFirst()
          .ifPresent(phoneNumber -> phoneNumber.setPrimary(true));
    } else {
      long primaryCount = Arrays.stream(companyPhones).filter(PhoneNumber::isPrimary).count();
      if (primaryCount != 1) {
        log.error("At least and at most one phone number should be marked as primary");
        this.processException("companyPhones", INVALID_PHONE_PAYLOAD);
      }
    }
  }

  private Set<LeadUtm> getUpdatedUtm(Set<LeadUtm> existingLeadUtms, Set<LeadUtm> requestedLeadUtms) {
    Set<LeadUtm> utms = new LinkedHashSet<>();
    LeadUtm persistedLeadUtm = existingLeadUtms.iterator().next();
    Iterator<LeadUtm> iterator = requestedLeadUtms.iterator();
    LeadUtm requestedLeadUtm = iterator.hasNext() ? iterator.next() : new LeadUtm();
    if (ObjectUtils.notEqual(persistedLeadUtm.getUtmCampaign(), requestedLeadUtm.getUtmCampaign()) ||
        ObjectUtils.notEqual(persistedLeadUtm.getUtmContent(), requestedLeadUtm.getUtmContent()) ||
        ObjectUtils.notEqual(persistedLeadUtm.getUtmMedium(), requestedLeadUtm.getUtmMedium()) ||
        ObjectUtils.notEqual(persistedLeadUtm.getSubSource(), requestedLeadUtm.getSubSource()) ||
        ObjectUtils.notEqual(persistedLeadUtm.getUtmTerm(), requestedLeadUtm.getUtmTerm()) ||
        ObjectUtils.notEqual(persistedLeadUtm.getUtmSource(), requestedLeadUtm.getUtmSource())) {
      LeadUtm leadUtm = persistedLeadUtm.updateWith(requestedLeadUtm);
      utms.add(leadUtm);
    } else {
      utms.add(persistedLeadUtm);
    }
    return utms;
  }

  private List<Product> getProductDetails(Map<Long, com.sell.sales.service.client.product.response.Product> productsByIds,
      List<Product> existingProducts) {

    Map<Long, Product> existingProductsByIds = existingProducts
        .stream()
        .collect(Collectors.toMap(Product::getId, product -> product, (productOne, productTwo) -> productOne));

    return productsByIds
        .entrySet()
        .stream()
        .map(product -> validateProductIsInactiveElseReturn(existingProductsByIds, product))
        .map(this::toDomainProduct)
        .collect(Collectors.toList());
  }

  private com.sell.sales.domain.Product toDomainProduct(com.sell.sales.service.client.product.response.Product product) {
    com.sell.sales.domain.Product domainProduct = new com.sell.sales.domain.Product();
    domainProduct.setId(product.getId());
    domainProduct.setName(product.getName());
    return domainProduct;
  }

  private com.sell.sales.service.client.product.response.Product validateProductIsInactiveElseReturn(Map<Long, Product> existingProductsByIds,
      Entry<Long, com.sell.sales.service.client.product.response.Product> product) {
    if (!existingProductsByIds.containsKey(product.getKey()) && !product.getValue().isActive()) {
      this.processException("products", INACTIVE_PRODUCT);
      return null;
    }
    return product.getValue();
  }

  public Lead clone() {
    Lead oldLead = new Lead();
    BeanUtils.copyProperties(this, oldLead);

    oldLead.setCustomFieldValues(null);
    Map<String, Object> oldCustomFieldValues =
        this.getCustomFieldValues() == null
            ? null
            : SerializationUtils.clone(new HashMap<>(this.getCustomFieldValues()));
    oldLead.setCustomFieldValues(oldCustomFieldValues);

    List<com.sell.sales.domain.Product> existingProducts = new ArrayList<>();
    oldLead.setProducts(null);
    if (null != this.getProducts()) {
      existingProducts.addAll(this.getProducts());
    }
    oldLead.setProducts(existingProducts);

    oldLead.setMetaInfo(null);
    MetaInfo metaInfo = this.getMetaInfo();
    if (!ObjectUtils.isEmpty(metaInfo)) {
      oldLead.setMetaInfo(
          new MetaInfo(metaInfo.isNew(), metaInfo.getLatestActivityCreatedAt(), metaInfo.getMeetingScheduledOn(), metaInfo.getTaskDueOn(),
              metaInfo.getLead(), metaInfo.getCreatedViaId(), metaInfo.getCreatedViaName(), metaInfo.getCreatedViaType(),
              metaInfo.getUpdatedViaId(), metaInfo.getUpdatedViaName(), metaInfo.getUpdatedViaType()));
    }

    oldLead.setLeadGPSAddress(null);
    LeadGPSAddress gpsAddress = this.getLeadGPSAddress();
    if (!ObjectUtils.isEmpty(gpsAddress)) {
      oldLead.setLeadGPSAddress(
          new LeadGPSAddress(gpsAddress.getId(),gpsAddress.getAddressCoordinate(),gpsAddress.getCompanyAddressCoordinate(),gpsAddress.getLead()));
    }

    oldLead.setLeadUtms(null);
    if (ObjectUtils.isNotEmpty(this.getLeadUtms())) {
      Set<LeadUtm> leadUtms = this.getLeadUtms()
          .stream()
          .map(next -> new LeadUtm(next.getId().longValue(), next.getSubSource(), next.getUtmSource(), next.getUtmMedium(), next.getUtmCampaign(),
              next.getUtmTerm(), next.getUtmContent(),
              next.getCreatedAt(), next.getUpdatedAt(), next.getCreatedBy(), next.getUpdatedBy()))
          .collect(Collectors.toCollection(LinkedHashSet::new));
      oldLead.setLeadUtms(leadUtms);
    }
    oldLead.setMetaData(null);
    if (ObjectUtils.isNotEmpty(this.getMetaData())) {
      oldLead.setMetaData(SerializationUtils.clone(new HashMap<>(this.getMetaData())));
    }
    oldLead.setPhoneNumbers(null);
    if (ObjectUtils.isNotEmpty(this.getPhoneNumbers())) {
      oldLead.setPhoneNumbers(SerializationUtils.clone(this.getPhoneNumbers()));
    }
    oldLead.setEmails(null);
    if (ObjectUtils.isNotEmpty(this.getEmails())) {
      oldLead.setEmails(SerializationUtils.clone(this.getEmails()));
    }
    oldLead.setCompanyPhones(null);
    if (ObjectUtils.isNotEmpty(this.getCompanyPhones())) {
      oldLead.setCompanyPhones(SerializationUtils.clone(this.getCompanyPhones()));
    }
    oldLead.setLeadPipeline(null);
    if (ObjectUtils.isNotEmpty(this.getLeadPipeline())) {
      oldLead.setLeadPipeline(this.getLeadPipeline().clone());
    }
    oldLead.setPhotoUrls(null);
    if (ObjectUtils.isNotEmpty(this.photoUrls)) {
      oldLead.setPhotoUrls(SerializationUtils.clone(this.photoUrls));
    }
    Set<LeadPhoneNumber> leadPhoneNumbers = this.getLeadPhoneNumbers().stream().map(leadPhoneNumber -> {
      LeadPhoneNumber leadPhoneNumber1 = new LeadPhoneNumber(leadPhoneNumber.getType(), leadPhoneNumber.getCode(), leadPhoneNumber.getValue(),
          leadPhoneNumber.getDialCode(), leadPhoneNumber.getLead(), leadPhoneNumber.isPrimary());
      leadPhoneNumber1.setId(leadPhoneNumber.getId());
      return leadPhoneNumber1;
    }).collect(Collectors.toSet());
    oldLead.setLeadPhoneNumbers(leadPhoneNumbers);

    Set<LeadCompanyPhoneNumber> leadCompanyPhoneNumbers = this.getLeadCompanyPhoneNumbers().stream().map(leadCompanyPhoneNumber -> {
      LeadCompanyPhoneNumber leadCompanyPhoneNumber1 = new LeadCompanyPhoneNumber(leadCompanyPhoneNumber.getType(), leadCompanyPhoneNumber.getCode(),
          leadCompanyPhoneNumber.getValue(), leadCompanyPhoneNumber.getDialCode(), leadCompanyPhoneNumber.getLead(),
          leadCompanyPhoneNumber.isPrimary());
      leadCompanyPhoneNumber1.setId(leadCompanyPhoneNumber.getId());
      return leadCompanyPhoneNumber1;
    }).collect(Collectors.toSet());
    oldLead.setLeadCompanyPhoneNumbers(leadCompanyPhoneNumbers);

    return oldLead;
  }

  public void withNewPipeline(LeadPipeline leadPipeline, Long pipelineStageId, String pipelineStageReason) {
    this.leadPipeline = leadPipeline;
    if (this.pipelineStage == null) {
      this.leadPipeline = leadPipeline.activateFirstStage();
      this.actualClosureDate = null;
      this.forecastingType = this.leadPipeline.getForecastingType();
      this.pipelineStage = this.leadPipeline.getCurrentStage().getPipelineStageId();
      this.pipeline = this.leadPipeline.getPipelineId();
      return;
    }
    this.leadPipeline = this.leadPipeline.setActiveStage(pipelineStageId, pipelineStageReason);
    LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
    this.pipelineStage = currentStage.getPipelineStageId();
    this.pipeline = this.leadPipeline.getPipelineId();
    this.forecastingType = currentStage.getForecastingType();
    this.actualClosureDate = null;
    if (CLOSED_WON.equals(this.forecastingType) && actualClosureDate == null) {
      this.actualClosureDate = new Date();
      this.pipelineStageReason = null;
    }
    if (CLOSED_LOST.equals(this.forecastingType) || CLOSED_UNQUALIFIED.equals(this.forecastingType)) {
      this.actualClosureDate = new Date();
      this.pipelineStageReason = pipelineStageReason;
    }

  }

  public void patch(LeadUpdateRequestV2 leadUpdateRequestV2, LeadPipeline leadPipeline, User loggedInUser) {

    BeanUtils.copyProperties(leadUpdateRequestV2, this, getNullValuedProperties(leadUpdateRequestV2));
    this.setCustomFieldValues(preProcessedCustomFields(leadUpdateRequestV2.getCustomFieldValues(), this.getCustomFieldValues()));

    if (!ObjectUtils.isEmpty(leadUpdateRequestV2.getOwnerId())) {
      this.setOwnerId(leadUpdateRequestV2.getOwnerId().getId());
    }

    mergeRequestedProducts(leadUpdateRequestV2, this);
    mergeEmail(leadUpdateRequestV2, this);
    mergePhone(leadUpdateRequestV2);
    mergeCompanyPhone(leadUpdateRequestV2);
    this.addLeadUtmValues(populateLeadUtmValuesForBulkPatch(this, leadUpdateRequestV2, loggedInUser));

    if (leadUpdateRequestV2.getPipeline() != null) {
      patchPipeline(leadPipeline, leadUpdateRequestV2.getPipeline().pipelineStageId(), leadUpdateRequestV2.getPipelineStageReason(),
          leadUpdateRequestV2.getActualClosureDate());
    }

  }

  private Set<LeadUtm> populateLeadUtmValuesForBulkPatch(Lead lead, LeadUpdateRequestV2 leadRequest, User loggedInUser) {
    Set<LeadUtm> leadUtms = new HashSet<>(emptySet());
    if (!ObjectUtils.isEmpty(lead.getLeadUtms())) {
      LeadUtm leadUtm = lead.getLeadUtms().stream().findFirst().get();
      leadUtms.add(new LeadUtm(leadRequest, leadUtm, loggedInUser.getUserId()));
      return leadUtms;
    } else {
      if (leadRequest.getSubSource() != null || leadRequest.getUtmSource() != null || leadRequest.getUtmContent() != null
          || leadRequest.getUtmCampaign() != null ||
          leadRequest.getUtmTerm() != null || leadRequest.getUtmMedium() != null) {
        leadUtms.clear();
        leadUtms.add(new LeadUtm(leadRequest.getSubSource(), leadRequest.getUtmSource(),
            leadRequest.getUtmMedium(), leadRequest.getUtmCampaign(),
            leadRequest.getUtmTerm(), leadRequest.getUtmContent(), loggedInUser.getUserId(), loggedInUser.getUserId()));
      }
    }

    return leadUtms;
  }

  private void mergePhone(LeadUpdateRequestV2 leadUpdateRequestV2) {
    if (ObjectUtils.isEmpty(leadUpdateRequestV2.getPhoneNumber())) {
      return;
    }
    if ((leadUpdateRequestV2.getPhoneNumber().getOperation().equals(FieldOperation.REPLACE)) || (ObjectUtils.isEmpty(this.getPhoneNumbers()))) {

      Set<PhoneNumber> phoneNumberRequestSet = new HashSet<>(Arrays.asList(leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers()));
      if (phoneNumberRequestSet.size() != leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers().length) {
        throw new SalesException(SalesErrorCodes.DUPLICATE_PHONES);
      }

      leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers()[0].setPrimary(true);
      this.phoneNumbers = leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers();
      if (ObjectUtils.isNotEmpty(this.getLeadPhoneNumbers())) {
        this.getLeadPhoneNumbers().clear();
      }
      Set<LeadPhoneNumber> leadPhoneNumbers = toLeadPhoneNumber(leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers());

      this.getLeadPhoneNumbers().addAll(leadPhoneNumbers);

      return;
    }

    this.phoneNumbers = Stream.of(
            this.phoneNumbers, leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers())
        .flatMap(phoneNumbers -> Arrays.stream(phoneNumbers))
        .toArray(PhoneNumber[]::new);

    Set<PhoneNumber> phoneNumberRequestSet = new HashSet<>(Arrays.asList(this.phoneNumbers));
    if (phoneNumberRequestSet.size() != this.phoneNumbers.length) {
      throw new SalesException(SalesErrorCodes.DUPLICATE_PHONES);
    }

    Set<LeadPhoneNumber> newLeadPhoneNumbers = toLeadPhoneNumber(leadUpdateRequestV2.getPhoneNumber().getPhoneNumbers());
    this.getLeadPhoneNumbers().addAll(newLeadPhoneNumbers);
  }

  private Set<LeadPhoneNumber> toLeadPhoneNumber(PhoneNumber[] phoneNumbers) {
    return Arrays.stream(phoneNumbers).map(leadPhoneNumber -> {
      return new LeadPhoneNumber(leadPhoneNumber.getType(), leadPhoneNumber.getCode(), leadPhoneNumber.getValue(), leadPhoneNumber.getDialCode(),
          this, leadPhoneNumber.isPrimary());
    }).collect(Collectors.toSet());
  }

  private void mergeCompanyPhone(LeadUpdateRequestV2 leadUpdateRequestV2) {
    if (ObjectUtils.isEmpty(leadUpdateRequestV2.getCompanyPhone())) {
      return;
    }
    if ((leadUpdateRequestV2.getCompanyPhone().getOperation().equals(FieldOperation.REPLACE)) || (ObjectUtils.isEmpty(this.getCompanyPhones()))) {

      Set<PhoneNumber> phoneNumberRequestSet = new HashSet<>(Arrays.asList(leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers()));
      if (phoneNumberRequestSet.size() != leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers().length) {
        throw new SalesException(SalesErrorCodes.DUPLICATE_COMPANY_PHONES);
      }

      leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers()[0].setPrimary(true);
      this.companyPhones = leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers();
      if (ObjectUtils.isNotEmpty(this.getLeadCompanyPhoneNumbers())) {
        this.getLeadCompanyPhoneNumbers().clear();
      }
      Set<LeadCompanyPhoneNumber> leadCompanyPhoneNumber = toLeadCompanyPhoneNumber(leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers());

      this.getLeadCompanyPhoneNumbers().addAll(leadCompanyPhoneNumber);

      return;
    }
    this.companyPhones = Stream.of(
            this.companyPhones, leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers())
        .flatMap(phoneNumbers -> Arrays.stream(phoneNumbers))
        .toArray(PhoneNumber[]::new);

    Set<PhoneNumber> phoneNumberRequestSet = new HashSet<>(Arrays.asList(this.companyPhones));
    if (phoneNumberRequestSet.size() != this.companyPhones.length) {
      throw new SalesException(SalesErrorCodes.DUPLICATE_COMPANY_PHONES);
    }

    Set<LeadCompanyPhoneNumber> newLeadCompanyPhoneNumbers = toLeadCompanyPhoneNumber(leadUpdateRequestV2.getCompanyPhone().getPhoneNumbers());
    this.getLeadCompanyPhoneNumbers().addAll(newLeadCompanyPhoneNumbers);
  }

  private Set<LeadCompanyPhoneNumber> toLeadCompanyPhoneNumber(PhoneNumber[] leadUpdateRequestV2) {
    return Arrays.stream(leadUpdateRequestV2)
        .map(phoneNumber -> new LeadCompanyPhoneNumber(phoneNumber.getType(), phoneNumber.getCode(), phoneNumber.getValue(),
            phoneNumber.getDialCode(), this, phoneNumber.isPrimary()))
        .collect(Collectors.toSet());
  }

  private void mergeEmail(LeadUpdateRequestV2 entity, Lead existingLead) {
    if (ObjectUtils.isEmpty(entity.getEmail())) {
      return;
    }
    if (entity.getEmail().getOperation().equals(FieldOperation.REPLACE)) {
      entity.getEmail().getEmails()[0].setPrimary(true);
      existingLead.setEmails(entity.getEmail().getEmails());
      return;
    }
    if (ObjectUtils.isEmpty(existingLead.getEmails())) {
      entity.getEmail().getEmails()[0].setPrimary(true);
      existingLead.setEmails(entity.getEmail().getEmails());
      return;
    }
    if (ObjectUtils.isNotEmpty(existingLead.getEmails())) {
      List<Email> collect = Stream.of(
              existingLead.getEmails(), entity.getEmail().getEmails())
          .flatMap(emails -> Arrays.stream(emails))
          .collect(Collectors.toList());
      Email[] emails = collect.toArray(new Email[collect.size()]);
      existingLead.setEmails(emails);
    }
  }

  private void mergeRequestedProducts(LeadUpdateRequestV2 entity, Lead existingLead) {
    if (ObjectUtils.isEmpty(entity.getProduct())) {
      return;
    }
    if (ObjectUtils.isEmpty(existingLead.getProducts()) && ObjectUtils.isNotEmpty(entity.getProduct())) {
      existingLead.setProducts(entity.getProduct().getProducts());
      return;
    }
    if (entity.getProduct().getOperation().equals(FieldOperation.REPLACE)) {
      existingLead.getProducts().clear();
      existingLead.setProducts(entity.getProduct().getProducts());
      return;
    }
    if (entity.getProduct() != null
        && entity.getProduct().getOperation().equals(FieldOperation.ADD)) {
      List<com.sell.sales.domain.Product> newProductsToBeAdded =
          entity.getProduct().getProducts().stream()
              .filter(
                  requestedProduct ->
                      existingLead.getProducts().stream()
                          .anyMatch(
                              existingProduct ->
                                  existingProduct.getId() != requestedProduct.getId()))
              .collect(Collectors.toList());
      existingLead.getProducts().addAll(newProductsToBeAdded);
    }
    return;
  }

  private Map<String, Object> preProcessedCustomFields(Map<String, Object> newCustomFields, Map<String, Object> existingCustomFields) {
    if (ObjectUtils.isEmpty(newCustomFields) && ObjectUtils.isEmpty(existingCustomFields)) {
      return emptyMap();
    }
    newCustomFields = ObjectUtils.isEmpty(newCustomFields) ? emptyMap() : newCustomFields;
    Map oldCustomFields = ObjectUtils.isEmpty(existingCustomFields) ? new HashMap() : existingCustomFields;
    ObjectMapper objectMapper = new ObjectMapper();
    newCustomFields
        .entrySet()
        .stream()
        .forEach(stringObjectEntry -> {
          Object value = stringObjectEntry.getValue();
          String key = stringObjectEntry.getKey();
          if (value instanceof Map) {
            try {
              MultiPicklistField multiPicklistField = objectMapper.convertValue(value, MultiPicklistField.class);
              if (multiPicklistField.getOperation().equals(FieldOperation.ADD) && !ObjectUtils.isEmpty(oldCustomFields.get(key))) {
                ArrayList<Number> existingMultiPicklistValue = (ArrayList) oldCustomFields.get(key);
                existingMultiPicklistValue.addAll(multiPicklistField.getValues());
                oldCustomFields.put(key, existingMultiPicklistValue.stream().distinct().collect(Collectors.toList()));
              } else {
                oldCustomFields.put(key, multiPicklistField.getValues());
              }
            } catch (IllegalArgumentException e) {
              log.error("Invalid MultiPicklistField for patch update, error: {}", e.getMessage());
            }
          } else if (value instanceof String && ObjectUtils.isEmpty(value.toString().trim())) {
            oldCustomFields.remove(key);
          } else {
            oldCustomFields.put(key, value);
          }
        });
    return oldCustomFields;
  }

  private String[] getNullValuedProperties(LeadUpdateRequestV2 lead) {
    final BeanWrapper src = new BeanWrapperImpl(lead);
    return Arrays.stream(src.getPropertyDescriptors())
        .filter(propertyDescriptor -> !"ownerChange".equalsIgnoreCase(propertyDescriptor.getName()))
        .filter(
            descriptor ->
                src.getPropertyValue(descriptor.getName()) == null
                    || descriptor.getName().equalsIgnoreCase("products")
                    || descriptor.getName().equalsIgnoreCase("customFieldValues")
        )
        .map(FeatureDescriptor::getName)
        .toArray(String[]::new);
  }

  private void patchPipeline(LeadPipeline leadPipeline, Long pipelineStageId, String pipelineStageReason, Date actualClosureDate) {
    if (leadPipeline == null) {
      return;
    }
    this.actualClosureDate = actualClosureDate;
    this.pipelineStageReason = null;

    if (ObjectUtils.isEmpty(this.leadPipeline) || ObjectUtils.notEqual(this.leadPipeline.getPipelineId(), leadPipeline.getPipelineId())) {
      this.leadPipeline = leadPipeline.setActiveStage(pipelineStageId, pipelineStageReason);
      this.pipeline = this.leadPipeline.getPipelineId();
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      if (CLOSED_WON.equals(this.forecastingType) && actualClosureDate == null) {
        this.actualClosureDate = new Date();
        this.pipelineStageReason = null;
      }
      if (CLOSED_LOST.equals(this.forecastingType) || CLOSED_UNQUALIFIED.equals(this.forecastingType)) {
        this.actualClosureDate = new Date();
        this.pipelineStageReason = pipelineStageReason;
      }
      return;
    }
    if (ObjectUtils.isNotEmpty(this.getLeadPipeline()) && this.leadPipeline.getPipelineId() == leadPipeline.getPipelineId()) {
      updateExistingPipeline(pipelineStageId, pipelineStageReason, actualClosureDate);
    }

  }

  private void updateExistingPipeline(long pipelineStageId, String pipelineStageReason, Date actualClosureDate) {
    LeadPipelineStage currentStage = this.getLeadPipeline().getCurrentStage();
    this.actualClosureDate = actualClosureDate;

    if (this.leadPipeline.getCurrentStage().getPipelineStageId() != pipelineStageId) {
      this.leadPipeline = this.leadPipeline.setActiveStage(pipelineStageId, pipelineStageReason);
      this.pipeline = leadPipeline.getPipelineId();
      LeadPipelineStage currentStage1 = leadPipeline.getCurrentStage();
      this.pipelineStage = currentStage1.getPipelineStageId();
      this.forecastingType = currentStage1.getForecastingType();
      if (CLOSED_WON.equals(this.forecastingType) && actualClosureDate == null) {
        this.actualClosureDate = new Date();

      }
      if (CLOSED_LOST.equals(this.forecastingType) || CLOSED_UNQUALIFIED.equals(this.forecastingType)) {
        this.actualClosureDate = new Date();
        this.pipelineStageReason = pipelineStageReason;
      }
    }
  }

  public void setPipeline(LeadPipeline leadPipeline, Long pipelineStage, String pipelineStageReason) {
    if (ObjectUtils.isNotEmpty(pipelineStage)) {
      this.leadPipeline = leadPipeline.setActiveStage(pipelineStage, pipelineStageReason);
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.forecastingType = currentStage.getForecastingType();
      this.actualClosureDate = getActualClosureDate(currentStage.getForecastingType());
      return;
    }
    this.leadPipeline = leadPipeline.activateFirstStage();
    LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
    this.forecastingType = currentStage.getForecastingType();
    this.actualClosureDate = getActualClosureDate(currentStage.getForecastingType());
  }

  private Date getActualClosureDate(ForecastingType forecastingType) {
    if (OPEN.equals(forecastingType)) {
      return null;
    }
    return new Date();
  }

  public void patchWorkflow(Lead lead, User loggedInUser, PipelineResponse pipelineResponse) {
    this.addLeadUtmValues(populateLeadUtmValuesForWorkflowPatch(this, lead, loggedInUser));
    String[] nullValuedProperties = getNullValuedProperties(lead);

    Set<LeadPhoneNumber> existingLeadPhoneNumbers = this.getLeadPhoneNumbers();
    Set<LeadCompanyPhoneNumber> existingLeadCompanyPhoneNumbers = this.getLeadCompanyPhoneNumbers();
    BeanUtils.copyProperties(lead, this, nullValuedProperties);
    this.setLeadPhoneNumbers(existingLeadPhoneNumbers);
    this.setLeadCompanyPhoneNumbers(existingLeadCompanyPhoneNumbers);
    this.setProducts(lead.getProducts());
    patchPipelineByWorkflow(lead, pipelineResponse, loggedInUser);
    this.setCustomFieldValues(lead.getCustomFieldValues());
  }

  public Double calculateAndUpdateScore(ScoreValueDetail scoreValueDetail) {
    RuleFieldValue ruleField = scoreValueDetail.getRuleFieldValue();
    RuleType ruleType = ruleField.getRuleType();
    Double scoreValue = Double.valueOf(ruleField.getScoreValue());
    Double oldScore = this.score != null ? this.score : 0.0;

    if(RuleType.POSITIVE.equals(ruleType)){
      updatePositiveScore(oldScore, scoreValue);
    }
    if(RuleType.NEGATIVE.equals(ruleType)){
      updateNegativeScore(oldScore, scoreValue);
    }
    return this.score - oldScore;
  }

  private void updatePositiveScore(Double existingScore, Double scoreToIncrease) {
    if (existingScore >= 0 && existingScore <= 100) {
      this.score = Math.min(100, existingScore + scoreToIncrease);
    }
    if (existingScore < 0) {
      this.score = existingScore + scoreToIncrease;
    }
  }

  private void updateNegativeScore(Double existingScore, Double scoreToDecrease) {
    if (existingScore >= 0 && existingScore <= 100) {
      this.score = Math.max(0, existingScore - scoreToDecrease);
    }
    if (existingScore > 100) {
      this.score = existingScore - scoreToDecrease;
    }
  }

  private void patchPipelineByWorkflow(Lead leadToPatch, PipelineResponse pipelineResponse, User loggedInUser) {

    if ((ObjectUtils.isEmpty(pipelineResponse) && ObjectUtils.isEmpty(this.getPipeline())) && leadToPatch.actualClosureDate != null) {
      log.error("Existing Lead and Update Request both with no pipeline and trying to update actual Closure Date with tenantId : {} and userId : {}",
          loggedInUser.getTenantId(), loggedInUser.getUserId());
      throw new SalesException(SalesErrorCodes.ACTUAL_CLOSURE_DATE_NOT_ALLOWED);
    }

    if (ObjectUtils.isEmpty(pipelineResponse)) {
      if (ObjectUtils.isNotEmpty(this.getForecastingType())) {
        if (this.getForecastingType().equals(OPEN) && leadToPatch.getActualClosureDate() != null) {
          log.error("Existing Lead with ForeCastingType Open and trying to update actual Closure Date with tenantId : {} and userId : {}",
              loggedInUser.getTenantId(), loggedInUser.getUserId());
          throw new SalesException(SalesErrorCodes.ACTUAL_CLOSURE_DATE_NOT_ALLOWED);
        }
      }
    }

    if (leadToPatch.getPipeline() != null) {
      PipelineStageResponse pipelineStageResponse1 = pipelineResponse.getStages().stream()
          .filter(pipelineStageResponse -> pipelineStageResponse.getId().equals(leadToPatch.getPipelineStage())).findFirst().get();
      if (pipelineStageResponse1.getForecastingType().equals(OPEN) && leadToPatch.getActualClosureDate() != null) {
        log.error("Update Request Lead with ForeCastingType Open and trying to update actual Closure Date with tenantId : {} and userId : {}",
            loggedInUser.getTenantId(), loggedInUser.getUserId());
        throw new SalesException(SalesErrorCodes.ACTUAL_CLOSURE_DATE_NOT_ALLOWED);
      }
    }

    if (leadToPatch.getPipeline() == null) {
      return;
    }

    if (this.pipeline != null && this.pipeline.equals(leadToPatch.getPipeline())) {
      this.leadPipeline = this.leadPipeline.setActiveStage(leadToPatch.getPipelineStage(), leadToPatch.getPipelineStageReason());
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();

      this.pipeline = pipelineResponse.getId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, leadToPatch.getActualClosureDate());
      return;
    }
    if (leadToPatch.getPipelineStage() != null) {
      this.leadPipeline = LeadPipeline.create(loggedInUser.getTenantId(), this, pipelineResponse)
          .setActiveStage(leadToPatch.getPipelineStage(), leadToPatch.getPipelineStageReason());

      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();

      this.pipeline = pipelineResponse.getId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, leadToPatch.getActualClosureDate());
    }
    if (leadToPatch.getPipeline() != null && leadToPatch.getPipelineStage() == null) {
      this.leadPipeline = LeadPipeline.create(loggedInUser.getTenantId(), this, pipelineResponse)
          .activateFirstStage();
      LeadPipelineStage currentStage = this.leadPipeline.getCurrentStage();
      this.pipeline = pipelineResponse.getId();
      this.pipelineStage = currentStage.getPipelineStageId();
      this.forecastingType = currentStage.getForecastingType();
      this.pipelineStageReason = getReason(this.forecastingType, currentStage.getReasonForClosing());
      this.actualClosureDate = getActualClosureDate(forecastingType, leadToPatch.getActualClosureDate());
    }
  }

  private String[] getNullValuedProperties(Lead lead) {
    final BeanWrapper src = new BeanWrapperImpl(lead);
    return Arrays.stream(src.getPropertyDescriptors())
        .filter(propertyDescriptor -> !"ownerChange".equalsIgnoreCase(propertyDescriptor.getName()))
        .filter(
            descriptor ->
                src.getPropertyValue(descriptor.getName()) == null
                    || descriptor.getName().equalsIgnoreCase("products") ||
                    descriptor.getName().equalsIgnoreCase("multiConversionAssociations") ||
                    descriptor.getName().equalsIgnoreCase("metaInfo") ||
                    descriptor.getName().equalsIgnoreCase("customFieldValues") ||
                    descriptor.getName().equalsIgnoreCase("leadUtms") ||
                    descriptor.getName().equalsIgnoreCase("pipeline") ||
                    descriptor.getName().equalsIgnoreCase("pipelineStage")
        )
        .map(FeatureDescriptor::getName)
        .toArray(String[]::new);
  }

  private Set<LeadUtm> populateLeadUtmValuesForWorkflowPatch(Lead existingLead, Lead leadToUpdate, User loggedInUser) {
    Set<LeadUtm> leadUtms = new HashSet<>(emptySet());
    Iterator<LeadUtm> iterator = leadToUpdate.getLeadUtms().iterator();
    LeadUtm requestedLeadUtm = iterator.hasNext() ? iterator.next() : new LeadUtm();
    if (!ObjectUtils.isEmpty(existingLead.getLeadUtms())) {
      LeadUtm leadUtm = existingLead.getLeadUtms().stream().findFirst().get();
      leadUtms.add(new LeadUtm(requestedLeadUtm, leadUtm, loggedInUser.getUserId()));
      return leadUtms;
    } else {
      if (requestedLeadUtm.getSubSource() != null || requestedLeadUtm.getUtmSource() != null || requestedLeadUtm.getUtmContent() != null
          || requestedLeadUtm.getUtmCampaign() != null ||
          requestedLeadUtm.getUtmTerm() != null || requestedLeadUtm.getUtmMedium() != null) {
        leadUtms.clear();
        leadUtms.add(new LeadUtm(requestedLeadUtm.getSubSource(), requestedLeadUtm.getUtmSource(),
            requestedLeadUtm.getUtmMedium(), requestedLeadUtm.getUtmCampaign(),
            requestedLeadUtm.getUtmTerm(), requestedLeadUtm.getUtmContent(), loggedInUser.getUserId(), loggedInUser.getUserId()));
      }
    }
    return leadUtms;
  }

  public void populateHistoricalFields(Long userId) {
    populateCreatedByAndUpdatedByForCreate(userId);

    if (ObjectUtils.isEmpty(this.getCreatedAt()) && ObjectUtils.isEmpty(this.getUpdatedAt())) {
      this.setCreatedAt(new Date());
      this.setUpdatedAt(new Date());
    }
    if (ObjectUtils.isEmpty(this.getCreatedAt())) {
      this.setCreatedAt(this.getUpdatedAt());
    }
    if (ObjectUtils.isEmpty(this.getUpdatedAt())) {
      this.setUpdatedAt(new Date());
    }
    this.validate();
  }

  private void validate() {

    if ((new Date()).before(this.getUpdatedAt()) ||
        (new Date()).before(this.getCreatedAt())) {
      this.processException("CreatedAt or UpdatedAt", SalesErrorCodes.FUTURE_DATE_NOT_ALLOWED);
    }
    if (this.getCreatedAt().compareTo(this.getUpdatedAt()) > 0) {
      this.processException("UpdatedAt", SalesErrorCodes.UPDATED_AT_SHOULD_BE_AFTER_CREATED_AT);
    }
  }

  private void populateCreatedByAndUpdatedByForCreate(Long userId) {
    if (ObjectUtils.isEmpty(this.getCreatedBy())) {
      this.setCreatedBy(userId);
    }
    if (ObjectUtils.isEmpty(this.getUpdatedBy())) {
      this.setUpdatedBy(userId);
    }
  }

  public void populateUpdatedByAndUpdatedAtForUpdate(Long userId) {
    this.setUpdatedBy(userId);
    this.setUpdatedAt(new Date());
  }

  @JsonIgnore
  public boolean isOwnerChange() {
    return oldOwnerId != this.getOwnerId();
  }

  public void processException(String field, ErrorResource errorResource) {
    if (this.isRequestFromImport()) {
      this.getErrors().add(new ImportErrorResource(field, errorResource));
      return;
    }
    throw new SalesException(errorResource);
  }

  //do not remove this method being used in mapper
  public Pipeline populatePipeline() {
    Pipeline pipeline = null;
    if (ObjectUtils.isNotEmpty(this.getPipeline())) {
      pipeline = new Pipeline(this.getPipeline(), null, null);
    }
    if (ObjectUtils.isNotEmpty(this.getPipeline()) && ObjectUtils.isNotEmpty(this.getPipelineStage())) {
      pipeline = new Pipeline(this.getPipeline(), null, new PipelineStage(this.getPipelineStage(), null));
    }
    return pipeline;
  }

  //do not remove this method being used in mapper
  public PhoneNumberRequest[] populateLeadPhones() {
    if (ObjectUtils.isEmpty(leadPhoneNumbers)) {
      return null;
    }
    return leadPhoneNumbers.stream().map(
        leadPhoneNumber -> new PhoneNumberRequest(leadPhoneNumber.getId(), leadPhoneNumber.getType(), leadPhoneNumber.getCode(),
            leadPhoneNumber.getValue(), leadPhoneNumber.getDialCode(), leadPhoneNumber.isPrimary())).toArray(PhoneNumberRequest[]::new);
  }

  //do not remove this method being used in mapper
  public PhoneNumberRequest[] populateLeadCompanyPhones() {
    if (ObjectUtils.isEmpty(leadCompanyPhoneNumbers)) {
      return null;
    }
    return leadCompanyPhoneNumbers.stream().map(
        leadPhoneNumber -> new PhoneNumberRequest(leadPhoneNumber.getId(), leadPhoneNumber.getType(), leadPhoneNumber.getCode(),
            leadPhoneNumber.getValue(), leadPhoneNumber.getDialCode(), leadPhoneNumber.isPrimary())).toArray(PhoneNumberRequest[]::new);
  }

  public void addLeadPhoneNumbers(Set<LeadPhoneNumber> leadPhoneNumbers) {
    if (!ObjectUtils.isEmpty(this.leadPhoneNumbers)) {
      this.leadPhoneNumbers.clear();
    }
    this.setLeadPhoneNumbers(leadPhoneNumbers);
  }

  public void addLeadCompanyPhoneNumbers(Set<LeadCompanyPhoneNumber> leadCompanyPhoneNumbers) {
    if (!ObjectUtils.isEmpty(this.leadCompanyPhoneNumbers)) {
      this.leadCompanyPhoneNumbers.clear();
    }
    this.setLeadCompanyPhoneNumbers(leadCompanyPhoneNumbers);
  }

  public List<Product> getUpdatedProducts(List<Product> replaceLeadProducts, List<Product> appendLeadProducts) {

    if (ObjectUtils.isEmpty(replaceLeadProducts) && ObjectUtils.isEmpty(appendLeadProducts)) {
      return this.products;
    }

    if (ObjectUtils.isNotEmpty(replaceLeadProducts)) {
      return replaceLeadProducts;
    }

    if (ObjectUtils.isNotEmpty(appendLeadProducts) && ObjectUtils.isNotEmpty(this.products)) {
        Set<Long> existingProductIds = this.products.stream()
            .map(Product::getId)
            .collect(Collectors.toSet());

        List<Product> updatedProducts = new ArrayList<>(this.products);
        appendLeadProducts.stream()
            .filter(product -> !existingProductIds.contains(product.getId()))
            .forEach(updatedProducts::add);

        return updatedProducts;
      } else {
        return appendLeadProducts;
      }

  }


  public Map<String, Object> getUpdatedCustomFields(Map<String, Object> replaceCustomFieldValues, Map<String, Object> appendCustomFieldValues) {
    Map<String, Object> updatedCustomFieldValueMap = new HashMap<>();


    if (ObjectUtils.isNotEmpty(this.getCustomFieldValues())) {
      updatedCustomFieldValueMap.putAll(getTrimmedCustomFieldValues(this.getCustomFieldValues()));
    }


    if (ObjectUtils.isEmpty(replaceCustomFieldValues) && ObjectUtils.isEmpty(appendCustomFieldValues)) {
      return getTrimmedCustomFieldValues(updatedCustomFieldValueMap);
    }

    if (ObjectUtils.isNotEmpty(replaceCustomFieldValues)) {
      updatedCustomFieldValueMap.putAll(replaceCustomFieldValues);
    }


    if (ObjectUtils.isNotEmpty(appendCustomFieldValues)) {
      for (Map.Entry<String, Object> entry : appendCustomFieldValues.entrySet()) {
        String key = entry.getKey();
        Object value = entry.getValue();

        if (value instanceof List && ObjectUtils.isNotEmpty(this.getCustomFieldValues().get(key))) {
          List<Number> existingValues = (List<Number>) this.getCustomFieldValues().get(key);
          List<Number> newValues = (List<Number>) value;

          List<Number> combinedValues = new ArrayList<>(existingValues);
          combinedValues.addAll(newValues);
          updatedCustomFieldValueMap.put(key, combinedValues.stream().distinct().collect(Collectors.toList()));
        } else {
          updatedCustomFieldValueMap.put(key, value);
        }
      }
    }

    return getTrimmedCustomFieldValues(updatedCustomFieldValueMap);
  }

}
