package com.sell.sales.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.entity.model.PhoneType;
import java.util.Arrays;
import java.util.Objects;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Entity
@NoArgsConstructor
@Getter
@Setter
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(exclude = {"lead"})
public class LeadPhoneNumber {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
  @Enumerated(EnumType.STRING)
  private PhoneType type;
  private String code;
  private String value;
  private String dialCode;
  private boolean isPrimary;
  @ManyToOne
  @JoinColumn(name = "lead_id")
  @JsonIgnore
  private Lead lead;

  public LeadPhoneNumber(PhoneType type, String code, String value, String dialCode, Lead lead, boolean isPrimary) {
    this.type = type;
    this.code = code;
    this.value = value;
    this.dialCode = dialCode;
    this.lead = lead;
    this.isPrimary = isPrimary;
  }


  public static boolean areLeadPhoneNumbersEqual(LeadPhoneNumber[] existingPhoneNumbers, LeadPhoneNumber[] updatedPhoneNumbers) {
    if (ObjectUtils.isEmpty(existingPhoneNumbers) && ObjectUtils.isEmpty(updatedPhoneNumbers)) {
      return true;
    }
    if (ObjectUtils.isEmpty(existingPhoneNumbers) || ObjectUtils.isEmpty(updatedPhoneNumbers)) {
      return false;
    }
    if (existingPhoneNumbers.length != updatedPhoneNumbers.length) {
      return false;
    }

    return Arrays.stream(existingPhoneNumbers)
        .allMatch(existing -> Arrays.stream(updatedPhoneNumbers)
            .anyMatch(updated -> areEqualIgnoringIdAndLead(existing, updated)))
        && Arrays.stream(updatedPhoneNumbers)
        .allMatch(updated -> Arrays.stream(existingPhoneNumbers)
            .anyMatch(existing -> areEqualIgnoringIdAndLead(updated, existing)));
  }

  private static boolean areEqualIgnoringIdAndLead(LeadPhoneNumber first, LeadPhoneNumber second) {
    if (first == null || second == null) {
      return false;
    }
    return first.getType() == second.getType()
        && Objects.equals(first.getCode(), second.getCode())
        && Objects.equals(first.getValue(), second.getValue())
        && first.isPrimary() == second.isPrimary();
  }
}
