package com.sell.sales.search.querybuilder.parser.es;

import com.sell.sales.search.querybuilder.model.IRule;
import com.sell.sales.search.querybuilder.model.enums.EnumOperator;
import com.sell.sales.search.querybuilder.parser.AbstractEsRuleParser;
import com.sell.sales.search.querybuilder.parser.JsonRuleParser;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * Created by aravindp on 07/5/19
 */
public class IsEmptyRuleParser extends AbstractEsRuleParser {

  public boolean canParse(IRule rule) {
    return EnumOperator.IS_EMPTY.getValue().equals(rule.getOperator());
  }

  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {
    BoolQueryBuilder first = QueryBuilders.boolQuery().should(QueryBuilders.termQuery(rule.getField(), ""));
    BoolQueryBuilder second =  QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery(rule.getField()));
    return new BoolQueryBuilder().should(first).should(second);
  }
}