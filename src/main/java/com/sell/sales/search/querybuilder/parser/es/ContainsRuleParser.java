package com.sell.sales.search.querybuilder.parser.es;

import com.sell.sales.search.querybuilder.model.IRule;
import com.sell.sales.search.querybuilder.model.enums.EnumOperator;
import com.sell.sales.search.querybuilder.parser.AbstractEsRuleParser;
import com.sell.sales.search.querybuilder.parser.JsonRuleParser;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * Created by hemants on 30/03/19.
 */
public class ContainsRuleParser extends AbstractEsRuleParser {

  @Override
  public boolean canParse(IRule rule) {
    return EnumOperator.CONTAINS.getValue().equals(rule.getOperator());
  }

  @Override
  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {
    return QueryBuilders.wildcardQuery(rule.getField(), "*" + rule.getValue().toString() + "*");
  }
}
