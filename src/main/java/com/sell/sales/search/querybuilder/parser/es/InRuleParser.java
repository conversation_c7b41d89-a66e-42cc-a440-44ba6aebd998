package com.sell.sales.search.querybuilder.parser.es;

import com.sell.sales.search.querybuilder.model.IRule;
import com.sell.sales.search.querybuilder.model.enums.EnumOperator;
import com.sell.sales.search.querybuilder.parser.AbstractEsRuleParser;
import com.sell.sales.search.querybuilder.parser.JsonRuleParser;
import com.sell.sales.search.querybuilder.parser.es.nested.NestedFieldRuleParser;
import java.util.List;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * Created by hemants on 25/03/19.
 */
public class InRuleParser extends AbstractEsRuleParser {

  @Override
  public boolean canParse(IRule rule) {
    return EnumOperator.IN.getValue().equals(rule.getOperator());
  }

  @Override
  public AbstractQueryBuilder parse(IRule rule, JsonRuleParser parser) {
    if (NestedFieldRuleParser.isNestedField(rule.getField())) {
      return NestedFieldRuleParser.parseIn(rule);
    }
    List<Object> values = (List<Object>) rule.getValue();
    return QueryBuilders.termsQuery(rule.getField(), values);
  }
}
