package com.sell.sales.search.querybuilder.parser.es;

import com.sell.sales.search.querybuilder.model.IGroup;
import com.sell.sales.search.querybuilder.model.enums.EnumCondition;
import com.sell.sales.search.querybuilder.parser.IGroupParser;
import com.sell.sales.search.querybuilder.parser.JsonRuleParser;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * Created by hemants on 13/03/19.
 */
public class DefaultGroupParser implements IGroupParser {

  @Override
  public Object parse(IGroup group, JsonRuleParser parser) {
    BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

    // rules
    for (int i = 0; i < group.getRules().size(); i++) {
      // json parse
      AbstractQueryBuilder queryBuilder = (AbstractQueryBuilder) parser
          .parse(group.getRules().get(i));
      if (EnumCondition.AND.getValue().equals(group.getCondition())) {
        boolQueryBuilder.must(queryBuilder);
      } else if (EnumCondition.OR.getValue().equals(group.getCondition())) {
        boolQueryBuilder.should(queryBuilder);
      }
    }

    return boolQueryBuilder;
  }

}
