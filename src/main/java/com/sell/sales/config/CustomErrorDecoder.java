package com.sell.sales.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.core.exception.APIException;
import com.sell.sales.core.exception.BaseException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomErrorDecoder extends ErrorDecoder.Default {

  @Override
  public Exception decode(final String methodKey, final Response response) {
    try {
      if (response.body() != null) {
        String body = Util.toString(response.body().asReader());
        log.error("Error while calling API {}, status {}, reason {}, URL {}", body, response.status(), response.reason(), response.request().url());
        return new BaseException((new ObjectMapper()).readValue(body, ErrorResource.class));
      }
    } catch (IOException e) {
      log.error("Decoding inter-service communication exception", e);
      return new BaseException(ErrorCodes.COMMON_INTERNAL_ERROR);
    }

    Exception e = super.decode(methodKey, response);
    return new APIException(e.getMessage(), e);
  }
}
