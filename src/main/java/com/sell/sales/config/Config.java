package com.sell.sales.config;

import com.sell.sales.core.config.CoreConfig;
import com.sell.sales.core.utils.JwtTokenConverter;
import com.sell.sales.entity.config.CrudEntityConfig;
import com.sell.sales.entity.config.CustomFieldEntityConfig;
import com.sell.sales.entity.validator.AnnotationBasedValidator;
import com.sell.sales.entity.validator.BaseValidator;
import com.sell.sales.search.config.ChangeOwnerBulkConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by hemants on 27/03/19.
 */
@Configuration
public class Config extends CoreConfig {

  @Value("${jwt.secret-key}")
  private String jwtSecretKey;

  @Configuration
  public class SalesEntityConfig extends CrudEntityConfig {

  }

  @Configuration
  public class SalesCustomFieldEntityConfig extends CustomFieldEntityConfig {

  }

  @Bean
  public BaseValidator getSimpleEntityValidator() {
    return new AnnotationBasedValidator();
  }
  @Configuration
  public class SalesChangeOwnerBulkConfig extends ChangeOwnerBulkConfig {
    //
  }
  @Bean
  public JwtTokenConverter jwtTokenConverter(){
    return new JwtTokenConverter("sell",jwtSecretKey);
  }
}
