package com.sell.sales.config;

import com.google.code.ssm.Cache;
import com.google.code.ssm.CacheFactory;
import com.google.code.ssm.config.AbstractSSMConfiguration;
import com.google.code.ssm.config.DefaultAddressProvider;
import com.google.code.ssm.providers.xmemcached.MemcacheClientFactoryImpl;
import com.google.code.ssm.providers.xmemcached.XMemcachedConfiguration;
import com.google.code.ssm.spring.ExtendedSSMCacheManager;
import com.google.code.ssm.spring.SSMCache;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@EnableCaching
@Profile({"!test"})
@Slf4j
public class MemcachedConfiguration extends AbstractSSMConfiguration {
  @Value("${memcached.host}")
  private String memcachedHost;

  @Value("${memcached.memPort}")
  private String memcachedPortNew;

  @Override
  public CacheFactory defaultMemcachedClient() {
    log.error("Memcached host {} and port {}",memcachedHost, memcachedPortNew);
    String host = String.format("%s:%s", memcachedHost, memcachedPortNew);

    final XMemcachedConfiguration conf = new XMemcachedConfiguration();

    conf.setUseBinaryProtocol(true);

    final CacheFactory cf = new CacheFactory();

    cf.setCacheClientFactory(new MemcacheClientFactoryImpl());
    cf.setAddressProvider(new DefaultAddressProvider(host));
    cf.setConfiguration(conf);

    return cf;
  }

  @Bean
  public CacheManager cacheManager() throws Exception {
    // Use SSMCacheManager instead of ExtendedSSMCacheManager if you do not
    // need to set per key expiration
    ExtendedSSMCacheManager cacheManager = new ExtendedSSMCacheManager();
    Cache cache = this.defaultMemcachedClient().getObject();

    // SSMCache(cache, 0, false) creates a cache with default key expiration
    // of 0 (no expiration) and flushing disabled (allowClear = false)

    SSMCache ssmCache = new SSMCache(cache, 0, false);
    cacheManager.setCaches(Arrays.asList(ssmCache));

    return cacheManager;
  }


}
