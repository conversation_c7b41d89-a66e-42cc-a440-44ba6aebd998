package com.sell.sales.config;

import com.sell.sales.core.config.CoreSwaggerConfig;
import org.springframework.beans.factory.annotation.Value;

/**
 * Created by hemants on 21/02/19.
 * @deprecated
 */
//@Configuration
//@EnableSwagger2
/*@Deprecated
public class SwaggerConfig extends CoreSwaggerConfig {

    @Value("${spring.application.name}")
    private String serviceName;

    @Override
    protected String getServiceName(){
        return serviceName;
    }

    @Override
    protected String getVersion(){
        return "0.0.1-SNAPSHOT";
    }

}*/
