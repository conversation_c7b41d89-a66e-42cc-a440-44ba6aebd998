package com.sell.sales.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.controller.request.pipeline.DefaultPipelineStage;
import com.sell.sales.core.client.InternalShareRuleService;
import com.sell.sales.domain.ForecastingType;
import com.sell.sales.dto.mapper.DefaultDealPipelineConfig;
import com.sell.sales.dto.mapper.DefaultLeadPipelineConfig;
import com.sell.sales.entity.util.ReadFileUtil;
import com.sell.sales.repository.ContactRepository;
import com.sell.sales.repository.LeadRepository;
import com.sell.sales.search.config.SearchConfig;
import feign.Feign;
import feign.Feign.Builder;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ApplicationConfig {

  public static final TypeReference<List<DefaultPipelineStage>> pipelineStageRequestTypeRef = new TypeReference<List<DefaultPipelineStage>>() {
  };

  @Value("${mandatory.stage.forecasting.types}")
  private List<ForecastingType> mandatoryStageForecastingTypes;

  @Value("${default.unqualified.reasons}")
  private String[] unqualifiedReasons;

  @Value("${default.lost.reasons}")
  private String[] lostReasons;

  @Value("${client.entity.basePath}")
  private String clientBasePath;

  @Value("${newElasticsearch.host:localhost}")
  public String newElasticsearchHost;

  @Value("${newElasticsearch.port:9400}")
  public int newElasticsearchPort;

  private final Feign.Builder clientBuilder;

  private final LeadRepository leadRepository;

  private final ContactRepository contactRepository;

  public ApplicationConfig(Builder clientBuilder, LeadRepository leadRepository, ContactRepository contactRepository) {
    this.clientBuilder = clientBuilder;
    this.leadRepository = leadRepository;
    this.contactRepository = contactRepository;
    this.clientBuilder
        .errorDecoder(new CustomErrorDecoder());
  }

  @Bean
  public DefaultLeadPipelineConfig defaultLeadPipelineConfig() throws IOException {
    return new DefaultLeadPipelineConfig(unqualifiedReasons, lostReasons, getDefaultLeadStages());
  }

  @Bean
  public DefaultDealPipelineConfig defaultDealPipelineConfig() throws IOException {
    return new DefaultDealPipelineConfig(unqualifiedReasons, lostReasons, getDefaultDealStages());
  }

  private List<DefaultPipelineStage> getDefaultLeadStages() throws IOException {
    return readStageFile("/pipeline/create-lead-pipeline-stage.json");
  }

  private List<DefaultPipelineStage> getDefaultDealStages() throws IOException {
    return readStageFile("/pipeline/create-deal-pipeline-stage.json");
  }

  private List<DefaultPipelineStage> readStageFile(String fileName) throws IOException {
    return new ObjectMapper().readValue(ReadFileUtil.readFromFile(fileName), pipelineStageRequestTypeRef);
  }

  @Bean
  public InternalShareRuleService getInternalShareRuleService() {
    return new InternalShareRuleService(clientBasePath, clientBuilder, leadRepository, contactRepository);
  }

  @Configuration
  class SalesSearchConfig extends SearchConfig {

  }

  @Bean
  public RestHighLevelClient restHighLevelClient() {
    return
        new RestHighLevelClient(
            RestClient.builder(new HttpHost(newElasticsearchHost, newElasticsearchPort, "http")));
  }

}
