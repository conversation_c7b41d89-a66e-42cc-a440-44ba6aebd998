package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class ContactEventPayload {
  private final ContactEventV2 entity;
  private final ContactEventV2 oldEntity;
  private final Metadata metadata;

  @JsonCreator
  public ContactEventPayload(
      @JsonProperty("entity") ContactEventV2 entity,
      @JsonProperty("oldEntity") ContactEventV2 oldEntity,
      @JsonProperty("metadata") Metadata metadata) {
    this.entity = entity;
    this.oldEntity = oldEntity;
    this.metadata = metadata;
  }

}
