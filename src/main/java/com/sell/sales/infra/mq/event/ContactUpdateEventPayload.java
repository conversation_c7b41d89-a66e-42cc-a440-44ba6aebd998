package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.domain.Contact;
import java.util.Map;
import lombok.Getter;

@Getter
public class ContactUpdateEventPayload {
  private final Map<EditPropertyActionType,Contact> entity;
  private final Map<EditPropertyActionType,Contact> oldEntity;
  private final Metadata metadata;

  @JsonCreator
  public ContactUpdateEventPayload(
      @JsonProperty("entity") Map<EditPropertyActionType,Contact> entity,
      @JsonProperty("oldEntity") Map<EditPropertyActionType,Contact> oldEntity,
      @JsonProperty("metadata") Metadata metadata) {
    this.entity = entity;
    this.oldEntity = oldEntity;
    this.metadata = metadata;
  }

}
