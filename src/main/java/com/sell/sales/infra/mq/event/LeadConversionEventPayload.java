package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.dto.ConversionMetadata;
import lombok.Getter;

@Getter
public class LeadConversionEventPayload {

  private final LeadConversionEvent entity;
  private final LeadConversionEvent oldEntity;
  private final ConversionMetadata metadata;

  @JsonCreator
  public LeadConversionEventPayload(
      @JsonProperty("entity") LeadConversionEvent entity,
      @JsonProperty("oldEntity") LeadConversionEvent oldEntity,
      @JsonProperty("metadata") ConversionMetadata metadata) {
    this.entity = entity;
    this.oldEntity = oldEntity;
    this.metadata = metadata;
  }

}
