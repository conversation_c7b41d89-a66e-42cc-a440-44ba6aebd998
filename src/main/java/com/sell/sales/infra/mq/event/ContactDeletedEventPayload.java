package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class ContactDeletedEventPayload {

  private final long id;
  private final long userId;
  private final long tenantId;
  private final int version;
  private final boolean publishUsage;

  @JsonCreator
  public ContactDeletedEventPayload(
      @JsonProperty("id") long id,
      @JsonProperty("userId") long userId,
      @JsonProperty("tenantId") long tenantId,
      @JsonProperty("version") int version,
      @JsonProperty("publishUsage") boolean publishUsage) {
    this.id = id;
    this.tenantId = tenantId;
    this.userId = userId;
    this.version = version;
    this.publishUsage = publishUsage;
  }
}
