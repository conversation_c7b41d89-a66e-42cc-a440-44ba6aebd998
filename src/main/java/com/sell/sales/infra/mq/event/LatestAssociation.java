package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.request.IdName;
import lombok.Getter;

@Getter
public class LatestAssociation extends IdName {

  @JsonProperty(value = "isLatestAssociation")
  boolean latestAssociation;

  public LatestAssociation(
      Long id,
      String name,
      boolean isLatestAssociation) {
    super(id, name);
    this.latestAssociation = isLatestAssociation;
  }
}