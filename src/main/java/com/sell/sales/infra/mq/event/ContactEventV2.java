package com.sell.sales.infra.mq.event;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.sell.sales.controller.response.lead.PhoneNumberResponse;
import com.sell.sales.domain.Contact;
import com.sell.sales.domain.ContactUtm;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.domain.GPSCoordinateEvent;
import com.sell.sales.entity.model.Email;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

@Getter
public class ContactEventV2 implements Serializable {

  public static final String CONTACT_CREATED_EVENT = "sales.contact.created";
  public static final String CONTACT_UPDATED_EVENT = "sales.contact.updated";
  private Long id;
  private Long tenantId;
  private IdName ownerId;

  private String firstName;
  private String lastName;
  private String name;
  private IdName salutation;

  private String address;
  private String city;
  private String state;
  private String zipcode;
  private String country;

  private Boolean dnd;
  private String timezone;
  private PhoneNumberResponse[] phoneNumbers;
  private Email[] emails;

  private String facebook;
  private String twitter;
  private String linkedin;

  private IdName company;
  private String designation;
  private String department;
  private Boolean stakeholder;

  private Boolean deleted;
  private Integer version;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date createdAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date updatedAt;
  private IdName createdBy;
  private IdName updatedBy;
  private Map<String, Object> customFieldValues;

  private List<Long> associatedDeals;
  Map<String, Map<Integer, String>> idNameStore = new HashMap<>();
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  private IdName importedBy;

  private IdName campaign;
  private IdName source;
  private String subSource;
  private String utmSource;
  private String utmMedium;
  private String utmCampaign;
  private String utmTerm;
  private String utmContent;
  private Double score;
  private GPSCoordinateEvent addressCoordinate;

  private ContactEventV2() {
  }

  public static ContactEventV2 valueOf(Contact contact) {
    ContactEventV2 contactEvent = new ContactEventV2();
    Map<String, Object> metaData = contact.getMetaData();
    Map<String, Map<Integer, String>> idNameStore =
        (Map<String, Map<Integer, String>>) metaData.get("idNameStore");
    contactEvent.idNameStore = idNameStore;
    contactEvent.id = contact.getId();
    contactEvent.tenantId = contact.getTenantId();
    contactEvent.ownerId = getIdName(idNameStore, contact.getOwnerId(), "ownerId");
    contactEvent.firstName = contact.getFirstName();
    contactEvent.lastName = contact.getLastName();
    contactEvent.name = contact.getName();
    contactEvent.salutation = getIdName(idNameStore, contact.getSalutation(), "salutation");
    contactEvent.timezone = contact.getTimezone();

    contactEvent.address = contact.getAddress();
    contactEvent.city = contact.getCity();
    contactEvent.state = contact.getState();
    contactEvent.zipcode = contact.getZipcode();
    contactEvent.country = contact.getCountry();

    contactEvent.department = contact.getDepartment();
    contactEvent.dnd = contact.getDnd();

    contactEvent.phoneNumbers = isEmpty(contact.getContactPhoneNumbers()) ? null : contact
        .getContactPhoneNumbers().stream()
        .map(contactPhoneNumber ->
        new PhoneNumberResponse(contactPhoneNumber.getId(), contactPhoneNumber.getType(),
            contactPhoneNumber.getCode(),
            contactPhoneNumber.getValue(),
            contactPhoneNumber.getDialCode(),
            contactPhoneNumber.isPrimary())
    ).toArray(PhoneNumberResponse[]::new);
    contactEvent.emails = contact.getEmails();
    contactEvent.facebook = contact.getFacebook();
    contactEvent.twitter = contact.getTwitter();
    contactEvent.linkedin = contact.getLinkedin();

    contactEvent.company = getIdName(idNameStore, contact.getCompany(), "company");

    contactEvent.designation = contact.getDesignation();

    contactEvent.stakeholder = contact.getStakeholder();
    contactEvent.customFieldValues = contact.getCustomFieldValues();

    contactEvent.deleted = contact.isDeleted();
    contactEvent.version = contact.getVersion();

    contactEvent.createdAt = contact.getCreatedAt();
    contactEvent.updatedAt = contact.getUpdatedAt();
    contactEvent.createdBy = getIdName(idNameStore, contact.getCreatedBy(), "createdBy");
    contactEvent.updatedBy = getIdName(idNameStore, contact.getUpdatedBy(), "updatedBy");
    contactEvent.createdViaId = contact.getCreatedViaId();
    contactEvent.createdViaName = contact.getCreatedViaName();
    contactEvent.createdViaType = contact.getCreatedViaType();
    contactEvent.updatedViaId = contact.getUpdatedViaId();
    contactEvent.updatedViaName = contact.getUpdatedViaName();
    contactEvent.updatedViaType = contact.getUpdatedViaType();
    contactEvent.addressCoordinate=contact.getContactGPSAddress()!=null?getCoordinate(contact.getContactGPSAddress().getAddressCoordinate()):null;

    contactEvent.associatedDeals = contact.getAssociatedDeals();
    contactEvent.importedBy = getIdName(idNameStore, contact.getImportedBy(), "importedBy");

    contactEvent.campaign = getIdName(idNameStore, contact.getCampaign(), "campaign");
    contactEvent.source = getIdName(idNameStore, contact.getSource(), "source");
    if (!ObjectUtils.isEmpty(contact.getContactUtms())) {
      ContactUtm contactUtm = contact.getContactUtms().stream().findFirst().get();
      contactEvent.subSource = contactUtm.getSubSource();
      contactEvent.utmSource = contactUtm.getUtmSource();
      contactEvent.utmCampaign = contactUtm.getUtmCampaign();
      contactEvent.utmContent = contactUtm.getUtmContent();
      contactEvent.utmMedium = contactUtm.getUtmMedium();
      contactEvent.utmTerm = contactUtm.getUtmTerm();
    }
    contactEvent.score = contact.getScore();

    return contactEvent;
  }

  private static IdName getIdName(
      Map<String, Map<Integer, String>> idNameStore, Long id, String fieldName) {
    if (id != null && idNameStore.containsKey(fieldName)) {
      return new IdName(id, idNameStore.get(fieldName).get(id.toString()));
    }
    return new IdName(id, null);
  }

  @Getter
  public static class IdName {

    private final Long id;
    private final String name;

    public IdName(Long id, String name) {
      this.id = id;
      this.name = name;
    }
  }

  private static GPSCoordinateEvent getCoordinate(GPSCoordinate gpsCoordinate) {
    if(gpsCoordinate==null){
      return null;
    }
    return new GPSCoordinateEvent(gpsCoordinate.getLat(), gpsCoordinate.getLon());
  }

}
