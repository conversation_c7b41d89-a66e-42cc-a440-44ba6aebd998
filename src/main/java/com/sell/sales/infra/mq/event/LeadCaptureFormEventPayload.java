package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class LeadCaptureFormEventPayload {

  private final String id;
  private final String displayName;

  @JsonCreator
  public LeadCaptureFormEventPayload(
      @JsonProperty("id") String id,
      @JsonProperty("displayName") String displayName) {
   this.id =id;
   this.displayName=displayName;
  }
}
