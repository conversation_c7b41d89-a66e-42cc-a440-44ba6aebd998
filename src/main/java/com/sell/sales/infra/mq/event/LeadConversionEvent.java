package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.sell.sales.domain.ConversionAssociationDetail;
import com.sell.sales.domain.Lead;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import lombok.Getter;

@Getter
public class LeadConversionEvent implements Serializable {

  private long id;
  private long tenantId;
  private String firstName;
  private String lastName;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date convertedAt;
  private IdName convertedBy;
  private IdName ownedBy;
  private ConversionAssociationDetail conversionAssociation;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  
  public LeadConversionEvent(){}


  public static LeadConversionEvent valueOf(Lead lead, ConversionAssociationDetail conversionAssociationDetail) {
    LeadConversionEvent LeadConversionEvent = new LeadConversionEvent();
    Map<String, Object> metaData = lead.getMetaData();
    Map<String, Map<Integer, String>> idNameStore =
        (Map<String, Map<Integer, String>>) metaData.get("idNameStore");
    LeadConversionEvent.id = lead.getId();
    LeadConversionEvent.tenantId = lead.getTenantId();
    LeadConversionEvent.firstName = lead.getFirstName();
    LeadConversionEvent.lastName = lead.getLastName();
    LeadConversionEvent.convertedAt = lead.getConvertedAt();
    LeadConversionEvent.convertedBy = getIdName(idNameStore, lead.getConvertedBy(), "convertedBy");
    LeadConversionEvent.ownedBy = getIdName(idNameStore, lead.getOwnerId(), "ownerId");
    LeadConversionEvent.conversionAssociation = conversionAssociationDetail;
    LeadConversionEvent.createdViaId = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getCreatedViaId();
    LeadConversionEvent.createdViaName = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getCreatedViaName();
    LeadConversionEvent.createdViaType = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getCreatedViaType();
    LeadConversionEvent.updatedViaId = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getUpdatedViaId();
    LeadConversionEvent.updatedViaName = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getUpdatedViaName();
    LeadConversionEvent.updatedViaType = lead.getMetaInfo() == null ? null : lead.getMetaInfo().getUpdatedViaType();

    return LeadConversionEvent;
  }
  
  private static IdName getIdName(
      Map<String, Map<Integer, String>> idNameStore, Long id, String fieldName) {
    if (id != null && idNameStore.containsKey(fieldName)) {
      return new IdName(id, idNameStore.get(fieldName).get(id.toString()));
    }
    return new IdName(id,null);
  }
  
  @Getter
  public static class IdName {
    private final Long id;
    private final String name;

    public IdName(Long id, String name) {
      this.id = id;
      this.name = name;
    }
  }
}

