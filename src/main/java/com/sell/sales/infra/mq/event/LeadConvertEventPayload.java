package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.request.lead.LeadConversionRequest;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadConvertEventPayload {
  private final LeadConversionRequest leadConversionRequest;
  private final Metadata metadata;

  @JsonCreator
  public LeadConvertEventPayload(
      @JsonProperty("entity") LeadConversionRequest leadConversionRequest,
      @JsonProperty("metadata") Metadata metadata
  ) {
    this.leadConversionRequest = leadConversionRequest;
    this.metadata = metadata;
  }

  public static String getEventName() {
    return "workflow.lead.convert";
  }

}
