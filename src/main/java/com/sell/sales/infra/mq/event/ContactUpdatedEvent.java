package com.sell.sales.infra.mq.event;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

import com.sell.sales.domain.Contact;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;

@Getter
public class ContactUpdatedEvent {

  private final Long salutation;
  private final String firstName;
  private final String lastName;
  private final List<PhoneNumber> phoneNumbers;
  private final List<Email> emails;
  private final boolean dnd;
  private final String timezone;
  private final String address;
  private final String city;
  private final String state;
  private final String zipcode;
  private final String country;

  private final String facebook;
  private final String twitter;
  private final String linkedin;

  private final Long company;
  private final String department;
  private final String designation;
  private final boolean stakeholder;

  private final Date convertedAt;

  private final Long convertedBy;
  private final Map<String, Object> customFieldValues;
  private final Long ownerId;

  private final Long tenantId;
  private final Long id;
  private final boolean deleted;
  private final Integer version;

  private final Date createdAt;
  private final Date updatedAt;
  private final Long createdBy;
  private final Long updatedBy;

  private final List<Long> associatedDeals;

  private ContactUpdatedEvent(Long salutation, String firstName, String lastName,
      List<PhoneNumber> phoneNumbers, List<Email> emails, boolean dnd, String timezone, String address, String city, String state, String zipcode,
      String country, String facebook, String twitter, String linkedin, Long company, String department, String designation, boolean stakeholder,
      Date convertedAt, Long convertedBy, Map<String, Object> customFieldValues, Long ownerId, Long tenantId, Long id, boolean deleted,
      Integer version, Date createdAt, Date updatedAt, Long createdBy, Long updatedBy, List<Long> associatedDeals) {
    this.salutation = salutation;
    this.firstName = firstName;
    this.lastName = lastName;
    this.phoneNumbers = phoneNumbers;
    this.emails = emails;
    this.dnd = dnd;
    this.timezone = timezone;
    this.address = address;
    this.city = city;
    this.state = state;
    this.zipcode = zipcode;
    this.country = country;
    this.facebook = facebook;
    this.twitter = twitter;
    this.linkedin = linkedin;
    this.company = company;
    this.department = department;
    this.designation = designation;
    this.stakeholder = stakeholder;
    this.convertedAt = convertedAt;
    this.convertedBy = convertedBy;
    this.customFieldValues = customFieldValues;
    this.ownerId = ownerId;
    this.tenantId = tenantId;
    this.id = id;
    this.deleted = deleted;
    this.version = version;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
    this.associatedDeals = associatedDeals;
  }

  public static ContactUpdatedEvent with(Contact contact) {
    return new ContactUpdatedEvent(
        contact.getSalutation(),
        contact.getFirstName(),
        contact.getLastName(),
        fromDomainPhoneNumbers(contact.getPhoneNumbers()),
        fromDomainEmails(contact.getEmails()),
        contact.getDnd(),
        contact.getTimezone(),
        contact.getAddress(),
        contact.getCity(),
        contact.getState(),
        contact.getZipcode(),
        contact.getCountry(),
        contact.getFacebook(),
        contact.getTwitter(),
        contact.getLinkedin(),
        contact.getCompany(),
        contact.getDepartment(),
        contact.getDesignation(),
        contact.getStakeholder(),
        null,
        null,
        contact.getCustomFieldValues(),
        contact.getOwnerId(),
        contact.getTenantId(),
        contact.getId(),
        contact.isDeleted(),
        contact.getVersion(),
        contact.getCreatedAt(),
        contact.getUpdatedAt(),
        contact.getCreatedBy(),
        contact.getUpdatedBy(),
        contact.getAssociatedDeals()
    );
  }

  static List<PhoneNumber> fromDomainPhoneNumbers(com.sell.sales.entity.model.PhoneNumber[] domainPhoneNumbers) {
    if(domainPhoneNumbers == null) {
      return emptyList();
    }
    return Arrays.stream(domainPhoneNumbers)
        .map(p -> new PhoneNumber(
            p.getType().name(),
            p.getCode(),
            p.getValue(),
            p.getDialCode(),
            p.isPrimary()
        ))
        .collect(toList());
  }

  static List<Email> fromDomainEmails(com.sell.sales.entity.model.Email[] domainEmails) {
    if(domainEmails == null) {
      return emptyList();
    }

    return Arrays.stream(domainEmails)
        .map(e -> new Email(
            e.getType().name(),
            e.getValue(),
            e.isPrimary()
        ))
        .collect(toList());
  }

  public static String getEventName() {
    return "sales.contact.updated";
  }

  @Getter
  static class PhoneNumber {
    private final String type;
    private final String code;
    private final String value;
    private final String dialCode;
    private final boolean isPrimary;

    PhoneNumber(String type, String code, String value, String dialCode, boolean isPrimary) {
      this.type = type;
      this.code = code;
      this.value = value;
      this.dialCode = dialCode;
      this.isPrimary = isPrimary;
    }
  }

  @Getter
  static class Email {
    private final String type;
    private final String value;
    private final boolean isPrimary;

    Email(String type, String value, boolean isPrimary) {
      this.type = type;
      this.value = value;
      this.isPrimary = isPrimary;
    }
  }
}
