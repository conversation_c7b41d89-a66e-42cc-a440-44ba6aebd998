package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DealDeletedEvent {

  private long dealId;
  private long tenantId;
  private final Long userId;
  @JsonCreator
  public DealDeletedEvent(
      @JsonProperty("id") long id,
      @JsonProperty("tenantId") long tenantId,
      @JsonProperty("userId") long userId) {
    this.dealId = id;
    this.tenantId = tenantId;
    this.userId = userId;
  }
  public static String getEventName(){
    return "deal.deleted";
  }
}
