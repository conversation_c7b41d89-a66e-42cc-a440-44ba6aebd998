package com.sell.sales.infra.mq.event;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.event.ContactEvent;
import java.util.List;
import lombok.Getter;

@Getter
public class ContactOwnerUpdateEvent {

  private long entityId;
  private long newOwnerId;
  private long oldOwnerId;
  private long tenantId;
  private List<EntityType> childEntities;
  private ContactEvent contact;
  private boolean sendNotification;

  private ContactOwnerUpdateEvent(long oldOwnerId, long tenantId, long entityId, List<EntityType> childEntities,
      ContactEvent contact, boolean sendNotification) {
    this.oldOwnerId = oldOwnerId;
    this.newOwnerId = contact.getOwnerId();
    this.tenantId = tenantId;
    this.entityId = entityId;
    this.childEntities = childEntities;
    this.contact = contact;
    this.sendNotification = sendNotification;
  }

  public static ContactOwnerUpdateEvent of(Long oldOwnerId, List<EntityType> childEntities, ContactEvent contactEvent, boolean sendNotification) {
    return new ContactOwnerUpdateEvent(oldOwnerId, contactEvent.getTenantId(), contactEvent.getId(), childEntities, contactEvent, sendNotification);
  }
}
