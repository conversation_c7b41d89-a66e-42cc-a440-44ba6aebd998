package com.sell.sales.infra.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DealNameUpdatedEvent {

  private Long id;
  private String name;
  private Long tenantId;

  @JsonCreator
  public DealNameUpdatedEvent(
      @JsonProperty("id") long id,
      @JsonProperty("name") String name,
      @JsonProperty("tenantId") long tenantId) {
    this.id = id;
    this.name = name;
    this.tenantId = tenantId;

  }

  public static String getEventName() {
    return "deal.name.updated";
  }

}
