package com.sell.sales.entity.entity;

import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.Where;

@Setter
@Getter
@Where(clause = "deleted=false")
@AccessPermission("config")
public class Picklist extends TenantAwareBaseEntity {
  private String name;
  private String displayName;

  private boolean global;
  private boolean systemDefault;

  @Transient
  private Map<Long,PicklistValue> picklistValueMap=new HashMap<>();

  @OneToMany(mappedBy = "picklist", fetch = FetchType.EAGER,
      cascade = CascadeType.ALL)
  private List<PicklistValue> values;



  public Picklist(String name, String displayName) {
    super();
    this.name = name;
    this.displayName = displayName;
    buildPicklistValueMap();
  }

  public Picklist() {
    super();
    buildPicklistValueMap();
  }

  private void buildPicklistValueMap() {
    if (ObjectUtils.isNotEmpty(values)) {
      values.forEach(picklistValue -> picklistValueMap.put(picklistValue.getId(), picklistValue));
    }
  }

  public Map<Long, PicklistValue> getPicklistValueMap() {
    if (ObjectUtils.isEmpty(picklistValueMap)) {
      buildPicklistValueMap();
    }
    return picklistValueMap;
  }
}
