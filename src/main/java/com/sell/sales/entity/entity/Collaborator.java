package com.sell.sales.entity.entity;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//@MappedSuperclass
public class Collaborator extends TenantAwareBaseEntity {

  @Enumerated(EnumType.STRING)
  private EntityType entityType;
  private Long entityId;

}
