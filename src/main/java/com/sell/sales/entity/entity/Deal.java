package com.sell.sales.entity.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.entity.annotation.ExtendedFields;
import com.sell.sales.entity.annotation.Internal;
import com.sell.sales.entity.annotation.LookupField;
import com.sell.sales.entity.model.CustomFieldAwareBaseEntity;
import java.util.Date;
import javax.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//@MappedSuperclass
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})
public class Deal extends CustomFieldAwareBaseEntity {
  private String name;
  private Date closedDate;
  private Long accountId; //TODO: This foreign key will reference to the Account

  // pipeline & stages
  private Long pipeline;
  private Long pipelineStage;
  private String pipelineStageReason;

  //contacts : This will referrance contacts
  private String requirementCurrency;
  private Double requirementBudget;
  private Date expectedClosureOn;

  //Internal
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Date convertedAt;

  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Long convertedBy;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }
}
