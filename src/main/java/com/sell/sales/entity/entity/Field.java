package com.sell.sales.entity.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.constants.InputSize;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import com.sell.sales.entity.annotation.Eventable;
import com.sell.sales.entity.model.DisplayNameAware;
import com.sell.sales.entity.model.FieldType;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by hemants on 02/04/19.
 */
@Setter
@Getter
@Eventable
@Where(clause = "deleted=false")
@AccessPermission("config")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Field extends TenantAwareBaseEntity implements DisplayNameAware {

  @NotEmpty
  @Column(nullable = false)
  private String displayName;

  @Column(length = InputSize.MAX_INPUT_SIZE_DESCRIPTION)
  private String description;

  @Column(nullable = false)
  @Enumerated(value = EnumType.STRING)
  private com.sell.sales.entity.model.FieldType type;

  private Class internalType;

  @Column(nullable = false)
  //TODO: Needs to be handled using DTO
//  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private String name;

  @ManyToOne(optional = false)
  @JoinColumn
  @JsonIgnore
  private EntityDef entityDef;

  @Transient
  private EntityType entityType;

  //TODO: Needs to be handled using DTO
//  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Boolean standard;

  private Boolean sortable;
  private Boolean filterable;
  private Boolean required;
  private boolean important;
  private boolean active;

  private Boolean multiValue;

  private Integer length;

  private Boolean isUnique;

  private String greaterThan; //String so that it can be used for various type

  private String lessThan; //String so that it can be used for various type

  private String lookupForEntity;

  //TODO: Needs to be handled using DTO
//  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Boolean internal;

  //TODO: Needs to be handled using DTO
//  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  @Transient
  private String lookupUrl;

  private boolean skipIdNameResolution;

  @ManyToOne(cascade = CascadeType.PERSIST)
  private Picklist picklist;

  private String regex;

  public Field type(FieldType type) {
    this.type = type;
    return this;
  }

  public Field isMultiValue(boolean isMultiValue) {
    multiValue = isMultiValue;
    return this;
  }
}
