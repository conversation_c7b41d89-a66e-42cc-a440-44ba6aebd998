package com.sell.sales.entity.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Where;

/**
 * Created by shashanks3 on 26/4/19.
 */
@Setter
@Getter
//@Entity
@Where(clause = "deleted=false")
@NoArgsConstructor
@AccessPermission("config")
public class PicklistValue extends TenantAwareBaseEntity {

  private String name;
  private String displayName;
  private boolean disabled;

  @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "picklist_id")
  @JsonIgnore
  private Picklist picklist;

  public PicklistValue(String displayName, Picklist picklist) {
    super();
    this.displayName = displayName;
    this.picklist = picklist;
  }
}
