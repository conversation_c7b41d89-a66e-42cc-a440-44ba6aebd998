package com.sell.sales.entity.entity;


import com.sell.sales.core.annotation.AccessPermission;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.TenantAwareBaseEntity;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by hemants on 02/04/19.
 */
//@Entity
@Getter
@Setter
@AccessPermission("config")
public class EntityDef extends TenantAwareBaseEntity {

  @Column(nullable = false)
  @Enumerated(value = EnumType.STRING)
  private EntityType name;

  private String displayName;
  private String displayNamePlural;

  @OneToMany(mappedBy = "entityDef", cascade = CascadeType.PERSIST)
  @OrderBy("id ASC")
  private List<Field> fields;
}
