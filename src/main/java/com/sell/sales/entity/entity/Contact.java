package com.sell.sales.entity.entity;

import com.sell.sales.core.annotation.FieldAttribute;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.entity.annotation.Internal;
import com.sell.sales.entity.annotation.LookupField;
import com.sell.sales.entity.annotation.PicklistField;
import com.sell.sales.entity.annotation.UrlField;
import com.sell.sales.entity.model.CustomFieldAwareBaseEntity;
import com.sell.sales.entity.model.Email;
import com.sell.sales.entity.model.PhoneNumber;
import java.util.Date;
import javax.persistence.Column;
import org.hibernate.annotations.Type;

/*@MappedSuperclass
@Getter
@Setter
@TypeDefs({
    @TypeDef(name = "PhoneJsonUserType", typeClass = PhoneJsonUserType.class),
    @TypeDef(name = "EmailJsonUserType", typeClass = EmailJsonUserType.class),
})
@ExtendedFields(value = {"ownerId", "createdAt", "updatedAt", "createdBy", "updatedBy"})*/
public class Contact extends CustomFieldAwareBaseEntity {

  @PicklistField(value = {"Mr", "Mrs", "Miss"})
  private Long salutation;
  private String firstName;
  private String lastName;

  @Type(type = "PhoneJsonUserType")
  @Column(name = "phone_numbers", columnDefinition = "jsonb")
  private PhoneNumber[] phoneNumbers;

  @Type(type = "EmailJsonUserType")
  @Column(name = "emails", columnDefinition = "jsonb")
  private Email[] emails;
  @FieldAttribute(displayName = "Do Not Disturb")
  private Boolean dnd;

  @Internal
  @PicklistField(name = "TIMEZONE")
  private String timezone;

  private String address;
  private String city;
  private String state;
  private String zipcode;

  @Internal
  @PicklistField(name = "COUNTRY")
  private String country;

  @UrlField
  private String facebook;
  @UrlField
  private String twitter;
  @UrlField
  private String linkedin;

  //Professional
  @LookupField(EntityType.COMPANY)
  private Long company;
  private String department;
  private String designation;
  @FieldAttribute(displayName = "Decision maker")
  private Boolean stakeholder;

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getOwnerId() {
    return super.getOwnerId();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getCreatedBy() {
    return super.getCreatedBy();
  }

  @Override
  @Internal
  @LookupField(EntityType.USER)
  public Long getUpdatedBy() {
    return super.getUpdatedBy();
  }

  @Override
  @Internal
  public Date getCreatedAt() {
    return super.getCreatedAt();
  }

  @Override
  @Internal
  public Date getUpdatedAt() {
    return super.getUpdatedAt();
  }
}
