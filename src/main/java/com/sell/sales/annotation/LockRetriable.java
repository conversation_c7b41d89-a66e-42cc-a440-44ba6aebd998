package com.sell.sales.annotation;

import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.core.exception.BaseException;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.exception.StaleObjectException;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import javax.persistence.OptimisticLockException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.hibernate.StaleStateException;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class LockRetriable {

  @Around(value = "@annotation(lockRetry)")
  public Object performMethodCallWithRetry(ProceedingJoinPoint joinPoint, LockRetry lockRetry) throws Throwable {

    log.debug("About to try checking the Connection");
    int initialDelay = lockRetry.initialDelay();
    int multiplier = lockRetry.multiplier();
    int maxInterval = lockRetry.maxInterval();

    int count = 0;
    long startTime = System.currentTimeMillis();
    int delay = 0;
    Object object = null;
    Throwable throwable;
    final AtomicReference<Boolean> isRetriableException = new AtomicReference<>(false);
    do {
      try {
        // Create the future call and wait for the response
        object = CompletableFuture.supplyAsync(() -> {
          try {
            return joinPoint.proceed();
          } catch (Throwable e) {
            isRetriableException.set(e instanceof StaleStateException || e instanceof ObjectOptimisticLockingFailureException || e instanceof OptimisticLockException);
            throw new IllegalStateException(e);
          }
        },
        new DelegatingSecurityContextAsyncTaskExecutor(new SimpleAsyncTaskExecutor(), SecurityContextHolder.getContext())
        ).join();

        throwable = null;
      } catch (Throwable e) {
        throwable = e.getCause();

        log.error(
            "Failed to process {} on attempt {}",
            joinPoint.getSignature().getName(),
            count + 1,
            e);

        delay = count == 0 ? initialDelay : count * multiplier * initialDelay;
        log.info("Sleeping for {} seconds", delay);
        if(!isRetriableException.get()){
          throwException(throwable);
          throw new SalesException(new ErrorResource(throwable.getMessage(),throwable.getMessage()));
        }
        Thread.sleep(delay);
      }
      count++;
    } while ((Objects.nonNull(throwable) && count < multiplier
        && System.currentTimeMillis() - startTime < maxInterval) && isRetriableException.get());
    log.debug("About to exit with success {}, count {} and delay {}ms", Objects.isNull(throwable),  count, delay);

    if (throwable != null) {
      throwException(throwable);
    }
    return object;
  }

  private Object process(ProceedingJoinPoint joinPoint, AtomicReference<Boolean> isRetriableException) {
    try {
      return joinPoint.proceed();
    } catch (Throwable e) {
      isRetriableException.set(e instanceof StaleStateException || e instanceof ObjectOptimisticLockingFailureException || e instanceof OptimisticLockException);
      throw new IllegalStateException(e);
    }
  }

  private void throwException(Throwable throwable) {
    if(throwable.getCause() instanceof SalesException){
      throw (SalesException) throwable.getCause();
    }
    if(throwable.getCause() instanceof BaseException){
      throw (BaseException) throwable.getCause();
    }
    if( throwable.getCause() instanceof ConcurrencyFailureException || throwable.getCause() instanceof StaleStateException ||
    throwable.getCause() instanceof ObjectOptimisticLockingFailureException){
      throw new StaleObjectException();
    }
    throw new SalesException(SalesErrorCodes.SALES_ERROR);
  }
}
