package com.sell.sales.converter;

import com.sell.sales.controller.request.lead.ConversionAssociationDTO;
import com.sell.sales.controller.request.lead.DuplicateEntityDTO;
import com.sell.sales.controller.request.lead.LeadImportRequest;
import com.sell.sales.controller.request.lead.LeadRequest;
import com.sell.sales.controller.request.lead.LeadUpdateRequest;
import com.sell.sales.controller.request.lead.ProductDTO;
import com.sell.sales.controller.response.lead.LeadResponse;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.Product;
import com.sell.sales.domain.layout.MultiConversionAssociation;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LeadMapper {

  @Mapping(target = "timezone", expression = "java( fromLead.getTimezone() )")
  @Mapping(target = "salutation", expression = "java( fromLead.getSalutation() )")
  @Mapping(target = "country", expression = "java( fromLead.getCountry() )")
  @Mapping(target = "companyIndustry", expression = "java( fromLead.getCompanyIndustry() )")
  @Mapping(target = "companyBusinessType", expression = "java( fromLead.getCompanyBusinessType())")
  @Mapping(target = "companyEmployees", expression = "java( fromLead.getCompanyEmployees())")
  @Mapping(target = "companyCountry", expression = "java( fromLead.getCompanyCountry())")
  @Mapping(target = "requirementCurrency", expression = "java( fromLead.getRequirementCurrency())")
  @Mapping(target = "campaign", expression = "java( fromLead.getCampaign())")
  @Mapping(target = "source", expression = "java( fromLead.getSource() )")
  @Mapping(target = "score", expression = "java( fromLead.getScore() )")
  Lead fromLeadToLead(Lead fromLead, @MappingTarget Lead toLead);

  @Mapping(target = "firstName", expression = "java(leadRequest.getFirstName() != null ? leadRequest.getFirstName().trim() : leadRequest.getFirstName())")
  @Mapping(target = "lastName", expression = "java(leadRequest.getLastName() != null ? leadRequest.getLastName().trim() : leadRequest.getLastName())")
  @Mapping(target = "timezone", expression = "java( leadRequest.getTimezone() )")
  @Mapping(target = "salutation", expression = "java( leadRequest.getSalutation() )")
  @Mapping(target = "country", expression = "java( leadRequest.getCountry() )")
  @Mapping(target = "companyIndustry", expression = "java( leadRequest.getCompanyIndustry() )")
  @Mapping(target = "companyBusinessType", expression = "java( leadRequest.getCompanyBusinessType())")
  @Mapping(target = "companyEmployees", expression = "java( leadRequest.getCompanyEmployees())")
  @Mapping(target = "companyCountry", expression = "java( leadRequest.getCompanyCountry())")
  @Mapping(target = "requirementCurrency", expression = "java( leadRequest.getRequirementCurrency())")
  @Mapping(target = "campaign", expression = "java( leadRequest.getCampaign())")
  @Mapping(target = "source", expression = "java( leadRequest.getSource() )")
  @Mapping(ignore = true, target = "pipeline")
  @Mapping(ignore = true, target = "pipelineStage")
  @Mapping(target = "ownerId", expression = "java( leadRequest.getOwnerId())")
  @Mapping(target = "explicitOwnerId", expression = "java( leadRequest.getOwnerId() != null ?  true : false)")
  @Mapping(target = "createdAt", expression = "java(leadRequest.getCreatedAt())")
  @Mapping(target = "updatedAt", expression = "java(leadRequest.getUpdatedAt())")
  @Mapping(target = "createdBy", expression = "java(leadRequest.getCreatedBy())")
  @Mapping(target = "updatedBy", expression = "java(leadRequest.getUpdatedBy())")
  @Mapping(target = "score", expression = "java(leadRequest.getScore())")
  Lead fromLeadRequest(LeadRequest leadRequest);

  @Mapping(target = "firstName", expression = "java(leadUpdateRequest.getFirstName() != null ? leadUpdateRequest.getFirstName().trim() : leadUpdateRequest.getFirstName())")
  @Mapping(target = "lastName", expression = "java(leadUpdateRequest.getLastName() != null ? leadUpdateRequest.getLastName().trim() : leadUpdateRequest.getLastName())")
  @Mapping(target = "timezone", expression = "java( leadUpdateRequest.getTimezone() )")
  @Mapping(target = "salutation", expression = "java( leadUpdateRequest.getSalutation() )")
  @Mapping(target = "country", expression = "java( leadUpdateRequest.getCountry() )")
  @Mapping(target = "companyIndustry", expression = "java( leadUpdateRequest.getCompanyIndustry() )")
  @Mapping(target = "companyBusinessType", expression = "java( leadUpdateRequest.getCompanyBusinessType())")
  @Mapping(target = "companyEmployees", expression = "java( leadUpdateRequest.getCompanyEmployees())")
  @Mapping(target = "companyCountry", expression = "java( leadUpdateRequest.getCompanyCountry())")
  @Mapping(target = "requirementCurrency", expression = "java( leadUpdateRequest.getRequirementCurrency())")
  @Mapping(target = "campaign", expression = "java( leadUpdateRequest.getCampaign())")
  @Mapping(target = "source", expression = "java( leadUpdateRequest.getSource() )")
  @Mapping(ignore = true, target = "pipeline")
  @Mapping(ignore = true, target = "pipelineStage")
  @Mapping(target = "ownerId", expression = "java( leadUpdateRequest.getOwnerId())")
  @Mapping(target = "explicitOwnerId", expression = "java( leadUpdateRequest.getOwnerId() != null ?  true : false)")
  @Mapping(target = "leadPhoneNumbers",expression = "java(leadUpdateRequest.getLeadPhoneNumbers(leadUpdateRequest.getPhoneNumbers()))")
  @Mapping(target = "leadCompanyPhoneNumbers",expression = "java(leadUpdateRequest.getLeadCompanyPhoneNumbers(leadUpdateRequest.getCompanyPhones()))")
  @Mapping(target = "score", expression = "java( convertScore(leadUpdateRequest))")
  Lead fromLeadRequest(LeadUpdateRequest leadUpdateRequest);

  @Mapping(target = "firstName", expression = "java(leadUpdateRequest.getFirstName() != null ? leadUpdateRequest.getFirstName().trim() : leadUpdateRequest.getFirstName())")
  @Mapping(target = "lastName", expression = "java(leadUpdateRequest.getLastName() != null ? leadUpdateRequest.getLastName().trim() : leadUpdateRequest.getLastName())")
  @Mapping(target = "timezone", expression = "java( leadUpdateRequest.getTimezone() )")
  @Mapping(target = "salutation", expression = "java( leadUpdateRequest.getSalutation() )")
  @Mapping(target = "country", expression = "java( leadUpdateRequest.getCountry() )")
  @Mapping(target = "companyIndustry", expression = "java( leadUpdateRequest.getCompanyIndustry() )")
  @Mapping(target = "companyBusinessType", expression = "java( leadUpdateRequest.getCompanyBusinessType())")
  @Mapping(target = "companyEmployees", expression = "java( leadUpdateRequest.getCompanyEmployees())")
  @Mapping(target = "companyCountry", expression = "java( leadUpdateRequest.getCompanyCountry())")
  @Mapping(target = "requirementCurrency", expression = "java( leadUpdateRequest.getRequirementCurrency())")
  @Mapping(target = "campaign", expression = "java( leadUpdateRequest.getCampaign())")
  @Mapping(target = "source", expression = "java( leadUpdateRequest.getSource() )")
  @Mapping(ignore = true, target = "pipeline")
  @Mapping(ignore = true, target = "pipelineStage")
  @Mapping(ignore = true, target = "products")
  @Mapping(target = "leadPhoneNumbers",expression = "java(leadUpdateRequest.getLeadPhoneNumbers(leadUpdateRequest.getPhoneNumbers()))")
  @Mapping(target = "leadCompanyPhoneNumbers",expression = "java(leadUpdateRequest.getLeadCompanyPhoneNumbers(leadUpdateRequest.getCompanyPhones()))")
  Lead fromLeadRequest(LeadImportRequest leadUpdateRequest);

  @Named("toMultiConversionDetails")
  static List<ConversionAssociationDTO> toMultiConversionDetails(Set<MultiConversionAssociation> multiConversionAssociations) {
    return Optional.ofNullable(multiConversionAssociations)
        .map(conversion -> {
          List<ConversionAssociationDTO> list = new ArrayList<>();

          multiConversionAssociations.stream().filter(association -> association.getEntityType().equals(EntityType.DEAL))
              .forEach(entry -> {
                if (entry.getEntityId() != null) {
                  list.add(new ConversionAssociationDTO(EntityType.DEAL, entry.getEntityId(), entry.getConvertedAt()));
                }
              });
          multiConversionAssociations.stream().filter(association -> association.getEntityType().equals(EntityType.CONTACT))
              .forEach(entry -> {
                if (entry.getEntityId() != null) {
                  list.add(new ConversionAssociationDTO(EntityType.CONTACT, entry.getEntityId(), entry.getConvertedAt()));
                }
              });
          multiConversionAssociations.stream().filter(association -> association.getEntityType().equals(EntityType.COMPANY))
              .forEach(entry -> {
                if (entry.getEntityId() != null) {
                  list.add(new ConversionAssociationDTO(EntityType.COMPANY, entry.getEntityId(), entry.getConvertedAt()));
                }
              });

          return list;
        }).orElse(new ArrayList<>());
  }

  List<DuplicateEntityDTO> toDuplicateEntityDTOList(List<Lead> lead);

  Product map(ProductDTO productDTO);

  ProductDTO map(Product product);

  @Mapping(source = "multiConversionAssociations", target = "conversionDetails", qualifiedByName = "toMultiConversionDetails")
  @Mapping(target = "salutation", expression = "java( lead.getSalutation() )")
  @Mapping(target = "timezone", expression = "java( lead.getTimezone() )")
  @Mapping(target = "country", expression = "java( lead.getCountry() )")
  @Mapping(target = "companyIndustry", expression = "java( lead.getCompanyIndustry() )")
  @Mapping(target = "companyBusinessType", expression = "java( lead.getCompanyBusinessType())")
  @Mapping(target = "companyEmployees", expression = "java( lead.getCompanyEmployees())")
  @Mapping(target = "companyCountry", expression = "java( lead.getCompanyCountry())")
  @Mapping(target = "requirementCurrency", expression = "java( lead.getRequirementCurrency())")
  @Mapping(target = "campaign", expression = "java( lead.getCampaign())")
  @Mapping(target = "source", expression = "java( lead.getSource() )")
  @Mapping(ignore = true, target = "pipeline")
  @Mapping(target = "latestActivityCreatedAt", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getLatestActivityCreatedAt(): null)")
  @Mapping(target = "createdViaId", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getCreatedViaId(): null)")
  @Mapping(target = "createdViaName", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getCreatedViaName(): null)")
  @Mapping(target = "createdViaType", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getCreatedViaType(): null)")
  @Mapping(target = "updatedViaId", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getUpdatedViaId(): null)")
  @Mapping(target = "updatedViaName", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getUpdatedViaName(): null)")
  @Mapping(target = "updatedViaType", expression = "java(lead.getMetaInfo() != null ? lead.getMetaInfo().getUpdatedViaType(): null)")
  @Mapping(target = "subSource", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getSubSource(): null)")
  @Mapping(target = "utmSource", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getUtmSource(): null)")
  @Mapping(target = "utmCampaign", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getUtmCampaign(): null)")
  @Mapping(target = "utmMedium", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getUtmMedium(): null)")
  @Mapping(target = "utmContent", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getUtmContent(): null)")
  @Mapping(target = "utmTerm", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(lead.getLeadUtms()) ? lead.getLeadUtms().stream().findFirst().get().getUtmTerm(): null)")
  @Mapping(target = "phoneNumbers", expression = "java(lead.getLeadPhones())")
  @Mapping(target = "companyPhones", expression = "java(lead.getLeadCompanyPhones())")
  @Mapping(target = "score", expression = "java( lead.getScore() )")
  LeadResponse toLeadResponse(Lead lead);

  default Double convertScore(LeadUpdateRequest leadUpdateRequest) {
    if (ObjectUtils.isEmpty(leadUpdateRequest.getScore())) {
      return null;
    }
    return BigDecimal.valueOf(leadUpdateRequest.getScore())
        .setScale(2, RoundingMode.HALF_DOWN)
        .doubleValue();
  }

  @Mapping(target = "pipeline", expression = "java(existingLead.populatePipeline())")
  @Mapping(target = "phoneNumbers", expression = "java(existingLead.populateLeadPhones())")
  @Mapping(target = "companyPhones", expression = "java(existingLead.populateLeadCompanyPhones())")
  @Mapping(target = "subSource", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getSubSource(): null)")
  @Mapping(target = "utmSource", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getUtmSource(): null)")
  @Mapping(target = "utmCampaign", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getUtmCampaign(): null)")
  @Mapping(target = "utmMedium", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getUtmMedium(): null)")
  @Mapping(target = "utmContent", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getUtmContent(): null)")
  @Mapping(target = "utmTerm", expression = "java(!org.springframework.util.ObjectUtils.isEmpty(existingLead.getLeadUtms()) ? existingLead.getLeadUtms().stream().findFirst().get().getUtmTerm(): null)")
  LeadUpdateRequest fromLeadToLeadUpdateRequest(String input, Lead existingLead);
}
