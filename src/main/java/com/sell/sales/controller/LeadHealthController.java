package com.sell.sales.controller;

import com.sell.sales.service.lead.LeadHealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/vVNJ1C2HHA2V0Q2HH/leads")
public class LeadHealthController {

    private final LeadHealthService leadHealthService;

    @Autowired
    public LeadHealthController(LeadHealthService leadHealthService) {
        this.leadHealthService = leadHealthService;
    }

    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Integer> getLeadHealth() {
        Integer leadHealth = leadHealthService.getLeadHealth();
        if (leadHealth == 200) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
    }
}
