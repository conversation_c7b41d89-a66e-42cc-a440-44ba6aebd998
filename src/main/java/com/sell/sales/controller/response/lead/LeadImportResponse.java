package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class LeadImportResponse {

  private final ImportStatus importStatus;
  private String importMessage;
  private final long leadId;

  @JsonCreator
  public LeadImportResponse(
          @JsonProperty("importStatus") ImportStatus importStatus,
          @JsonProperty("importMessage") String importMessage,
          @JsonProperty("leadId") Long leadId) {
    this.importStatus = importStatus;
    this.importMessage = importMessage;
    this.leadId = leadId;
  }
}
