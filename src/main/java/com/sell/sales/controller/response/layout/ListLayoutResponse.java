package com.sell.sales.controller.response.layout;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.domain.FieldType;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ListLayoutResponse implements Serializable{
  private boolean leftNav;
  private PageConfig pageConfig;
  private DefaultConfig defaultConfig;
  @JsonCreator
  public ListLayoutResponse(@JsonProperty("leftNav") boolean leftNav, @JsonProperty("pageConfig") PageConfig pageConfig,
      @JsonProperty("defaultConfig") DefaultConfig defaultConfig) {
    this.leftNav = leftNav;
    this.pageConfig = pageConfig;
    this.defaultConfig = defaultConfig;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class PageConfig implements Serializable{
    private ActionConfig actionConfig;
    private TableConfig tableConfig;

    @JsonCreator
    public PageConfig(@JsonProperty("actionConfig") ActionConfig actionConfig, @JsonProperty("tableConfig") TableConfig tableConfig) {
      this.actionConfig = actionConfig;
      this.tableConfig = tableConfig;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class ActionConfig implements Serializable{
    private boolean search;
    private boolean filter;
    private boolean create;
    private boolean importItems;
    private boolean columnSelector;

    @JsonCreator
    public ActionConfig(@JsonProperty("search") boolean search, @JsonProperty("filter")boolean filter, @JsonProperty("create")boolean create,
        @JsonProperty("importItems")boolean importItems, @JsonProperty("columnSelector")boolean columnSelector) {
      this.search = search;
      this.filter = filter;
      this.create = create;
      this.importItems = importItems;
      this.columnSelector = columnSelector;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class TableConfig implements Serializable{
    private String fetchURL;
    private String searchService;
    private RecordClickAction recordClickAction;
    private String clickActionUrl;

    private List<TableColumn> columns;



    public void addToColumns(List<TableColumn> columnList) {
      if (columns == null) {
        columns = columnList;
      } else {
        columns.addAll(columnList);
      }
    }

    @JsonCreator
    public TableConfig(@JsonProperty("fetchURL") String fetchURL, @JsonProperty("searchService")String searchService,
        @JsonProperty("recordClickAction") RecordClickAction recordClickAction, @JsonProperty("clickActionUrl")String clickActionUrl,
        @JsonProperty("columns") List<TableColumn> columns) {
      this.fetchURL = fetchURL;
      this.searchService = searchService;
      this.recordClickAction = recordClickAction;
      this.clickActionUrl = clickActionUrl;
      this.columns = columns;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class TableColumn implements Serializable{
    private String id;
    private String header;
    private Boolean isStandard;
    private Boolean isFilterable;
    private Boolean isSortable;
    private Boolean isInternal;
    private FieldType fieldType;
    private Picklist picklist;
    private Boolean multiValue;
    private Lookup lookup;
    private Object values;
    private boolean showDefaultOptions;
    private List<String> relatedFieldIds;
    private List<FieldType> relatedFieldTypes;
    private Boolean active;
    private List<ColorCode> colorConfiguration;
    private boolean isMasked;
    private String primaryField;

    @JsonCreator
    public TableColumn(@JsonProperty("id") String id, @JsonProperty("header") String header, @JsonProperty("isStandard") Boolean isStandard,
        @JsonProperty("isFilterable") Boolean isFilterable,
        @JsonProperty("isSortable") Boolean isSortable,
        @JsonProperty("isInternal") Boolean isInternal,
        @JsonProperty("fieldType") FieldType fieldType,
        @JsonProperty("picklist") Picklist picklist,
        @JsonProperty("multiValue") Boolean multiValue,
        @JsonProperty("lookup") Lookup lookup,
        @JsonProperty("values") Object values,
        @JsonProperty("showDefaultOptions") boolean showDefaultOptions,
        @JsonProperty("active") Boolean active,
        @JsonProperty("isMasked") boolean isMasked,
        @JsonProperty("primaryField") String primaryField) {
      this.id = id;
      this.header = header;
      this.isStandard = isStandard;
      this.isFilterable = isFilterable;
      this.isSortable = isSortable;
      this.isInternal = isInternal;
      this.fieldType = fieldType;
      this.picklist = picklist;
      this.multiValue = multiValue;
      this.lookup = lookup;
      this.values = values;
      this.showDefaultOptions = showDefaultOptions;
      this.active = active;
      this.isMasked = isMasked;
      this.primaryField = primaryField;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class Picklist implements Serializable{
    Long id;
    String name;
    List<PicklistValue> picklistValues;
    @JsonCreator
    public Picklist(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("picklistValues") List<PicklistValue> picklistValues) {
      this.id = id;
      this.name = name;
      this.picklistValues = picklistValues;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class Lookup implements Serializable{
    private String entity;
    private String lookupUrl;

    @JsonCreator
    public Lookup(@JsonProperty("entity") String entity, @JsonProperty("lookupUrl") String lookupUrl) {
      this.entity = entity;
      this.lookupUrl = lookupUrl;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class PicklistValue implements Serializable{
    private Long id;
    private String name;
    private String displayName;
    @JsonCreator
    public PicklistValue(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("displayName") String displayName) {
      this.id = id;
      this.name = name;
      this.displayName = displayName;
    }
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class ColorCode implements Serializable {

    private String hexCode;
    private String operator;
    private Number from;
    private Number to;
    private Number value;

    @JsonCreator
    public ColorCode(
        @JsonProperty("hexCode") String hexCode,
        @JsonProperty("operator") String operator,
        @JsonProperty("from") Number from,
        @JsonProperty("to") Number to,
        @JsonProperty("value") Number value) {
      this.hexCode = hexCode;
      this.operator = operator;
      this.from = from;
      this.to = to;
      this.value = value;
    }
  }

  public enum RecordClickAction {
    VIEW, EDIT
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class DefaultConfig implements Serializable{
    private String[] fields;
    @JsonCreator
    public DefaultConfig(@JsonProperty("fields") String[] fields) {
      this.fields = fields;
    }

  }
}
