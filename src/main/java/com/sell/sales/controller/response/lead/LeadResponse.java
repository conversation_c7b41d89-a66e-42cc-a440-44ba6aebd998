package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.sell.sales.controller.request.lead.ConversionAssociationDTO;
import com.sell.sales.controller.request.lead.Pipeline;
import com.sell.sales.controller.request.lead.ProductDTO;
import com.sell.sales.core.dto.BaseResponse;
import com.sell.sales.domain.ForecastingType;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.entity.model.Email;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(Include.NON_EMPTY)
public class LeadResponse extends BaseResponse {
  private Long id;
  private Long ownerId;

  private String firstName;
  private String lastName;

  private PhoneNumberResponse[] phoneNumbers;

  private Long salutation;
  private String tags;
  private String[] photoUrls;
  private Email[] emails;

  // pipeline & stages
  private Pipeline pipeline;
  private String pipelineStageReason;
  private ForecastingType forecastingType;

  private String timezone;
  private String city;
  private String state;
  private String zipcode;
  private String country;

  private String department;
  private Boolean dnd;
  private String facebook;
  private String twitter;
  private String linkedIn;
  private String address;
  private GPSCoordinate addressCoordinate;

  // company
  private String companyName;
  private String companyAddress;
  private GPSCoordinate companyAddressCoordinate;
  private String companyCity;
  private String companyState;
  private String companyZipcode;
  private String companyCountry;
  private Integer companyEmployees;
  private Double companyAnnualRevenue;
  private String companyWebsite;
  private PhoneNumberResponse[] companyPhones;
  private String companyIndustry;
  private String companyBusinessType;

  // requirement
  private String requirementName;
  private String requirementCurrency;
  private Double requirementBudget;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date expectedClosureOn;
  private List<ProductDTO> products;

  // Campaign
  private String firstCampaign;
  private String firstSource;
  private String firstSubSource;
  private String firstMedium;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date firstRespondedAt;
  private String lastCampaign;
  private String lastSource;
  private String lastSubSource;
  private String lastMedium;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date lastRespondedAt;

  // Internal
  private String quality;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date lastContactedAt;
  private String lastContactedMode;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date lastSuccessfulContactAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date lastAttemptedContactAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date lastSeenAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date nextActivityAt;
  private String lastNote;
  private String emailStatus;
  private String phoneStatus;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date convertedAt;
  private Long convertedBy;
  private String ip;
  private String designation;
  private Long campaign;
  private Long source;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date actualClosureDate;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date latestActivityCreatedAt;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  private String subSource;
  private String utmSource;
  private String utmMedium;
  private String utmCampaign;
  private String utmTerm;
  private String utmContent;
  private Long importedBy;

  //Conversion Details
  private List<ConversionAssociationDTO> conversionDetails;

  // CustomFields
  private Map<String, Object> customFieldValues;

  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date createdAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date updatedAt;
  private Double score;
}
