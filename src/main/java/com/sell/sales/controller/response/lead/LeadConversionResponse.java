package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.request.lead.ConversionAssociationDTO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadConversionResponse {

  private final long ownerId;
  private final Date convertedAt;
  private final long convertedBy;
  private final Map<String, Object> metaData;
  private final List<ConversionAssociationDTO> conversionDetails;

  @JsonCreator
  public LeadConversionResponse(
      @JsonProperty("ownerId") long ownerId,
      @JsonProperty("convertedAt") Date convertedAt,
      @JsonProperty("convertedBy") long convertedBy,
      @JsonProperty("metaData") Map<String, Object> metaData,
      @JsonProperty("conversionDetails") List<ConversionAssociationDTO> conversionDetails) {
    this.ownerId = ownerId;
    this.convertedAt = convertedAt;
    this.convertedBy = convertedBy;
    this.metaData = metaData;
    this.conversionDetails = conversionDetails;
  }
}
