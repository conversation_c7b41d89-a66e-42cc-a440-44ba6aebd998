package com.sell.sales.controller.response.contact;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.sell.sales.controller.response.lead.PhoneNumberResponse;
import com.sell.sales.core.dto.BaseResponse;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.entity.model.Email;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_EMPTY)
public class ContactResponse extends BaseResponse {

  private Long id;
  private Long ownerId;
  private Long salutation;
  private String firstName;
  private String lastName;

  private PhoneNumberResponse[] phoneNumbers;
  private Email[] emails;

  private Map<String, Object> customFieldValues;

  private boolean dnd;
  private String timezone;
  private String address;
  private String city;
  private String state;
  private String zipcode;
  private String country;

  private String facebook;
  private String twitter;
  private String linkedin;


  //Professional
  private Long company;
  private String department;
  private String designation;
  private boolean stakeholder;

  //Internal
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date convertedAt;
  private Long convertedBy;
  private String createdViaId;
  private String createdViaName;
  private String createdViaType;
  private String updatedViaId;
  private String updatedViaName;
  private String updatedViaType;
  private Long importedBy;
  private GPSCoordinate addressCoordinate;

  //Campaign Information
  private Long campaign;
  private Long source;
  private String subSource;
  private String utmSource;
  private String utmMedium;
  private String utmCampaign;
  private String utmTerm;
  private String utmContent;
  private Double score;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date createdAt;
  @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
  private Date updatedAt;
}
