package com.sell.sales.controller.response.pipeline;

import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.dto.BaseResponse;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PipelineResponse extends BaseResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long id;
  private String name;
  private EntityType entityType;

  private String[] unqualifiedReasons;
  private String[] lostReasons;
  private List<PipelineStageResponse> stages;
  private boolean active;
}
