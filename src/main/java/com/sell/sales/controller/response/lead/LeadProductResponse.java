package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class LeadProductResponse {
  private final List<Long> leadIds;

  @JsonCreator
  public LeadProductResponse(@JsonProperty("leadIds") List<Long> leadIds){
    this.leadIds = leadIds;
  }
}
