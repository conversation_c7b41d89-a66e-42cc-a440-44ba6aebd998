package com.sell.sales.controller.response.contact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.response.lead.ImportStatus;
import lombok.Getter;

@Getter
public class ContactImportResponse {

  private final ImportStatus importStatus;
  private final String importMessage;
  private final long contactId;

  @JsonCreator
  public ContactImportResponse(
      @JsonProperty("importStatus") ImportStatus importStatus,
      @JsonProperty("importMessage") String importMessage,
      @JsonProperty("contactId") Long contactId) {
    this.importStatus = importStatus;
    this.importMessage = importMessage;
    this.contactId = contactId;
  }
}
