package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.entity.model.PhoneType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class PhoneNumberResponse {
  private Long id;
  private PhoneType type;
  private String code;
  private String value;
  private String dialCode;
  private boolean isPrimary;

  @JsonCreator
  public PhoneNumberResponse(
     @JsonProperty("id") Long id,
     @JsonProperty("type") PhoneType type,
     @JsonProperty("code") String code,
     @JsonProperty("value") String value,
     @JsonProperty("dialCode") String dialCode,
     @JsonProperty("primary") boolean isPrimary) {
    this.id = id;
    this.type = type;
    this.code = code;
    this.value = value;
    this.dialCode = dialCode;
    this.isPrimary = isPrimary;
  }

  public void mask(){
    this.value = "****".concat(this.value.substring(this.value.length() - 3));
  }
}
