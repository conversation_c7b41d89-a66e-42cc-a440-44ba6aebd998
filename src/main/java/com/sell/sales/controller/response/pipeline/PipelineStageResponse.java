package com.sell.sales.controller.response.pipeline;

import com.sell.sales.core.dto.BaseResponse;
import com.sell.sales.domain.ForecastingType;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PipelineStageResponse  extends BaseResponse implements Serializable{

  private static final long serialVersionUID = 1L;

  private Long id;
  private String name;
  private String description;
  private ForecastingType forecastingType;
  private int position;
  private Integer winLikelihood;
}
