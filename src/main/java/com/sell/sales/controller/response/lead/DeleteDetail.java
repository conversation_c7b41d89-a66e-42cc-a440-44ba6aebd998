package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class DeleteDetail {

  private final long entityId;
  private final long ownerId;
  private final long tenantId;
  private final ResultType result;

  @JsonCreator
  public DeleteDetail(@JsonProperty("entityId") long entityId, @JsonProperty("ownerId") long ownerId,
      @JsonProperty("tenantId") long tenantId, @JsonProperty("result") ResultType result) {
    this.entityId = entityId;
    this.ownerId = ownerId;
    this.tenantId = tenantId;
    this.result = result;
  }
}
