package com.sell.sales.controller.response.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class ReassignDetail {

  private final long entityId;
  private final long oldOwnerId;
  private final long newOwnerId;
  private final ResultType result;

  @JsonCreator
  public ReassignDetail(@JsonProperty("entityId") long entityId, @JsonProperty("oldOwnerId") long oldOwnerId,
      @JsonProperty("newOwnerId") long newOwnerId, @JsonProperty("result") ResultType result) {
    this.entityId = entityId;
    this.oldOwnerId = oldOwnerId;
    this.newOwnerId = newOwnerId;
    this.result = result;
  }
}
