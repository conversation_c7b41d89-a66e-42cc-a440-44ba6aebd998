package com.sell.sales.controller;

import com.sell.sales.service.contact.ContactHealthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/vQ2HHDWROYXJPVNJ1/contacts")
public class ContactHealthController {

  private final ContactHealthService contactHealthService;

  @Autowired
  public ContactHealthController(ContactHealthService contactHealthService) {
    this.contactHealthService = contactHealthService;
  }

  @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity getContactHealth() {
    int contactHealth = contactHealthService.getContactHealth();
    if (contactHealth == 200) {
      return ResponseEntity.status(HttpStatus.OK).build();
    }
    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
  }

}
