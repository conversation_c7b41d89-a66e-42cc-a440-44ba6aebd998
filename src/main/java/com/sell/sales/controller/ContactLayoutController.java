package com.sell.sales.controller;

import com.sell.sales.controller.response.layout.ListLayoutResponse;
import com.sell.sales.service.lead.ContactLayoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/contacts/layout")
public class ContactLayoutController {
  private final ContactLayoutService contactLayoutService;

  @Autowired
  public ContactLayoutController(ContactLayoutService contactLayoutService) {
    this.contactLayoutService = contactLayoutService;
  }
  @GetMapping(
      value = "/list",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ListLayoutResponse getListLayout() {
    return contactLayoutService.getListLayout();
  }
}
