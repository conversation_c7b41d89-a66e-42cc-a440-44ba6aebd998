package com.sell.sales.controller;

import com.sell.sales.controller.response.layout.ListLayoutResponse;
import com.sell.sales.service.lead.LeadLayoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/leads/layout")
public class LeadLayoutController{
  private final LeadLayoutService leadLayoutService;

  @Autowired
  public LeadLayoutController(LeadLayoutService leadLayoutService) {
    this.leadLayoutService = leadLayoutService;
  }
  @GetMapping(
      value = "/list",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ListLayoutResponse getListLayout() {
    return leadLayoutService.getListLayout();
  }
}
