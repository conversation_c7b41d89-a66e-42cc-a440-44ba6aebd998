package com.sell.sales.controller;

import static com.sell.sales.service.contact.ContactService.LEAD_CONVERSION;
import static com.sell.sales.utils.ExceptionWrapper.wrapException;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.sell.sales.controller.request.UpdateOwnerRequest;
import com.sell.sales.controller.request.contact.ContactCreateRequest;
import com.sell.sales.controller.request.contact.ContactImportRequestData;
import com.sell.sales.controller.request.contact.ContactPatchRequest;
import com.sell.sales.controller.request.contact.ContactUpdateRequest;
import com.sell.sales.controller.response.DuplicateCheckResponse;
import com.sell.sales.controller.response.contact.ContactResponse;
import com.sell.sales.controller.response.contact.ContactSummary;
import com.sell.sales.controller.response.lead.LeadResponse;
import com.sell.sales.core.annotation.ApiPageable;
import com.sell.sales.core.client.InternalShareRuleService;
import com.sell.sales.core.client.SharableEntityController;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.core.domain.FieldErrorResource;
import com.sell.sales.core.utils.LogMarker;
import com.sell.sales.domain.Contact;
import com.sell.sales.domain.ContactGPSAddress;
import com.sell.sales.domain.ContactUtm;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.LeadGPSAddress;
import com.sell.sales.domain.layout.Layout;
import com.sell.sales.dto.LookUp;
import com.sell.sales.dto.mapper.ContactMapper;
import com.sell.sales.entity.dto.ChangeOwnerResponse;
import com.sell.sales.entity.model.EntityUniquenessStrategy;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.PicklistValueService;
import com.sell.sales.service.contact.ContactImportService;
import com.sell.sales.service.contact.ContactService;
import com.sell.sales.utils.ExceptionWrapper.CheckedFunction;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

@RestController
@RequestMapping("/v1/contacts")
@Tag(name = "Contacts", description = "APIs for creating & managing contacts")
@Slf4j
public class ContactController extends SharableEntityController {

  @Autowired
  private ContactService contactService;
  @Autowired
  ApplicationContext applicationContext;
  @Autowired
  private ContactMapper mapper;
  @Autowired
  private PicklistValueService picklistValueService;
  @Autowired
  private ContactImportService contactImportService;
  @Autowired
  private UserFacade userFacade;

  protected ContactController(InternalShareRuleService client) {
    super(client);
  }

  @PostMapping(
      produces = MediaType.APPLICATION_JSON_VALUE,
      consumes = MediaType.APPLICATION_JSON_VALUE)
  @Operation(description = "Create contact", extensions = {
      @Extension(name = "access-policy", properties = {
          @ExtensionProperty(name = "action", value = "create"),
          @ExtensionProperty(name = "policy", value = "records"),
          @ExtensionProperty(name = "resource", value = "contact")
      })
  })
  public ContactResponse createContact(
      @Valid @RequestBody ContactCreateRequest contactCreateRequest) {
    Contact contact = mapper.fromContactCreateRequestToContact(contactCreateRequest);
    contact.addContactUtmValues(populateCreateContactUtmValues(contactCreateRequest));
    populateGpsAddress(contactCreateRequest.getAddressCoordinate(), contact);
    Contact contact1 = contactService.create(contact);
    ContactResponse contactResponse = mapper.fromContactToContactResponse(contact1);
    toGpsAddress(contactResponse,contact1);
    return contactResponse;
  }

  @Operation(summary = "Get contact")
  @GetMapping(value = "/{contactId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public ContactResponse getContact(@PathVariable("contactId") Long contactId) {
    Contact contact = contactService.get(contactId);
    ContactResponse contactResponse = mapper.fromContactToContactResponse(contact);
    toGpsAddress(contactResponse,contact);
    return contactResponse;
  }

  @Operation(summary = "Get contact list")
  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public ResponseEntity getContactList(
      Pageable pageable, @RequestParam(value = "id", required = false) Set<Long> ids,
      @RequestParam(value = "companyId", required = false) Long companyId,
      @RequestParam(value = "stakeholder", required = false) Boolean isStakeholder,
      @RequestParam(value = "view", required = false, defaultValue = "summary") String view
  ) {
    if (ids != null && !ids.isEmpty() && companyId != null) {
      throw new SalesException(SalesErrorCodes.BAD_REQUEST);
    }
    if (ids != null && !ids.isEmpty()) {
      List<Contact> contacts = contactService.getContactsByIds(ids);

      return "full".equalsIgnoreCase(view) ?
          new ResponseEntity(contacts.stream()
              .map(contact -> mapper.fromContactToContactResponse(contact))
              .collect(Collectors.toList()), HttpStatus.OK)
          : new ResponseEntity(mapper.toContactSummary(contacts), HttpStatus.OK);
    }
    if (companyId != null) {
      return new ResponseEntity(contactService.getContactsByCompany(companyId, isStakeholder, pageable), HttpStatus.OK);
    }
    return new ResponseEntity(
        (contactService
            .getList(pageable)
            .map(contact -> mapper.fromContactToContactResponse(contact))),
        HttpStatus.OK);
  }

  @Operation(summary = "Update contact")
  @PutMapping(
      value = "/{contactId}",
      produces = MediaType.APPLICATION_JSON_VALUE,
      consumes = MediaType.APPLICATION_JSON_VALUE)
  public ContactResponse updateContact(
      @PathVariable("contactId") Long contactId,
      @Valid @RequestBody ContactUpdateRequest updateRequest) {
    Contact contact = mapper.fromContactUpdateRequestToContact(updateRequest);
    contact.setId(contactId);
    contact.addContactUtmValues(populateUpdateContactUtmValues(updateRequest));
    populateGpsAddress(updateRequest.getAddressCoordinate(),contact);
    Contact updateContact = contactService.update(contact, null);
    ContactResponse contactResponse = mapper.fromContactToContactResponse(updateContact);
    toGpsAddress(contactResponse,updateContact);
    return contactResponse;
  }

  private Set<ContactUtm> populateUpdateContactUtmValues(ContactUpdateRequest updateRequest) {
    Set<ContactUtm> contactUtms = new HashSet<>();
    if (updateRequest.getSubSource() != null || updateRequest.getUtmSource() != null || updateRequest.getUtmContent() != null
        || updateRequest.getUtmCampaign() != null ||
        updateRequest.getUtmTerm() != null || updateRequest.getUtmMedium() != null) {
      contactUtms.add(new ContactUtm(updateRequest.getSubSource(), updateRequest.getUtmSource(),
          updateRequest.getUtmMedium(), updateRequest.getUtmCampaign(),
          updateRequest.getUtmTerm(), updateRequest.getUtmContent(), userFacade.getUserId(), userFacade.getUserId()));
    }
    return contactUtms;
  }

  @Operation(summary = "Delete contact")
  @DeleteMapping(value = "/{contactId}")
  public void deleteContact(
      @PathVariable("contactId") Long contactId,
      @RequestParam(defaultValue = "false") boolean publishUsage) {
    contactService.deleteContact(contactId, publishUsage);
  }

  @Operation(summary = "Update contact owner")
  @PutMapping(
      value = "/{id}/owner",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ChangeOwnerResponse<Long> updateOwner(
      @PathVariable(value = "id") Long contactId, @Valid @RequestBody UpdateOwnerRequest request) {
    CheckedFunction<Long, Long, ChangeOwnerResponse<Long>> updateOwner =
        (o, e, ce) -> contactService.updateEntityOwner(o, e, ce);
    return (wrapException(
        request.getOwnerId(), contactId, request.getChildEntities(), updateOwner));
  }

  @GetMapping(
      value = "/duplicates/{uniquenessStrategyField}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public boolean validateDuplicates(EntityUniquenessStrategy uniquenessStrategy) {
    return contactService.validateDuplicates(uniquenessStrategy);
  }

  @GetMapping(value = "/has-duplicates", produces = MediaType.APPLICATION_JSON_VALUE)
  public DuplicateCheckResponse hasDuplicates(@RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "fieldName") String fieldName,
      @RequestParam(value = "value", required = false) String value,
      @RequestParam(value = "phoneNumberId", required = false) Long phoneNumberId) {
    return contactService.hasDuplicateContacts(id, fieldName, value, phoneNumberId);
  }

  @GetMapping(value = "/layout", produces = APPLICATION_JSON_VALUE)
  public ResponseEntity<Layout> getLayoutByView(
      @RequestParam(value = "view") String viewName,
      @RequestParam(value = "mode", required = false, defaultValue = "new") String mode) {
    return ResponseEntity.ok(contactService.getLayoutForView(viewName, mode));
  }

  @GetMapping(value = "/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<Page<LookUp>> lookUpContacts(@RequestParam(value = "view") String view, @RequestParam("q") String queryParam) {
    String[] params = queryParam.split(":");
    if (LEAD_CONVERSION.equalsIgnoreCase(view) && isNotEmpty(params) && "firstName".equalsIgnoreCase(params[0])) {
      String key = params.length > 1 ? params[1] : "";
      return ResponseEntity.ok(contactService.lookUpUpdatableContacts(key));
    }
    return ResponseEntity.ok(new PageImpl<>(emptyList()));
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.CONTACT;
  }

  @Operation(description = "Patch Contact")
  @PatchMapping(
      value = "/{id}",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<ContactSummary> patchContact(
      @PathVariable("id") long contactId,
      @Valid @RequestBody ContactPatchRequest contactPatchRequest) {
    Contact contact = contactService.patch(contactId, contactPatchRequest.getOperation(), contactPatchRequest.getContact());
    return ResponseEntity
        .ok(new ContactSummary(contact.getId(), contact.getFirstName(), contact.getLastName()));
  }

  @Operation(summary = "Delete contact picklist value")
  @DeleteMapping(
      value = "/picklists/{picklistName}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public void deletePicklistValue(@PathVariable("picklistName") String picklistName, @RequestParam(name = "id") Long id,
      @RequestParam(name = "value") String value) {
    picklistValueService.deletePicklistValue(picklistName, com.sell.sales.dto.EntityType.CONTACT, id, value);
  }

  @Operation(
      summary = "Imports a given contact into the system depending on the provided duplication strategy", hidden = true)
  @PostMapping(
      value = "/import",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<?> importContact(@Valid @RequestBody ContactImportRequestData contactImportRequestData) {

    return ResponseEntity.ok(contactImportService.importContact(contactImportRequestData));
  }

  private Set<ContactUtm> populateCreateContactUtmValues(ContactCreateRequest contactCreateRequest) {
    Set<ContactUtm> contactUtms = new HashSet<>(emptySet());
    if (contactCreateRequest.getSubSource() != null || contactCreateRequest.getUtmSource() != null || contactCreateRequest.getUtmContent() != null
        || contactCreateRequest.getUtmCampaign() != null ||
        contactCreateRequest.getUtmTerm() != null || contactCreateRequest.getUtmMedium() != null) {
      contactUtms.clear();
      contactUtms.add(new ContactUtm(contactCreateRequest.getSubSource(), contactCreateRequest.getUtmSource(),
          contactCreateRequest.getUtmMedium(), contactCreateRequest.getUtmCampaign(),
          contactCreateRequest.getUtmTerm(), contactCreateRequest.getUtmContent(), userFacade.getUserId(), userFacade.getUserId()));
    }
    return contactUtms;
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Object> handleUserMethodFieldErrors(MethodArgumentNotValidException ex, WebRequest request) {
    log.warn(LogMarker.INVALID_METHOD_ARG, ex.getMessage());
    HttpHeaders headers = this.getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getCode(),
        this.applicationContext.getMessage(SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getMessage(), (Object[]) null, locale));
    List<FieldErrorResource> fieldErrors = ex.getBindingResult().getFieldErrors().stream()
        .map((e) -> new FieldErrorResource(e.getField(), e.getDefaultMessage())).collect(Collectors.toList());
    error.setFieldErrors(fieldErrors);
    return this.handleExceptionInternal(ex, error, headers, HttpStatus.BAD_REQUEST, request);
  }

  protected HttpHeaders getRequiredHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    return headers;
  }

  protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatus status, WebRequest request) {
    if (HttpStatus.INTERNAL_SERVER_ERROR.equals(status)) {
      request.setAttribute("javax.servlet.error.exception", ex, 0);
    }
    return new ResponseEntity(body, headers, status);
  }

  private void populateGpsAddress(GPSCoordinate gpsCoordinate, Contact contact) {
    if(gpsCoordinate==null){
      return;
    }
      ContactGPSAddress gpsAddress = ContactGPSAddress.create(gpsCoordinate, contact);
      contact.setContactGPSAddress(gpsAddress);
  }

  private void toGpsAddress(ContactResponse contactResponse, Contact contact) {
    if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(contact.getContactGPSAddress())){
      ContactGPSAddress gpsAddress = contact.getContactGPSAddress();
      contactResponse.setAddressCoordinate(gpsAddress.getAddressCoordinate());
    }
  }

}
