package com.sell.sales.controller;

import static java.util.Collections.emptySet;

import com.github.fge.jsonpatch.JsonPatch;
import com.sell.sales.annotation.LockRetry;
import com.sell.sales.controller.request.UpdateOwnerRequest;
import com.sell.sales.controller.request.lead.DuplicateRecordDTO;
import com.sell.sales.controller.request.lead.ImportStrategy;
import com.sell.sales.controller.request.lead.ImportStrategyType;
import com.sell.sales.controller.request.lead.LeadConversionRequest;
import com.sell.sales.controller.request.lead.LeadImportNameToIdRequest;
import com.sell.sales.controller.request.lead.LeadImportRequestData;
import com.sell.sales.controller.request.lead.LeadPatchRequest;
import com.sell.sales.controller.request.lead.LeadRequest;
import com.sell.sales.controller.request.lead.LeadUpdateRequest;
import com.sell.sales.controller.request.lead.Pipeline;
import com.sell.sales.controller.request.lead.PipelineStage;
import com.sell.sales.controller.response.DuplicateCheckResponse;
import com.sell.sales.controller.response.lead.LeadConversionResponse;
import com.sell.sales.controller.response.lead.LeadCount;
import com.sell.sales.controller.response.lead.LeadProductResponse;
import com.sell.sales.controller.response.lead.LeadResponse;
import com.sell.sales.controller.response.lead.LeadSummary;
import com.sell.sales.converter.LeadMapper;
import com.sell.sales.core.annotation.ApiPageable;
import com.sell.sales.core.client.InternalShareRuleService;
import com.sell.sales.core.client.SharableEntityController;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.ErrorResource;
import com.sell.sales.core.domain.FieldErrorResource;
import com.sell.sales.core.utils.LogMarker;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.LeadGPSAddress;
import com.sell.sales.domain.LeadUtm;
import com.sell.sales.entity.dto.ChangeOwnerResponse;
import com.sell.sales.entity.model.EntityUniquenessStrategy;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.PicklistValueService;
import com.sell.sales.service.lead.LeadService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

@RestController
@Slf4j
@RequestMapping("/v1/leads")
public class LeadController extends SharableEntityController {

  @Autowired
  ApplicationContext applicationContext;
  @Autowired
  private LeadService leadService;
  @Autowired
  private LeadMapper leadMapper;
  @Autowired
  private PicklistValueService picklistValueService;
  @Autowired
  private UserFacade userFacade;

  protected LeadController(InternalShareRuleService client) {
    super(client);
  }

  @PostMapping(
      produces = MediaType.APPLICATION_JSON_VALUE,
      consumes = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Create lead", extensions = {
      @Extension(name = "access-policy", properties = {
          @ExtensionProperty(name = "action", value = "create"),
          @ExtensionProperty(name = "policy", value = "records"),
          @ExtensionProperty(name = "resource", value = "lead")
      })
  })
  public LeadResponse createLead(@Valid @RequestBody LeadRequest leadRequest) {
    Lead lead = leadMapper.fromLeadRequest(leadRequest);
    populatePipeline(lead,leadRequest.getPipeline());
    lead.addLeadUtmValues(populateCreateLeadUtmValues(leadRequest));
    populateGpsAddress(leadRequest, lead);
    Lead lead1 = leadService.create(lead);
    if(!ObjectUtils.isEmpty(lead1.getLeadUtms())){
      lead1.addLeadUtmValues(Collections.singleton(lead.getLeadUtms().stream().findFirst().get()));
    }
    LeadResponse leadResponse = leadMapper.toLeadResponse(lead1);
    Pipeline pipeline = toPipeline(lead1);
    leadResponse.setPipeline(pipeline);
    toGpsAddress(leadResponse,lead);
    return leadResponse;
  }

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public Page<LeadResponse> getLeads(Pageable pageable) {
    return (leadService.getList(pageable).map(lead -> leadMapper.toLeadResponse(lead)));
  }

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE, params = "productId")
  @ApiPageable
  public LeadProductResponse getLeadIdsByProduct(@RequestParam("productId") Long productId) {
    return leadService.getLeadIdsAttachedToProduct(productId);
  }

  @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  public LeadResponse getLead(@PathVariable("id") long leadId) {
    Lead lead = leadService.get(leadId);
    if(!ObjectUtils.isEmpty(lead.getLeadUtms())){
      lead.addLeadUtmValues(Collections.singleton(lead.getLeadUtms().stream().findFirst().get()));
    }
    LeadResponse leadResponse = leadMapper.toLeadResponse(lead);
    Pipeline pipeline = toPipeline(lead);
    leadResponse.setPipeline(pipeline);
    toGpsAddress(leadResponse,lead);
    return leadResponse;
  }

  private void toGpsAddress(LeadResponse leadResponse, Lead lead) {
    if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(lead.getLeadGPSAddress())){
      LeadGPSAddress gpsAddress = lead.getLeadGPSAddress();
      leadResponse.setAddressCoordinate(gpsAddress.getAddressCoordinate());
      leadResponse.setCompanyAddressCoordinate(gpsAddress.getCompanyAddressCoordinate());
    }
  }

  @PutMapping(
      value = "/{id}",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public LeadResponse updateLeadById(
      @PathVariable("id") long leadId, @Valid @RequestBody LeadUpdateRequest leadUpdateRequest) {
    Lead requestLead = leadMapper.fromLeadRequest(leadUpdateRequest);
    requestLead.addLeadUtmValues(populateCreateLeadUtmValues(leadUpdateRequest));
    populatePipeline(requestLead, leadUpdateRequest.getPipeline());
    populateGpsAddress(leadUpdateRequest,requestLead);
    requestLead.setId(leadId);
    Lead update = leadService.update(requestLead);
    if(!ObjectUtils.isEmpty(update.getLeadUtms())){
      update.addLeadUtmValues(Collections.singleton(update.getLeadUtms().stream().findFirst().get()));
    }
    LeadResponse leadResponse = leadMapper.toLeadResponse(update);
    leadResponse.setPipeline(toPipeline(update));
    toGpsAddress(leadResponse, update);
    return leadResponse;
  }
  
  @Operation(summary = "Update lead owner")
  @PutMapping(
      value = "/{id}/owner",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @LockRetry
  public ChangeOwnerResponse<Long> updateOwner(
      @PathVariable(value = "id") Long leadId, @RequestBody UpdateOwnerRequest request) {
      return leadService.updateEntityOwner(request.getOwnerId(), leadId, request.getChildEntities());
  }

  @GetMapping(value = "/{id}/duplicates", produces = MediaType.APPLICATION_JSON_VALUE)
  public DuplicateRecordDTO getDuplicateRecords(@PathVariable("id") Long leadId) {
    return leadService.getDuplicateRecords(leadId);
  }

  @GetMapping(value = "/{id}/has-duplicates", produces = MediaType.APPLICATION_JSON_VALUE)
  public DuplicateRecordDTO hasDuplicate(@PathVariable("id") Long leadId) {
    return leadService.hasDuplicateLead(leadId);
  }

  @GetMapping(
      value = "/duplicates/{uniquenessStrategyField}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public boolean validateDuplicates(@PathVariable("uniquenessStrategyField") EntityUniquenessStrategy entityUniquenessStrategy) {
    return leadService.validateDuplicates(entityUniquenessStrategy);
  }

  @GetMapping(value = "/has-duplicates", produces = MediaType.APPLICATION_JSON_VALUE)
  public DuplicateCheckResponse hasDuplicates(@RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "fieldName") String fieldName,
      @RequestParam(value = "value", required = false) String value,
      @RequestParam(value = "phoneNumberId", required = false) Long phoneNumberId) {
    return leadService.hasDuplicateLeads(id, fieldName, value, phoneNumberId);
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.LEAD;
  }

  @Operation(
      summary = "Imports a given lead into the system depending on the provided duplication strategy")
  @PostMapping(
      value = "/import",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<?> importLead(@Valid @RequestBody LeadImportRequestData leadImportRequestData) {
    Lead leadToImport = leadMapper.fromLeadRequest(leadImportRequestData.getLeadToImport());
    LeadRequest leadRequest = new LeadRequest();
    setImportFields(leadRequest, leadImportRequestData, leadToImport);
    log.debug("Lead import job id:{}", leadImportRequestData.getJobId());
    return ResponseEntity.ok(leadService.importLead(leadToImport, getImportStrategy(leadImportRequestData),
        getLeadImportNameToIdRequest(leadImportRequestData),
        leadImportRequestData.getLeadToImport().getOwnerEmail()));
  }

  @Operation(
      summary = "Converts a given lead")
  @PostMapping(
      value = "/{id}/convert",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<LeadConversionResponse> convertLead(@PathVariable("id") Long leadId,
      @RequestBody(required = false) LeadConversionRequest leadConversionRequest) {
    return ResponseEntity.ok(leadService.convertLead(leadId, leadConversionRequest));
  }

  @DeleteMapping(value = "/{id}")
  public void deleteLead(
      @PathVariable("id") long leadId,
      @RequestParam(defaultValue = "false") boolean publishUsage) {
    leadService.deleteLead(leadId, publishUsage);
  }

  @Operation(summary = "Patch Lead")
  @PatchMapping(
      value = "/bulk-patch/{id}",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<LeadSummary> patchLead(
      @PathVariable("id") long leadId,
      @Valid @RequestBody LeadPatchRequest leadPatchRequest) {
    return ResponseEntity.ok(new LeadSummary(leadService.patch(leadId, leadPatchRequest.getOperation(), leadPatchRequest.getLead()).getId()));
  }

  @Operation(summary = "Patch Lead Partial Update Using Json Patch")
  @PatchMapping(
      value = "/{id}",
      consumes = "application/json-patch+json",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<LeadSummary> patchLeadById(
      @PathVariable("id") long leadId, @RequestBody JsonPatch jsonPatch) {
    return ResponseEntity.ok(new LeadSummary(leadService.patchLeadById(leadId, jsonPatch).getId()));
  }

  @Operation(summary = "Delete lead picklist value")
  @DeleteMapping(
      value = "/picklists/{picklistName}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public void deletePicklistValue(@PathVariable("picklistName") String picklistName, @RequestParam(name = "id") Long id,
      @RequestParam(name = "value") String value) {
    picklistValueService.deletePicklistValue(picklistName, com.sell.sales.dto.EntityType.LEAD, id, value);
  }

  @Operation(summary = "Get lead count by ownerId")
  @GetMapping(
      value = "/count-by-owner",
      produces = MediaType.APPLICATION_JSON_VALUE)
  public List<LeadCount> getCountByOwner(@RequestParam(name = "ownerId",required = true) @NotEmpty List<Long> ownerId,
      @RequestParam("fromDate") @DateTimeFormat(iso = ISO.DATE_TIME) Date fromDate,
      @RequestParam("toDate") @DateTimeFormat(iso = ISO.DATE_TIME) Date toDate) {
    return leadService.getCountByOwner(ownerId,fromDate ,toDate);
  }

  private LeadImportNameToIdRequest getLeadImportNameToIdRequest(LeadImportRequestData leadImportRequestData) {
    LeadImportNameToIdRequest leadImportNameToIdRequest = new LeadImportNameToIdRequest();
    leadImportNameToIdRequest.setProductName(leadImportRequestData.getLeadToImport().getProductName());
    leadImportNameToIdRequest.setPipeline(leadImportRequestData.getLeadToImport().getPipeline());
    leadImportNameToIdRequest.setCreatedByEmail(leadImportRequestData.getLeadToImport().getCreatedByEmail());
    leadImportNameToIdRequest.setUpdatedByEmail(leadImportRequestData.getLeadToImport().getUpdatedByEmail());

    return leadImportNameToIdRequest;
  }

  private void populatePipeline(Lead lead, Pipeline pipeline) {
    if (pipeline != null && pipeline.getId() != null) {
      lead.setPipeline(pipeline.getId());
      PipelineStage stage = pipeline.getStage();
      if (stage != null && stage.getId() != null) {
        lead.setPipelineStage(stage.getId());
      }
    }
  }

  private void populateGpsAddress(LeadRequest leadRequest, Lead lead) {
    LeadGPSAddress gpsAddress = LeadGPSAddress.create(leadRequest, lead);
    lead.setLeadGPSAddress(gpsAddress);
  }

  private Pipeline toPipeline(Lead lead) {
    if (lead.getPipeline() != null) {
      Map<String, Object> metaData = lead.getMetaData();
      Map<String, Map<Integer, String>> idNameStore =
          (Map<String, Map<Integer, String>>) metaData.get("idNameStore");
      String pipelineName = getNameFromMetadata("pipeline", lead.getPipeline(), idNameStore);
      PipelineStage stage = null;
      if (lead.getPipelineStage() != null) {
        String pipelineStageName = getNameFromMetadata("pipelineStage", lead.getPipelineStage(), idNameStore);
        stage = new PipelineStage(lead.getPipelineStage(), pipelineStageName);
      }
      Pipeline pipeline = new Pipeline(lead.getPipeline(), pipelineName, stage);
      return pipeline;
    }
    return null;
  }
  private String getNameFromMetadata(String key, Object id,Map<String, Map<Integer, String>> idNameStore){
    if(id != null){
      return idNameStore.containsKey(key) ? idNameStore.get(key).get(id.toString()) : null;
    }
    return null;
  }

  private Set<LeadUtm> populateCreateLeadUtmValues(LeadRequest leadRequest) {
    Set<LeadUtm> leadUtms = new HashSet<>(emptySet());
    if (leadRequest.getSubSource() != null || leadRequest.getUtmSource() != null || leadRequest.getUtmContent() != null
        || leadRequest.getUtmCampaign() != null ||
        leadRequest.getUtmTerm() != null || leadRequest.getUtmMedium() != null) {
      leadUtms.clear();
      leadUtms.add(new LeadUtm(leadRequest.getSubSource(), leadRequest.getUtmSource(),
          leadRequest.getUtmMedium(), leadRequest.getUtmCampaign(),
          leadRequest.getUtmTerm(), leadRequest.getUtmContent(), userFacade.getUserId(), userFacade.getUserId()));
    }
    return leadUtms;
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Object> handleUserMethodFieldErrors(MethodArgumentNotValidException ex, WebRequest request) {
    log.warn(LogMarker.INVALID_METHOD_ARG, ex.getMessage());
    HttpHeaders headers = this.getRequiredHeaders();
    Locale locale = LocaleContextHolder.getLocale();
    ErrorResource error = new ErrorResource(SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getCode(),
        this.applicationContext.getMessage(SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getMessage(), (Object[]) null, locale));
    List<FieldErrorResource> fieldErrors = ex.getBindingResult().getFieldErrors().stream()
        .map((e) -> new FieldErrorResource(e.getField(), e.getDefaultMessage())).collect(Collectors.toList());
    error.setFieldErrors(fieldErrors);
    return this.handleExceptionInternal(ex, error, headers, HttpStatus.BAD_REQUEST, request);
  }

  protected HttpHeaders getRequiredHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    return headers;
  }

  protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatus status, WebRequest request) {
    if (HttpStatus.INTERNAL_SERVER_ERROR.equals(status)) {
      request.setAttribute("javax.servlet.error.exception", ex, 0);
    }
    return new ResponseEntity(body, headers, status);
  }

  protected void setImportFields(LeadRequest leadRequest, LeadImportRequestData leadImportRequestData,
      Lead leadToImport) {
    leadRequest.setSubSource(leadImportRequestData.getLeadToImport().getSubSource());
    leadRequest.setUtmSource(leadImportRequestData.getLeadToImport().getUtmSource());
    leadRequest.setUtmMedium(leadImportRequestData.getLeadToImport().getUtmMedium());
    leadRequest.setUtmCampaign(leadImportRequestData.getLeadToImport().getUtmCampaign());
    leadRequest.setUtmContent(leadImportRequestData.getLeadToImport().getUtmContent());
    leadRequest.setUtmTerm(leadImportRequestData.getLeadToImport().getUtmTerm());
    leadToImport.addLeadUtmValues(populateCreateLeadUtmValues(leadRequest));
    leadToImport.setRequestFromImport(true);
    leadToImport.setImportJobId(leadImportRequestData.getJobId());
    leadToImport.setCreatedAt(leadImportRequestData.getLeadToImport().getCreatedAt());
    leadToImport.setUpdatedAt(leadImportRequestData.getLeadToImport().getUpdatedAt());
    leadToImport.setErrorInImport(leadImportRequestData.getErrorInImport());
  }

  protected ImportStrategy getImportStrategy(LeadImportRequestData leadImportRequestData) {
    return
        leadImportRequestData.getImportStrategy() == null
            ? new ImportStrategy(ImportStrategyType.NONE)
            : leadImportRequestData.getImportStrategy();
  }
}
