package com.sell.sales.controller;

import com.sell.sales.controller.request.pipeline.CreatePipelineRequest;
import com.sell.sales.controller.request.pipeline.CreatePipelineStageRequest;
import com.sell.sales.controller.request.pipeline.UpdatePipelineRequest;
import com.sell.sales.controller.request.pipeline.UpdatePipelineStageRequest;
import com.sell.sales.controller.response.pipeline.PipelineResponse;
import com.sell.sales.controller.response.pipeline.PipelineStageResponse;
import com.sell.sales.core.annotation.ApiPageable;
import com.sell.sales.core.client.InternalShareRuleService;
import com.sell.sales.core.client.SharableEntityController;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.search.controller.SearchController;
import com.sell.sales.search.dto.SearchRequest;
import com.sell.sales.search.dto.SearchResponse;
import com.sell.sales.service.PipelineService;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/v1/pipelines")
@Tag(name = "Pipelines", description = "APIs for creating & managing pipelines")
public class PipelineController extends SharableEntityController implements SearchController {

  @Autowired private PipelineService pipelineService;

  protected PipelineController(InternalShareRuleService client) {
    super(client);
  }

  @Operation(summary = "Create a pipeline")
  @PostMapping(
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PipelineResponse createPipeline(
      @Valid @RequestBody CreatePipelineRequest createPipelineRequest) {
    return (pipelineService.createPipeline(createPipelineRequest));
  }

  @Operation(summary = "Get all available (active & inactive) pipelines")
  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiPageable
  public Page<PipelineResponse> getPipelines(
      @PageableDefault(sort = {"name"}) Pageable pageable,
      @NotNull @RequestParam("entityType") String entityType) {
    return (pipelineService.getPipelines(entityType, pageable));
  }

  @Operation(summary = "Get a pipeline")
  @GetMapping(value = "/{pipelineId}", produces = MediaType.APPLICATION_JSON_VALUE)
  public PipelineResponse getPipeline(@PathVariable("pipelineId") Long pipelineId) {
    return (pipelineService.getPipeline(pipelineId));
  }

  @Operation(summary = "Update a pipeline")
  @PutMapping(
      value = "/{pipelineId}",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public PipelineResponse updatePipeline(
      @PathVariable("pipelineId") Long pipelineId,
      @Valid @RequestBody UpdatePipelineRequest updatePipelineRequest) {
    return (pipelineService.updatePipeline(pipelineId, updatePipelineRequest));
  }

  @Operation(summary = "Activates the pipeline")
  @PostMapping(value = "{pipelineId}/activate", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<PipelineResponse> activate(@PathVariable long pipelineId) {
    return ResponseEntity.ok(pipelineService.activate(pipelineId));
  }

  @Operation(summary = "Deactivates the pipeline")
  @PostMapping(value = "{pipelineId}/deactivate", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<PipelineResponse> deactivate(@PathVariable long pipelineId) {
    return ResponseEntity.ok(pipelineService.deactivate(pipelineId));
  }

  @Operation(summary = "Delete a pipeline")
  @DeleteMapping(value = "/{pipelineId}")
  public void deletePipeline(@PathVariable(value = "pipelineId") Long pipelineId) {
    pipelineService.deletePipeline(pipelineId);
  }

  @Operation(summary = "Create a pipeline stage in a pipeline")
  @PostMapping(
      value = "/{pipelineId}/stages",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Hidden
  @Deprecated
  public PipelineStageResponse createPipelineStage(
      @PathVariable("pipelineId") Long pipelineId,
      @Valid @RequestBody CreatePipelineStageRequest createPipelineStageRequest) {
    return (pipelineService.createPipelineStage(pipelineId, createPipelineStageRequest));
  }

  @Operation(summary = "Get all available (active & inactive) pipeline stages in a pipeline")
  @GetMapping(value = "/{pipelineId}/stages", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<PipelineStageResponse> getPipelineStages(
      @PathVariable("pipelineId") Long pipelineId) {
    return (pipelineService.getPipelineStages(pipelineId));
  }

  @Operation(summary = "Get all available  pipeline stage reasons in a pipeline")
  @GetMapping(value = "/{pipelineId}/pipelineStageReasons", produces = MediaType.APPLICATION_JSON_VALUE)
  public List<String> getPipelineStageReasons(
      @PathVariable("pipelineId") Long pipelineId) {
    return (pipelineService.getPipelineStageReasons(pipelineId));
  }

  @Operation(summary = "Get a pipeline stage in a pipeline")
  @GetMapping(
      value = "/{pipelineId}/stages/{pipelineStageId}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Hidden
  @Deprecated
  public PipelineStageResponse getPipelineStage(
      @PathVariable("pipelineId") Long pipelineId,
      @PathVariable("pipelineStageId") Long pipelineStageId) {
    return (pipelineService.getPipelineStage(pipelineId, pipelineStageId));
  }

  @Operation(summary = "Update a pipeline stage in a pipeline")
  @PutMapping(
      value = "/{pipelineId}/stages/{pipelineStageId}",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Hidden
  @Deprecated
  public PipelineStageResponse updatePipelineStage(
      @PathVariable("pipelineId") Long pipelineId,
      @PathVariable("pipelineStageId") Long pipelineStageId,
      @Valid @RequestBody UpdatePipelineStageRequest updatePipelineStageRequest) {
    return (pipelineService.updatePipelineStage(
        pipelineId, pipelineStageId, updatePipelineStageRequest));
  }

  @Operation(summary = "Delete a pipeline stage in a pipeline")
  @DeleteMapping(value = "/{pipelineId}/stages/{pipelineStageId}")
  @Hidden
  @Deprecated
  public void deletePipelineStage(
      @PathVariable(value = "pipelineId") Long pipelineId,
      @PathVariable(value = "pipelineStageId") Long pipelineStageId) {
    pipelineService.deletePipelineStage(pipelineId, pipelineStageId);
  }

  @Operation(summary = "Get a pipeline by name", hidden = true)
  @GetMapping(value = "/name", produces = MediaType.APPLICATION_JSON_VALUE)
  public PipelineResponse getPipeline(@NotNull @RequestParam("entityType") EntityType entityType, @RequestParam("pipelineName") String pipelineName) {
    return pipelineService.getPipelineByName(entityType, pipelineName);
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.PIPELINE;
  }

  @Operation(summary = "lookup for pipeline")
  @GetMapping(value = "/lookup", produces = MediaType.APPLICATION_JSON_VALUE)
  public Page<SearchResponse> getLookupResults(
      Pageable pageable,
      @RequestParam("q") String fieldAndValue,
      @NotNull @RequestParam("entityType") EntityType entityType) {
    return pipelineService.lookupPipelines(fieldAndValue, entityType, getPageRequestForLookup(getQueryParamValue(fieldAndValue)));
  }

  @Operation(summary = "This is utility and can only be called on from data migration API")
  @Hidden
  @PostMapping(value = "/{entityType}/trim-reason-space", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity trimPipelineReasonSpace(
      @PathVariable("entityType") EntityType entityType,@RequestParam(value = "tenantId") long tenantId) {
    return ResponseEntity.ok(pipelineService.trimPipelineReason(tenantId,entityType));
  }
  @Override
  public Page<SearchResponse> getResults(
      Pageable pageable, @Valid @RequestBody SearchRequest searchRequest) {
    return pipelineService.getResults(pageable, searchRequest);
  }

  private String getQueryParamValue(String queryParam) {
    if (!queryParam.contains(":")) {
      throw new SalesException(SalesErrorCodes.BAD_REQUEST);
    }
    String[] split = queryParam.split(":");
    return split.length == 2 ? split[1] : "";
  }

  private Pageable getPageRequestForLookup(String value){
    Sort sortOnUpdatedAtDesc = new Sort(Direction.DESC, "updatedAt");
    return new PageRequest(0, ("".equalsIgnoreCase(value) ? 10 : 25), sortOnUpdatedAtDesc);
  }
}
