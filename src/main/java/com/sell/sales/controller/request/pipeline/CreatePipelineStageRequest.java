package com.sell.sales.controller.request.pipeline;

import com.sell.sales.domain.ForecastingType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePipelineStageRequest {

  @Schema(required = true, example = "Site visit", description = "Unique name for this stage")
  @NotBlank
  @Size(max = InputSize.MAX_INPUT_SIZE_NAME, message = "Name must be max " + InputSize.MAX_INPUT_SIZE_NAME + " characters")
  private String name;

  @Schema(required = false, example = "Site visit stage", description = "Description for this stage")
  @Size(min = 0, max = InputSize.MAX_INPUT_SIZE_DESCRIPTION, message = "Description must be less than " + InputSize.MAX_INPUT_SIZE_DESCRIPTION
      + " characters")
  private String description;

  @Schema(required = true, example = "OPEN", description = "Stage type")
  @NotNull
  private ForecastingType forecastingType;

  @Schema(required = false, example = "1", description = "Position in the attached pipeline")
  @Min(value = 1, message = "Please select a positive number")
  private Integer position;

  @Schema(required = true, example = "50", description = "Win likelihood percentage between 0-100%")
  @NotNull
  @Range(min = 0, max = 100, message = "Please select a number between 0 & 100 (both inclusive)")
  private Integer winLikelihood;
}
