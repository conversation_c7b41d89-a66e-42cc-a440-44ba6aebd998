package com.sell.sales.controller.request;

import com.sell.sales.core.domain.EntityType;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpdateOwnerListRequest {

  @NotNull
  private Long ownerId;

  @NotEmpty
  private List<Long> entityIds;

  private List<EntityType> childEntities;

}
