package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.domain.LeadCompanyPhoneNumber;
import com.sell.sales.domain.LeadPhoneNumber;
import com.sell.sales.entity.model.Email;
import com.sell.sales.entity.model.PhoneNumber;
import com.sell.sales.utils.EmptyStringToNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

@Getter
@Setter
public class LeadImportRequest extends LeadBaseRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private Pipeline pipeline;
  private String productName;
  private String ownerEmail;
  private String createdByEmail;
  private String updatedByEmail;
  private Email[] emails;
  private PhoneNumber[] phoneNumbers;
  private PhoneNumber[] companyPhones;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String subSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmMedium;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmCampaign;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmTerm;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmContent;
  private Date createdAt;
  private Date updatedAt;
  private Long createdBy;
  private Long updatedBy;

  public Set<LeadPhoneNumber> getLeadPhoneNumbers(PhoneNumber[] phoneNumbers){
    if (ObjectUtils.isEmpty(phoneNumbers)) {
      return Collections.emptySet();
    }
    return Arrays.stream(phoneNumbers).map(phoneNumber -> {
      LeadPhoneNumber leadPhoneNumber = new LeadPhoneNumber(phoneNumber.getType(), phoneNumber.getCode(),
          phoneNumber.getValue(), phoneNumber.getDialCode(), null, phoneNumber.isPrimary());
      return leadPhoneNumber;
    }).collect(Collectors.toSet());

  }

  public Set<LeadCompanyPhoneNumber> getLeadCompanyPhoneNumbers(PhoneNumber[] phoneNumbers){
    if(ObjectUtils.isEmpty(phoneNumbers))
      return null;
    return Arrays.stream(phoneNumbers).map(phoneNumber -> {
      LeadCompanyPhoneNumber leadCompanyPhoneNumber = new LeadCompanyPhoneNumber(phoneNumber.getType(), phoneNumber.getCode(),
          phoneNumber.getValue(), phoneNumber.getDialCode(), null, phoneNumber.isPrimary());
      return leadCompanyPhoneNumber;
    }).collect(Collectors.toSet());
  }
}
