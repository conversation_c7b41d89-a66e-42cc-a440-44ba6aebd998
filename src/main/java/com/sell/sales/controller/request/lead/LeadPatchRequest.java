package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.domain.Operation;
import javax.validation.Valid;
import lombok.Getter;

@Getter
public class LeadPatchRequest {

  @Valid
  private LeadUpdateRequestV2 lead;
  private Operation operation;

  @JsonCreator
  public LeadPatchRequest(
      @JsonProperty("lead") LeadUpdateRequestV2 lead,
      @JsonProperty("executeWorkflow") boolean executeWorkflow,
      @JsonProperty("sendNotification") boolean sendNotification,
      @JsonProperty("executeScoreRule") boolean executeScoreRule
  ) {
    this.lead = lead;
    this.operation = new Operation(executeWorkflow, sendNotification, executeScoreRule);
  }
}
