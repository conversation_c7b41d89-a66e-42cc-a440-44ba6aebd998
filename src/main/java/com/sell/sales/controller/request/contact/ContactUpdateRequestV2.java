package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.request.IdName;
import com.sell.sales.controller.request.lead.patch.Email;
import com.sell.sales.controller.request.lead.patch.PhoneNumber;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContactUpdateRequestV2 extends ContactBaseRequest implements Serializable {

  private static final long serialVersionUID = 1L;
  private Date actualClosureDate;
  private IdName ownerId;
  @JsonProperty("emails")
  private Email email;
  @JsonProperty("phoneNumbers")
  private PhoneNumber phoneNumber;
  @JsonProperty("subSource")
  private String subSource;
  @JsonProperty("utmSource")
  private String utmSource;
  @JsonProperty("utmMedium")
  private String utmMedium;
  @JsonProperty("utmCampaign")
  private String utmCampaign;
  @JsonProperty("utmTerm")
  private String utmTerm;
  @JsonProperty("utmContent")
  private String utmContent;
  @JsonProperty("score")
  private Double score;
}
