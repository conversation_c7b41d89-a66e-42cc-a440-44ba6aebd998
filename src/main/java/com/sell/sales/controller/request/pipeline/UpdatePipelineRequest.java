package com.sell.sales.controller.request.pipeline;

import com.sell.sales.core.constants.InputSize;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.domain.PipelineStage;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class UpdatePipelineRequest {

  @Schema(required = false, example = "LEAD", allowableValues = "LEAD, DEAL",
      description = "Entity type applicable for the list")
  private EntityType entityType;

  @Schema(required = false, example = "[\"Wrong number\", \"Did not pick phone\"]",
      description = "User entered values which to be used as picklist in pipeline execution")
  private String[] unqualifiedReasons;

  @Schema(required = false, example = "[\"Low budget\", \"Not interested\"]",
      description = "User entered values which to be used as picklist in pipeline execution")
  private String[] lostReasons;

  @Schema(required = false, example = "Baner Properties", description = "Unique name for this pipeline")
  @Size(max = InputSize.MAX_INPUT_SIZE, message = "Name must be max " + InputSize.MAX_INPUT_SIZE + " characters")
  private String name;

  private List<PipelineStage> stages;
}
