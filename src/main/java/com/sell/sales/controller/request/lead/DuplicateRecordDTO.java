package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.core.domain.ErrorResource;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by shashanks3 on 16/5/19.
 */
@Getter
@Setter
public class DuplicateRecordDTO {
  ErrorResource message;
  List<DuplicateEntityDTO> records;
  private Map<String, Object> metaData;
  @Getter(AccessLevel.NONE)
  private boolean hasDuplicate = false;

  @JsonProperty("hasDuplicate")
  public boolean hasDuplicate() {
    return hasDuplicate;
  }
}
