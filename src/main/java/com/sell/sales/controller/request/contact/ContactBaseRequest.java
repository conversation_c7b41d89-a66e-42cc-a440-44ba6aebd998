package com.sell.sales.controller.request.contact;

import com.sell.sales.controller.request.IdName;
import com.sell.sales.core.constants.InputSize;
import java.io.Serializable;
import java.util.Map;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContactBaseRequest implements Serializable {

  
  @Size(max = InputSize.MAX_INPUT_SIZE)
  private String firstName;

  
  @Size(max = InputSize.MAX_INPUT_SIZE)
  private String lastName;
  private Long salutation;
  private Map<String, Object> customFieldValues;
  private boolean dnd;
  private String timezone;
  private String address;
  private String city;
  private String state;
  private String zipcode;
  private String country;
  private String facebook;
  private String twitter;
  private String linkedin;

  //Professional
  private IdName company;
  private String designation;
  private String department;
  private boolean stakeholder;
  private Long campaign;
  private Long source;
}
