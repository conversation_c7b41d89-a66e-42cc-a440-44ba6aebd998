package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sell.sales.core.domain.Action;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DuplicateEntityDTO implements Serializable {

  private Long id;
  private String firstName;
  private String lastName;
  private Long ownerId;

  @JsonIgnoreProperties(value = {"write", "readAll", "updateAll"})
  private Action recordActions;
}
