package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.domain.Operation;
import javax.validation.Valid;
import lombok.Getter;

@Getter
public class ContactPatchRequest {

  @Valid
  private ContactUpdateRequestV2 contact;
  private Operation operation;

  @JsonCreator
  public ContactPatchRequest(
      @JsonProperty("contact") ContactUpdateRequestV2 contact,
      @JsonProperty("executeWorkflow") boolean executeWorkflow,
      @JsonProperty("sendNotification") boolean sendNotification,
      @JsonProperty("executeScoreRule") boolean executeScoreRule
  ) {
    this.contact = contact;
    this.operation = new Operation(executeWorkflow, sendNotification, executeScoreRule);
  }
}
