package com.sell.sales.controller.request.lead.patch;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class Email {
  private final FieldOperation operation;
  private final com.sell.sales.entity.model.Email[] emails;

  @JsonCreator
  public Email(@JsonProperty("operation") FieldOperation fieldOperation,
      @JsonProperty("values") com.sell.sales.entity.model.Email[] emails) {
    this.operation = fieldOperation;
    this.emails = emails;
  }
}
