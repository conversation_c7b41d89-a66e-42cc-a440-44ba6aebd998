package com.sell.sales.controller.request.pipeline;

import static com.sell.sales.core.constants.InputSize.*;

import com.sell.sales.core.constants.InputSize;
import com.sell.sales.core.domain.EntityType;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePipelineRequest {

  @Schema(required = true, example = "LEAD", allowableValues = "LEAD, DEAL", description = "Entity type applicable for the list")
  @NotNull
  private EntityType entityType;

  @Schema(required = true, example = "Baner Properties", description = "Unique name for this pipeline")
  @NotBlank
  @Size(max = MAX_INPUT_SIZE, message = "Name must be max " + MAX_INPUT_SIZE + " characters")
  private String name;
}
