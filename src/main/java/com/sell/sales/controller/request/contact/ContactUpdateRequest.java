package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.controller.request.lead.PhoneNumberRequest;
import com.sell.sales.core.constants.InputSize;
import com.sell.sales.domain.ContactPhoneNumber;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.entity.model.Email;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.utils.EmptyStringToNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContactUpdateRequest {

    @Size(max = InputSize.MAX_INPUT_SIZE)
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String firstName;
    @Size(max = InputSize.MAX_INPUT_SIZE)
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String lastName;
    private Long salutation;
    private Long ownerId;
    private PhoneNumberRequest[] phoneNumbers;
    private Email[] emails;
    private Map<String, Object> customFieldValues;
    private boolean dnd;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String timezone;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String address;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String city;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String state;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String zipcode;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String country;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String facebook;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String twitter;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String linkedin;
    private GPSCoordinate addressCoordinate;
    //Professional
    private Long company;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String designation;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String department;
    private boolean stakeholder;
    //Campaign Information
    private Long campaign;
    private Long source;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String subSource;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String utmSource;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String utmMedium;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String utmCampaign;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String utmTerm;
    @JsonDeserialize(using = EmptyStringToNull.class)
    private String utmContent;
    private Double score;

    public Set<ContactPhoneNumber> getContactPhoneNumbers() {
        if (ObjectUtils.isEmpty(this.phoneNumbers)) {
            return Collections.emptySet();
        }
        Set<PhoneNumberRequest> phoneNumberRequestSet = new HashSet<>(Arrays.asList(this.phoneNumbers));
        if (phoneNumberRequestSet.size() != this.phoneNumbers.length) {
            throw new SalesException(SalesErrorCodes.DUPLICATE_PHONES);
        }

        return Arrays.stream(this.phoneNumbers).map(phoneNumberRequest -> {
            ContactPhoneNumber contactPhoneNumber = new ContactPhoneNumber(phoneNumberRequest.getType(), phoneNumberRequest.getCode(),
                phoneNumberRequest.getValue(), phoneNumberRequest.getDialCode(), null, phoneNumberRequest.isPrimary());
            if (!ObjectUtils.isEmpty(phoneNumberRequest.getId())) {
                contactPhoneNumber.setId(phoneNumberRequest.getId());
            }
            return contactPhoneNumber;
        }).collect(Collectors.toSet());

    }

}
