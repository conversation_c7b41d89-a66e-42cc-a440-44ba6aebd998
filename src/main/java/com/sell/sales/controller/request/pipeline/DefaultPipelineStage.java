package com.sell.sales.controller.request.pipeline;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.domain.ForecastingType;
import lombok.Getter;

@Getter
public class DefaultPipelineStage {

  private final String name;
  private final String description;
  private final ForecastingType forecastingType;
  private final int position;
  private final int winLikelihood;
  private final boolean systemDefault;
  private final Integer systemDefaultPosition;

  @JsonCreator
  public DefaultPipelineStage(
      @JsonProperty("name") String name,
      @JsonProperty("description") String description,
      @JsonProperty("forecastingType") ForecastingType forecastingType,
      @JsonProperty("position") int position,
      @JsonProperty("winLikelihood") int winLikelihood,
      @JsonProperty("systemDefault") boolean systemDefault,
      @JsonProperty("systemDefaultPosition") Integer systemDefaultPosition) {
    this.name = name;
    this.description = description;
    this.forecastingType = forecastingType;
    this.position = position;
    this.winLikelihood = winLikelihood;
    this.systemDefault = systemDefault;
    this.systemDefaultPosition = systemDefaultPosition;
  }
}
