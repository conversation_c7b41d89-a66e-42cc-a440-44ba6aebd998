package com.sell.sales.controller.request.lead;

import static com.sell.sales.controller.request.lead.ConversionMode.EXISTING;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.controller.request.contact.ContactCreateRequest;
import com.sell.sales.core.domain.ErrorCodes;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.LeadCompanyPhoneNumber;
import com.sell.sales.domain.LeadPhoneNumber;
import com.sell.sales.exception.LeadConversionException;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

@Getter
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadConversionRequest {

  private ConversionRequest deal;
  private ContactConversionRequest contact;
  private ConversionRequest company;

  @JsonCreator
  public LeadConversionRequest(@JsonProperty("deal") ConversionRequest deal,
      @JsonProperty("contact") ContactConversionRequest contact,
      @JsonProperty("company") ConversionRequest company) {
    this.deal = deal;
    this.contact = contact;
    this.company = company;
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ConversionRequest {

    private final ConversionMode mode;
    private final HashMap<String, Object> details;

    @JsonCreator
    public ConversionRequest(@JsonProperty("mode") ConversionMode mode, @JsonProperty("details") HashMap<String, Object> details) {
      this.mode = mode;
      this.details = details;
    }

    public boolean isExisting() {
      return EXISTING.equals(mode);
    }

    public Long getExistingId() {
      return Long.valueOf(details.get("id").toString());
    }
  }

  @Getter
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ContactConversionRequest {

    private final ConversionMode mode;
    @Getter(AccessLevel.NONE)
    private final HashMap<String, Object> details;

    @JsonCreator
    public ContactConversionRequest(@JsonProperty("mode") ConversionMode mode, @JsonProperty("details") HashMap<String, Object> details) {
      this.mode = mode;
      this.details = details;
    }

    public boolean isExisting() {
      return EXISTING.equals(mode);
    }

    public Long getExistingId() {
      return Long.valueOf(details.get("id").toString());
    }

    public ContactCreateRequest getDetail() {
      ObjectMapper mapper = new ObjectMapper();
      try {
        ContactCreateRequest contactRequest = mapper.readValue(mapper.writeValueAsString(details), ContactCreateRequest.class);
        validateProperties(contactRequest);
        return contactRequest;
      } catch (LeadConversionException le) {
        throw le;
      } catch (IOException e) {
        if (e.getCause() instanceof LeadConversionException) {
          throw (LeadConversionException) e.getCause();
        }
        log.error(e.getMessage(), e);
      }
      return null;
    }

    public Map<String, Object> getDetailMap() {
      return this.details;
    }

    public void validateProperties(ContactCreateRequest contactRequest) {
      Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
      Set<ConstraintViolation<ContactCreateRequest>> validationErrors = validator.validate(contactRequest);
      if (!validationErrors.isEmpty()) {
        List<String> errorLogs =
            validationErrors.stream()
                .map(
                    constraintViolation ->
                        "Field: "
                            + constraintViolation.getPropertyPath()
                            + " | message: "
                            + constraintViolation.getMessage())
                .collect(Collectors.toList());
        log.error(String.join(",", errorLogs));
        throw new LeadConversionException(SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getCode(),
            SalesErrorCodes.INVALID_SPECIAL_CHARACTER_IN_TEXT_FIELD.getMessage());
      }
    }
  }

  public void replaceMaskedPhoneNumbers(Map<String, String> conversionMapping, Lead persistedLead) {
    if (conversionMapping.isEmpty()) {
      return;
    }
    updateContactConversionWithUnmaskedValues(persistedLead, conversionMapping);
    updateCompanyConversionWithUnmaskedValues(persistedLead, conversionMapping);
  }

  private void updateCompanyConversionWithUnmaskedValues(Lead persistedLead,
      Map<String, String> conversionMapping) {
    if (ObjectUtils.isEmpty(this.getCompany()) || ObjectUtils.isEmpty(
        this.getCompany().getDetails().get("phoneNumbers"))) {
      return;
    }
    List<Map<String, Object>> companyPhoneNumbers = (List<Map<String, Object>>) this.getCompany().getDetails().get("phoneNumbers");
    if (conversionMapping.get("companyPhoneNumbers").equals("phoneNumbers")) {
      this.getCompany().getDetails()
          .put("phoneNumbers", replaceMaskedPhoneNumbersWithLeadPhoneNumbers(companyPhoneNumbers, persistedLead.getLeadPhoneNumbers()));
      return;
    }
    this.getCompany().getDetails()
        .put("phoneNumbers", replaceMaskedPhoneNumbersWithLeadCompanyPhones(companyPhoneNumbers, persistedLead.getLeadCompanyPhoneNumbers()));
  }

  private void updateContactConversionWithUnmaskedValues(Lead persistedLead,
      Map<String, String> conversionMapping) {
    if (ObjectUtils.isEmpty(this.getContact()) || ObjectUtils.isEmpty(
        this.getContact().getDetailMap().get("phoneNumbers"))) {
      return;
    }
    List<Map<String, Object>> contactPhoneNumbers = ((List<Map<String, Object>>) this.getContact().getDetailMap().get("phoneNumbers"));
    if (conversionMapping.get("contactPhoneNumbers").equals("phoneNumbers")) {
      this.getContact().getDetailMap()
          .put("phoneNumbers", replaceMaskedPhoneNumbersWithLeadPhoneNumbers(contactPhoneNumbers, persistedLead.getLeadPhoneNumbers()));
      return;
    }
    this.getContact().getDetailMap()
        .put("phoneNumbers", replaceMaskedPhoneNumbersWithLeadCompanyPhones(contactPhoneNumbers, persistedLead.getLeadCompanyPhoneNumbers()));

  }

  private List<Map<String, Object>> replaceMaskedPhoneNumbersWithLeadPhoneNumbers(List<Map<String, Object>> phoneNumbers,
      Set<LeadPhoneNumber> leadPhoneNumbers) {
    if (ObjectUtils.isEmpty(phoneNumbers)) {
      return null;
    }
    phoneNumbers.stream().forEach(phoneNumber -> leadPhoneNumbers.forEach(leadPhoneNumber -> {
      if(ObjectUtils.isEmpty(phoneNumber.get("id"))){
        return;
      }
      if (Long.parseLong(phoneNumber.get("id").toString())==(leadPhoneNumber.getId()) &&  ((String)phoneNumber.get("value")).contains("****")) {
        phoneNumber.put("value",leadPhoneNumber.getValue());
      }
    }));

    if (phoneNumbers.stream().anyMatch(phoneNumberRequest -> ((String)phoneNumberRequest.get("value")).contains("*"))) {
      throw new SalesException(ErrorCodes.INVALID_PHONE_NUMBER);
    }
    return phoneNumbers;
  }

  private List<Map<String, Object>> replaceMaskedPhoneNumbersWithLeadCompanyPhones(List<Map<String, Object>> phoneNumbers,
      Set<LeadCompanyPhoneNumber> leadCompanyPhoneNumbers) {
    if (ObjectUtils.isEmpty(phoneNumbers)) {
      return null;
    }
    phoneNumbers.stream().forEach(phoneNumber -> leadCompanyPhoneNumbers.forEach(leadCompanyPhoneNumber -> {
      if(ObjectUtils.isEmpty(phoneNumber.get("id"))){
        return;
      }
      if (Long.parseLong(phoneNumber.get("id").toString())==(leadCompanyPhoneNumber.getId())&&  ((String)phoneNumber.get("value")).contains("****")) {
        phoneNumber.put("value", leadCompanyPhoneNumber.getValue());
      }
    }));

    if (phoneNumbers.stream().anyMatch(phoneNumberRequest -> ((String)phoneNumberRequest.get("value")).contains("*"))) {
      throw new SalesException(ErrorCodes.INVALID_PHONE_NUMBER);
    }

    return phoneNumbers;
  }
}
