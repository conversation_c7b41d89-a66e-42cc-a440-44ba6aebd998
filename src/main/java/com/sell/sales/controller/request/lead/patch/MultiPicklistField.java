package com.sell.sales.controller.request.lead.patch;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MultiPicklistField {
  private FieldOperation operation;
  private List values;

  @JsonCreator
  public MultiPicklistField(
      @JsonProperty("operation") FieldOperation operation,
      @JsonProperty("values")List values) {
    this.operation = operation;
    this.values = values;
  }
}
