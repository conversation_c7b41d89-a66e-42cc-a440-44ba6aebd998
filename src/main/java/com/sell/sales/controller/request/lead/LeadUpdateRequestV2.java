package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.controller.request.IdName;
import com.sell.sales.controller.request.lead.patch.Email;
import com.sell.sales.controller.request.lead.patch.PhoneNumber;
import com.sell.sales.controller.request.lead.patch.Product;
import com.sell.sales.utils.EmptyStringToNull;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LeadUpdateRequestV2 extends LeadBaseRequest implements Serializable {
  private static final long serialVersionUID = 1L;
  private Date actualClosureDate;
  private IdName ownerId;
  private Pipeline pipeline;
  @JsonProperty("products")
  private Product product;
  @JsonProperty("emails")
  private Email email;
  @JsonProperty("phoneNumbers")
  private PhoneNumber phoneNumber;
  @JsonProperty("companyPhones")
  private PhoneNumber companyPhone;
  @JsonProperty("subSource")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String subSource;
  @JsonProperty("utmSource")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmSource;
  @JsonProperty("utmMedium")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmMedium;
  @JsonProperty("utmCampaign")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmCampaign;
  @JsonProperty("utmTerm")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmTerm;
  @JsonProperty("utmContent")
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmContent;
  @JsonProperty("score")
  private Double score;
}
