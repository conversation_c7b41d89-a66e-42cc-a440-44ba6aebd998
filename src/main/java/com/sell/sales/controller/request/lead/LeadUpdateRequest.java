package com.sell.sales.controller.request.lead;

import com.sell.sales.domain.LeadCompanyPhoneNumber;
import com.sell.sales.domain.LeadPhoneNumber;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

@Getter
@Setter
public class LeadUpdateRequest extends LeadRequest implements Serializable {
  private static final long serialVersionUID = 1L;
  private Date actualClosureDate;

  public Set<LeadPhoneNumber> getLeadPhoneNumbers(PhoneNumberRequest[] leadPhoneNumberRequests) {
    if (ObjectUtils.isEmpty(leadPhoneNumberRequests)) {
      return Collections.emptySet();
    }

    Set<PhoneNumberRequest> phoneNumberRequestSet = new HashSet<>(Arrays.asList(leadPhoneNumberRequests));
    if (phoneNumberRequestSet.size() != leadPhoneNumberRequests.length) {
      throw new SalesException(SalesErrorCodes.DUPLICATE_PHONES);
    }

    return Arrays.stream(leadPhoneNumberRequests).map(leadPhoneNumberRequest -> {
      LeadPhoneNumber leadPhoneNumber = new LeadPhoneNumber(leadPhoneNumberRequest.getType(), leadPhoneNumberRequest.getCode(),
          leadPhoneNumberRequest.getValue(), leadPhoneNumberRequest.getDialCode(), null, leadPhoneNumberRequest.isPrimary());
      if(!ObjectUtils.isEmpty(leadPhoneNumberRequest.getId())){
        leadPhoneNumber.setId(leadPhoneNumberRequest.getId());
      }
      return leadPhoneNumber;
    }).collect(Collectors.toSet());

  }

  public Set<LeadCompanyPhoneNumber> getLeadCompanyPhoneNumbers(PhoneNumberRequest[] leadPhoneNumberRequests) {
    if(ObjectUtils.isEmpty(leadPhoneNumberRequests)) {
      return null;
    }

    Set<PhoneNumberRequest> phoneNumberRequestSet = new HashSet<>(Arrays.asList(leadPhoneNumberRequests));
    if (phoneNumberRequestSet.size() != leadPhoneNumberRequests.length) {
      throw new SalesException(SalesErrorCodes.DUPLICATE_COMPANY_PHONES);
    }

    return Arrays.stream(leadPhoneNumberRequests).map(leadPhoneNumberRequest -> {
      LeadCompanyPhoneNumber leadCompanyPhoneNumber = new LeadCompanyPhoneNumber(leadPhoneNumberRequest.getType(), leadPhoneNumberRequest.getCode(),
          leadPhoneNumberRequest.getValue(), leadPhoneNumberRequest.getDialCode(), null, leadPhoneNumberRequest.isPrimary());
      if(!ObjectUtils.isEmpty(leadPhoneNumberRequest.getId())){
        leadCompanyPhoneNumber.setId(leadPhoneNumberRequest.getId());
      }
      return leadCompanyPhoneNumber;
    }).collect(Collectors.toSet());
  }
}
