package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactImportRequest extends ContactCreateRequest implements Serializable {

  private String companyName;
  private String ownerEmail;
  private String createdByEmail;
  private String updatedByEmail;
}
