package com.sell.sales.controller.request.pipeline;

import static com.sell.sales.core.constants.InputSize.MAX_INPUT_SIZE;

import com.sell.sales.domain.ForecastingType;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class UpdatePipelineStageRequest {

  @Schema(required = false, example = "Site visit", description = "Unique name for this stage")
  @Size(max = MAX_INPUT_SIZE, message = "Name must be max " + MAX_INPUT_SIZE + " characters")
  private String name;

  @Schema(required = false, example = "Site visit stage", description = "Description for this stage")
  @Size(min = 0, max = InputSize.MAX_INPUT_SIZE_DESCRIPTION, message = "Description must be less than " + InputSize.MAX_INPUT_SIZE_DESCRIPTION
      + " characters")
  private String description;

  @Schema(required = false, example = "OPEN", description = "Stage type")
  private ForecastingType forecastingType;

  @Schema(required = false, example = "1", description = "Position in the attached pipeline")
  @Min(value = 1, message = "Please select a positive number")
  private Integer position;

  @Schema(required = false, example = "50", description = "Win likelihood percentage between 0-100%")
  @Range(min = 0, max = 100, message = "Please select a number between 0 & 100 (both inclusive)")
  private Integer winLikelihood;
}
