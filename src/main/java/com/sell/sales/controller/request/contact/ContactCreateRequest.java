package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.core.constants.InputSize;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.entity.model.Email;
import com.sell.sales.entity.model.PhoneNumber;
import com.sell.sales.utils.EmptyStringToNull;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ContactCreateRequest {

  @Size(max = InputSize.MAX_INPUT_SIZE)
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String firstName;
  @Size(max = InputSize.MAX_INPUT_SIZE)
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String lastName;
  private Long salutation;
  private Long ownerId;
  private PhoneNumber[] phoneNumbers;
  private Email[] emails;
  private Map<String, Object> customFieldValues;
  private boolean dnd;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String timezone;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String address;
  private GPSCoordinate addressCoordinate;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String city;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String state;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String zipcode;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String country;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String facebook;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String twitter;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String linkedin;
  //Professional
  private Long company;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String designation;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String department;
  private boolean stakeholder;
  private Long campaign;
  private Long source;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String subSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmMedium;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmCampaign;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmTerm;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmContent;
  private Long createdBy;
  private Long updatedBy;
  private Date createdAt;
  private Date updatedAt;
  private Double score;
}
