package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.domain.GPSCoordinate;
import com.sell.sales.utils.EmptyStringToNull;
import com.sell.sales.core.constants.InputSize;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
abstract class LeadBaseRequest implements Serializable {

 private static final long serialVersionUID = 1L;

 @Size(max = InputSize.MAX_INPUT_SIZE)
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String title;
 @Size(max = InputSize.MAX_INPUT_SIZE)
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String firstName;
 @Size(max = InputSize.MAX_INPUT_SIZE)
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String lastName;
 private Long salutation;
 private String[] photoUrls;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String timezone;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String city;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String state;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String zipcode;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String country;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String department;
 private Boolean dnd;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String facebook;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String twitter;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String linkedIn;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String address;
 private GPSCoordinate addressCoordinate;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String pipelineStageReason;

 // company
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyName;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyAddress;
 private GPSCoordinate companyAddressCoordinate;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyCity;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyState;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyZipcode;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyCountry;
 private Integer companyEmployees;
 private Double companyAnnualRevenue;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyWebsite;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyIndustry;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String companyBusinessType;

 // requirement
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String requirementName;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String requirementCurrency;
 private Double requirementBudget;
 private Date expectedClosureOn;

 // Campaign
 private String firstCampaign;
 private String firstSource;
 private String firstSubSource;
 private String firstMedium;
 private Date firstRespondedAt;
 private String lastCampaign;
 private String lastSource;
 private String lastSubSource;
 private String lastMedium;
 private Date lastRespondedAt;
 @JsonDeserialize(using = EmptyStringToNull.class)
 private String designation;
 private Long campaign;
 private Long source;
 // CustomFields
 private Map<String, Object> customFieldValues;

}
