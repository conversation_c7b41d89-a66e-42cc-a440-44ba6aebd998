package com.sell.sales.controller.request.contact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.controller.request.lead.ImportStrategy;
import javax.validation.Valid;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactImportRequestData {

  private final long jobId;
  @Valid
  private final ContactImportRequest contact;
  private final ImportStrategy importStrategy;

  @JsonCreator
  public ContactImportRequestData(
      @JsonProperty("jobId") long jobId, @JsonProperty("contact") ContactImportRequest contact,
      @JsonProperty("importStrategy") ImportStrategy importStrategy) {
    this.jobId = jobId;
    this.contact = contact;
    this.importStrategy = importStrategy;
  }
}
