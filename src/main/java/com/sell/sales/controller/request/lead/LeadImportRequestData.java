package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.Valid;
import lombok.Getter;

@Getter
public class LeadImportRequestData {

  private final long jobId;
  @Valid
  private final LeadImportRequest leadToImport;
  private final ImportStrategy importStrategy;
  private final Boolean errorInImport;

  @JsonCreator
  public LeadImportRequestData(
      @JsonProperty("jobId") long jobId, @JsonProperty("lead") LeadImportRequest leadToImport,
      @JsonProperty("importStrategy") ImportStrategy importStrategy,
      @JsonProperty("errorInImport") Boolean errorInImport) {
    this.jobId = jobId;
    this.leadToImport = leadToImport;
    this.importStrategy = importStrategy;
    this.errorInImport = errorInImport;
  }
}
