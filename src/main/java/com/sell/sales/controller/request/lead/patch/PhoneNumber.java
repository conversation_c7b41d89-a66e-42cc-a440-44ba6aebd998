package com.sell.sales.controller.request.lead.patch;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class PhoneNumber {
  private final FieldOperation operation;
  private final com.sell.sales.entity.model.PhoneNumber[] phoneNumbers;

  @JsonCreator
  public PhoneNumber(@JsonProperty("operation") FieldOperation fieldOperation,
      @JsonProperty("values") com.sell.sales.entity.model.PhoneNumber[] phoneNumbers) {
    this.operation = fieldOperation;
    this.phoneNumbers = phoneNumbers;
  }
}
