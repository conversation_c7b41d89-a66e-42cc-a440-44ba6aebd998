package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sell.sales.entity.model.Email;
import com.sell.sales.utils.EmptyStringToNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LeadRequest extends LeadBaseRequest implements Serializable {

  private static final long serialVersionUID = 1L;
  private Pipeline pipeline;
  private List<ProductDTO> products;
  private Long ownerId;
  private Email[] emails;
  private PhoneNumberRequest[] phoneNumbers;
  private PhoneNumberRequest[] companyPhones;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String subSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmSource;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmMedium;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmCampaign;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmTerm;
  @JsonDeserialize(using = EmptyStringToNull.class)
  private String utmContent;
  private Long createdBy;
  private Long updatedBy;
  private Date createdAt;
  private Date updatedAt;
  private Double score;

}
