package com.sell.sales.controller.request.lead.patch;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class Product {
  private FieldOperation operation;
  private List<com.sell.sales.domain.Product> products;

  @JsonCreator
  public Product(
      @JsonProperty("operation") FieldOperation operation,
      @JsonProperty("values")List<com.sell.sales.domain.Product> products) {
    this.operation = operation;
    this.products = products;
  }
}
