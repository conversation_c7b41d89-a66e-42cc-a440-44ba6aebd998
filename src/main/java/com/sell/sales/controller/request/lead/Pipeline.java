package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class Pipeline {
  private final Long id;
  private final String name;
  private final PipelineStage stage;

  @JsonCreator
  public Pipeline(@JsonProperty("id") Long id, @JsonProperty("name") String name, @JsonProperty("stage") PipelineStage stage) {
    this.id = id;
    this.name = name;
    this.stage = stage;
  }

  public Long pipelineStageId(){
    return this.getStage().getId();
  }
}
