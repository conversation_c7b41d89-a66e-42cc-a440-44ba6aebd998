package com.sell.sales.controller.request.lead;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sell.sales.core.domain.EntityType;
import java.util.Date;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversionAssociationDTO {

  private final EntityType entityType;
  private final Long entityId;
  @JsonProperty("convertedAt")
  private final Date convertedAt;

  @JsonCreator
  public ConversionAssociationDTO(@JsonProperty("entityType") EntityType entityType, @JsonProperty("entityId") Long entityId,
      @JsonProperty("convertedAt") Date convertedAt) {
    this.entityType = entityType;
    this.entityId = entityId;
    this.convertedAt = convertedAt;
  }
}
