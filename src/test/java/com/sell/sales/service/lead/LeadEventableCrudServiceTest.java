package com.sell.sales.service.lead;


import static com.sell.sales.core.domain.EntityType.LEAD;
import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mapstruct.ap.internal.util.Collections.asSet;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.core.event.CoreEvent;
import com.sell.sales.core.event.CoreEventEmitter;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.LeadCompanyPhoneNumber;
import com.sell.sales.domain.LeadPhoneNumber;
import com.sell.sales.domain.MetaInfo;
import com.sell.sales.domain.Operation;
import com.sell.sales.domain.Product;
import com.sell.sales.entity.dto.SearchResponseDetailsDTO;
import com.sell.sales.entity.dto.SearchResponseWithMetaData;
import com.sell.sales.entity.exception.OwnerDoesNotExist;
import com.sell.sales.entity.model.PhoneType;
import com.sell.sales.entity.service.IdNameResolver;
import com.sell.sales.entity.service.SalesIdNameResolver;
import com.sell.sales.exception.InvalidPipelineStageException;
import com.sell.sales.exception.SalesException;
import com.sell.sales.infra.mq.IdNameCommandPublisher;
import com.sell.sales.infra.mq.LeadEventPublisherV2;
import com.sell.sales.infra.mq.command.IdNameRequest;
import com.sell.sales.infra.mq.event.LeadEvent;
import com.sell.sales.infra.mq.event.LeadEventPublisherV3;
import com.sell.sales.pipeline.service.LeadPipelineService;
import com.sell.sales.repository.LeadRepository;
import com.sell.sales.security.User;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.client.iam.web.response.UserResponse;
import com.sell.sales.service.product.ProductRepository;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.transaction.annotation.Transactional;

@RunWith(MockitoJUnitRunner.class)
public class LeadEventableCrudServiceTest {

  @Mock
  private UserFacade userFacade;
  @Mock
  private LeadPipelineService leadPipelineService;
  @Mock
  private LeadCustomFieldCrudService leadCustomFieldCrudService;
  @Mock
  private CoreEventEmitter coreEventEmitter;
  @Mock
  private ProductRepository productRepository;
  @Mock
  private LeadOwnershipChangeService ownershipChangeService;

  @Mock
  private Lead mockLead;

  @Mock
  private LeadEventPublisherV2 leadEventPublisherV2;

  @Mock
  private IdNameCommandPublisher idNameCommandPublisher;


  private LeadEventableCrudService spiedUnit;
  @Mock
  private IdNameResolver idNameResolver;
  @Mock
  private LeadRepository leadRepository;
  @Mock
  private LeadEventPublisherV3 leadEventPublisherV3;
  @Mock
  private SalesIdNameResolver salesIdNameResolver;


  @Before
  public void setUp() {
    LeadEventableCrudService unit = new LeadEventableCrudService(leadCustomFieldCrudService, coreEventEmitter,
        userFacade, leadPipelineService, ownershipChangeService, leadEventPublisherV2, idNameCommandPublisher, idNameResolver,
        leadRepository, leadEventPublisherV3,salesIdNameResolver);
    spiedUnit = Mockito.spy(unit);

    Action defaultRecordActions = new Action().read(true).update(true).delete(true).note(true).task(true);
    given(mockLead.getRecordActions()).willReturn(defaultRecordActions);
  }


  @Test
  public void givenNonConvertedLead_shouldUpdatePipelineStage() {
    long leadId = 21L;
    long pipelineId = 33L;
    final long stageIdToActivate = 2L;
    long userId = 10;
    long tenantId = 11;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setTenantId(tenantId);
    lead.setPipeline(pipelineId);
    lead.setPipelineStage(3L);
    lead.setVersion(1);
    Map<String, Object> metadata = new HashMap<>();
    metadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(metadata);
    User user = new User(userId, tenantId, "Tony", Collections.EMPTY_SET);
    given(userFacade.getLoggedInUser()).willReturn(user);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);

    spiedUnit.setActiveStage(leadId, stageIdToActivate, null);

    verify(leadPipelineService, times(1)).setActiveStage(leadId, pipelineId, stageIdToActivate, null);
  }

  @Test
  public void givenPipelineStageIdToActive_leadHasSameStage_shouldThrow() {
    long leadId = 21L;
    final long stageIdToActivate = 2L;
    long userId = 10;
    long tenantId = 11;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setTenantId(tenantId);
    lead.setPipelineStage(2L);
    lead.setVersion(1);
    Map<String, Object> metadata = new HashMap<>();
    metadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(metadata);
    User user = new User(userId, tenantId, "Tony", Collections.EMPTY_SET);
    given(userFacade.getLoggedInUser()).willReturn(user);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);


    assertThatExceptionOfType(InvalidPipelineStageException.class)
        .isThrownBy(() ->  spiedUnit.setActiveStage(leadId, stageIdToActivate, null))
        .withMessage("pipeline.stage.is.already.active");

  }
  @Test
  public void givenConvertedLead_shouldUpdatePipelineStage() {
    long leadId = 21L;
    long pipelineId = 33L;
    final long stageIdToActivate = 2L;
    long userId = 10;
    long tenantId = 11;
    Lead lead = new Lead();
    lead.setRecordActions(new Action());
    lead.setId(leadId);
    lead.setTenantId(tenantId);
    lead.setPipeline(pipelineId);
    lead.setPipelineStage(4L);
    lead.setVersion(1);
    lead.setConvertedBy(10L);
    Map<String, Object> metadata = new HashMap<>();
    metadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(metadata);
    User user = new User(userId, tenantId, "Tony", Collections.EMPTY_SET);
    given(userFacade.getLoggedInUser()).willReturn(user);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);

    spiedUnit.setActiveStage(leadId, stageIdToActivate, null);

    verify(leadPipelineService, times(1)).setActiveStage(leadId, pipelineId, stageIdToActivate, null);
  }

  @Test
  public void givenNonConvertedLead_shouldNotAllowToChangeOwnershipForNonExistingOwner() throws OwnerDoesNotExist {
    long leadId = 21L;
    long ownerId = 11L;

    given(mockLead.getId()).willReturn(leadId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(mockLead);
    given(mockLead.isConverted()).willReturn(false);
    doThrow(new OwnerDoesNotExist()).when(ownershipChangeService).changeOwnership(ownerId, leadId);

    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> spiedUnit.changeOwnership(ownerId, leadId))
        .withMessage("user.not.found");
  }

  @Test
  public void givenNonConvertedLead_shouldAllowToChangeOwnership() throws OwnerDoesNotExist {
    long leadId = 21L;
    long ownerId = 11L;

    given(mockLead.getId()).willReturn(leadId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(mockLead);
    given(mockLead.isConverted()).willReturn(false);

    spiedUnit.changeOwnership(ownerId, leadId);

    verify(ownershipChangeService, times(1)).changeOwnership(ownerId, leadId);
  }

  @Test
  public void givenNonConvertedLead_shouldNotUpdateRecordActions() {
    long leadId = 21L;

    given(mockLead.getId()).willReturn(leadId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(mockLead);
    given(mockLead.isConverted()).willReturn(false);

    final Action recordActions = spiedUnit.get(leadId).getRecordActions();

    assertThat(recordActions.isRead()).isTrue();
    assertThat(recordActions.isUpdate()).isTrue();
    assertThat(recordActions.isDelete()).isTrue();
    assertThat(recordActions.isTask()).isTrue();
    assertThat(recordActions.isNote()).isTrue();
  }

  @Test
  public void givenConvertedLead_shouldUpdateRecordActions() {
    long leadId = 21L;

    given(mockLead.getId()).willReturn(leadId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(mockLead);
    given(mockLead.isConverted()).willReturn(true);

    final Action recordActions = spiedUnit.get(leadId).getRecordActions();

    assertThat(recordActions.isRead()).isTrue();
    assertThat(recordActions.isNote()).isTrue();
    assertThat(recordActions.isTask()).isTrue();
    assertThat(recordActions.isUpdate()).isTrue();
    assertThat(recordActions.isDelete()).isFalse();
  }

  @Test
  public void givenLeadToCreate_shouldPublishIdNameCommand() {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    given(userFacade.getUserId()).willReturn(userId);
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    lead.setTenantId(12L);
    lead.setVersion(1);
    lead.setOwnerId(userId);
    lead.setCreatedBy(userId);
    lead.setUpdatedBy(userId);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("Tony Stark");
    expectedLead.setTenantId(12L);
    expectedLead.setVersion(1);
    expectedLead.setId(leadId);
    expectedLead.setOwnerId(userId);

    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), eq("Lead"))).willReturn(searchResponseWithMetaData);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    //when
    spiedUnit.create(lead);
    //then
    ArgumentCaptor<Long> tenantIdArgCaptor = ArgumentCaptor.forClass(Long.class);
    ArgumentCaptor<Long> userIdArgCaptor = ArgumentCaptor.forClass(Long.class);
    ArgumentCaptor<IdNameRequest> idNameArgCaptor = ArgumentCaptor.forClass(IdNameRequest.class);
    Mockito.verify(idNameCommandPublisher, times(1))
        .publishIdNameCreate(tenantIdArgCaptor.capture(), userIdArgCaptor.capture(), idNameArgCaptor.capture());
    assertThat(tenantIdArgCaptor.getValue()).isEqualTo(tenantId);
    assertThat(userIdArgCaptor.getValue()).isEqualTo(userId);
    IdNameRequest idNameRequest = idNameArgCaptor.getValue();
    assertThat(idNameRequest.getTenantId()).isEqualTo(tenantId);
    assertThat(idNameRequest.getName()).isEqualTo("Tony Stark");
    assertThat(idNameRequest.getId()).isEqualTo(leadId);
    assertThat(idNameRequest.getForType()).isEqualTo("LEAD");
  }

  @Test
  public void givenLeadToUpdate_shouldPublishIdNameCommand() {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    lead.setTenantId(12L);
    lead.setVersion(1);
    lead.setOwnerId(3L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("Tony Stark");
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(leadId);
    expectedLead.setOwnerId(3L);

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then
    ArgumentCaptor<Long> tenantIdArgCaptor = ArgumentCaptor.forClass(Long.class);
    ArgumentCaptor<Long> userIdArgCaptor = ArgumentCaptor.forClass(Long.class);
    ArgumentCaptor<IdNameRequest> idNameArgCaptor = ArgumentCaptor.forClass(IdNameRequest.class);
    Mockito.verify(idNameCommandPublisher, times(1))
        .publishIdNameUpdate(tenantIdArgCaptor.capture(), userIdArgCaptor.capture(), idNameArgCaptor.capture());
    assertThat(tenantIdArgCaptor.getValue()).isEqualTo(tenantId);
    assertThat(userIdArgCaptor.getValue()).isEqualTo(userId);
    IdNameRequest idNameRequest = idNameArgCaptor.getValue();
    assertThat(idNameRequest.getTenantId()).isEqualTo(tenantId);
    assertThat(idNameRequest.getName()).isEqualTo("Tony Stark");
    assertThat(idNameRequest.getId()).isEqualTo(leadId);
    assertThat(idNameRequest.getForType()).isEqualTo("LEAD");
  }


  @Test
  public void givenLeadToUpdateWithNoChangeInNewAndOld_shouldNotPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId,1,userId);
    lead.setUpdatedAt(new Date());
    MetaInfo metaInfo = new MetaInfo();
    metaInfo.setLatestActivityCreatedAt(new Date());
    metaInfo.setNew(true);
    metaInfo.setUpdatedViaId("120");
    metaInfo.setUpdatedViaName("Updated Name");
    metaInfo.setUpdatedViaType("Updated Type");
    lead.setMetaInfo(metaInfo);


    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    String expectedDateString = "2021-12-22T13:32:13.275Z";
    Date updatedAt = simpleDateFormat.parse(expectedDateString);

    Lead expectedLead = getLead(leadId,5,userId);
    expectedLead.setUpdatedAt(updatedAt);
    MetaInfo metaInfo1 = new MetaInfo();
    metaInfo1.setLatestActivityCreatedAt(updatedAt);
    metaInfo1.setNew(false);
    metaInfo1.setUpdatedViaId("120");
    metaInfo1.setUpdatedViaName("New Lead");
    metaInfo1.setUpdatedViaType("New Type");
    lead.setMetaInfo(metaInfo1);

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(lead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithChangeInProductName_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Product product1 = new Product();
    product1.setName("product 1");
    product1.setId(23L);
    lead.setProducts(asList(product1));


    Lead expectedLead = getLead(leadId,5,3L);
    Product product2 = new Product();
    product2.setName("product 2");
    product2.setId(23L);
    expectedLead.setProducts(asList(product2));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithNoChangeInPhoneNumber_shouldNotPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    LeadPhoneNumber leadPhoneNumber = new LeadPhoneNumber();
    leadPhoneNumber.setPrimary(true);
    leadPhoneNumber.setType(PhoneType.MOBILE);
    leadPhoneNumber.setId(100L);
    leadPhoneNumber.setValue("9090909090");
    leadPhoneNumber.setDialCode("+91");
    lead.setLeadPhoneNumbers(asSet(leadPhoneNumber));


    Lead expectedLead = getLead(leadId,5,3L);
    LeadPhoneNumber leadPhoneNumber1 = new LeadPhoneNumber();
    leadPhoneNumber1.setPrimary(true);
    leadPhoneNumber1.setType(PhoneType.MOBILE);
    leadPhoneNumber1.setId(100L);
    leadPhoneNumber1.setValue("9090909090");
    leadPhoneNumber1.setDialCode("+91");
    expectedLead.setLeadPhoneNumbers(asSet(leadPhoneNumber1));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithChangeInPhoneNumberId_shouldNotPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    LeadPhoneNumber leadPhoneNumber = new LeadPhoneNumber();
    leadPhoneNumber.setPrimary(true);
    leadPhoneNumber.setType(PhoneType.MOBILE);
    leadPhoneNumber.setId(100L);
    leadPhoneNumber.setValue("9090909090");
    leadPhoneNumber.setDialCode("+91");
    lead.setLeadPhoneNumbers(asSet(leadPhoneNumber));


    Lead expectedLead = getLead(leadId,5,3L);
    LeadPhoneNumber leadPhoneNumber1 = new LeadPhoneNumber();
    leadPhoneNumber1.setPrimary(true);
    leadPhoneNumber1.setType(PhoneType.MOBILE);
    leadPhoneNumber1.setId(101L);
    leadPhoneNumber1.setValue("9090909090");
    leadPhoneNumber1.setDialCode("+91");
    expectedLead.setLeadPhoneNumbers(asSet(leadPhoneNumber1));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithChangeInLeadCompanyPhoneNumberId_shouldNotPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    LeadCompanyPhoneNumber leadPhoneNumber = new LeadCompanyPhoneNumber();
    leadPhoneNumber.setPrimary(true);
    leadPhoneNumber.setType(PhoneType.MOBILE);
    leadPhoneNumber.setId(100L);
    leadPhoneNumber.setValue("9090909090");
    leadPhoneNumber.setDialCode("+91");
    lead.setLeadCompanyPhoneNumbers(asSet(leadPhoneNumber));


    Lead expectedLead = getLead(leadId,5,3L);
    LeadCompanyPhoneNumber leadPhoneNumber1 = new LeadCompanyPhoneNumber();
    leadPhoneNumber1.setPrimary(true);
    leadPhoneNumber1.setType(PhoneType.MOBILE);
    leadPhoneNumber1.setId(101L);
    leadPhoneNumber1.setValue("9090909090");
    leadPhoneNumber1.setDialCode("+91");
    expectedLead.setLeadCompanyPhoneNumbers(asSet(leadPhoneNumber1));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }


  @Test
  public void givenLeadHasProductAndTryToUpdateWithNoProduct_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Product product1 = new Product();
    product1.setName("product 1");
    product1.setId(23L);
    lead.setProducts(asList(product1));


    Lead expectedLead = getLead(leadId,5,3L);

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }


  @Test
  public void givenLeadWithNoProductUpdateWithChangeInProduct_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);

    Lead expectedLead = getLead(leadId,5,3L);
    Product product2 = new Product();
    product2.setName("product 2");
    product2.setId(23L);
    expectedLead.setProducts(asList(product2));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWith1ProductTryToUpdateWithAddingNewProduct_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Product product1 = new Product();
    product1.setName("product 1");
    product1.setId(23L);
    lead.setProducts(asList(product1));


    Lead expectedLead = getLead(leadId,5,3L);
    Product product2 = new Product();
    product2.setName("product 1");
    product2.setId(23L);
    expectedLead.setProducts(asList(product1,product2));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }


  @Test
  public void givenLeadToUpdateWithChangeInIsPrimaryPhoneNumber_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    LeadPhoneNumber leadPhoneNumber = new LeadPhoneNumber();
    leadPhoneNumber.setPrimary(true);
    leadPhoneNumber.setType(PhoneType.MOBILE);
    leadPhoneNumber.setId(100L);
    leadPhoneNumber.setValue("9090909090");
    leadPhoneNumber.setDialCode("+91");
    lead.setLeadPhoneNumbers(asSet(leadPhoneNumber));


    Lead expectedLead = getLead(leadId,5,3L);
    LeadPhoneNumber leadPhoneNumber1 = new LeadPhoneNumber();
    leadPhoneNumber1.setPrimary(false);
    leadPhoneNumber1.setType(PhoneType.MOBILE);
    leadPhoneNumber1.setId(100L);
    leadPhoneNumber1.setValue("9090909090");
    leadPhoneNumber1.setDialCode("+91");
    expectedLead.setLeadPhoneNumbers(asSet(leadPhoneNumber1));


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithNoChangeInTextCustomFieldValue_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Map<String, Object> customFieldValuesMap = new HashMap<>();
    customFieldValuesMap.put("myName", "John");
    lead.setCustomFieldValues(customFieldValuesMap);


    Lead expectedLead = getLead(leadId,5,3L);
    Map<String, Object> customFieldValuesMap1 = new HashMap<>();
    customFieldValuesMap1.put("myName", "John");
    expectedLead.setCustomFieldValues(customFieldValuesMap1);


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithChangeInTextCustomFieldValue_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Map<String, Object> customFieldValuesMap = new HashMap<>();
    customFieldValuesMap.put("myName", "John");
    lead.setCustomFieldValues(customFieldValuesMap);


    Lead expectedLead = getLead(leadId,5,3L);
    Map<String, Object> customFieldValuesMap1 = new HashMap<>();
    customFieldValuesMap1.put("myName", "Tommy");
    expectedLead.setCustomFieldValues(customFieldValuesMap1);


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithAddingNewCustomFieldValue_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Map<String, Object> customFieldValuesMap = new HashMap<>();
    customFieldValuesMap.put("myName", "John");
    lead.setCustomFieldValues(customFieldValuesMap);


    Lead expectedLead = getLead(leadId,5,3L);
    Map<String, Object> customFieldValuesMap1 = new HashMap<>();
    customFieldValuesMap1.put("myName", "John");
    customFieldValuesMap1.put("myAddress", "Pune");
    expectedLead.setCustomFieldValues(customFieldValuesMap1);


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }


  @Test
  public void givenLeadToUpdateWithAddingNewValueInMultiPicklist_shouldPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Map<String, Object> customFieldValuesMap = new HashMap<>();
    customFieldValuesMap.put("multiPick", Arrays.asList(10L,12L));
    lead.setCustomFieldValues(customFieldValuesMap);


    Lead expectedLead = getLead(leadId,5,3L);
    Map<String, Object> customFieldValuesMap1 = new HashMap<>();
    customFieldValuesMap1.put("multiPick", Arrays.asList(10L,13L));
    expectedLead.setCustomFieldValues(customFieldValuesMap1);


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(1)).emit(any(),any());
  }

  @Test
  public void givenLeadToUpdateWithNoChangeInMultiPicklist_shouldNotPublishLeadUpdatedEvent() throws ParseException {
    //given
    long tenantId = 101L;
    long userId = 101L;
    long leadId = 99L;
    Lead lead = getLead(leadId, 1, 3L);
    Map<String, Object> customFieldValuesMap = new HashMap<>();
    customFieldValuesMap.put("multiPick", Arrays.asList(10L,12L));
    lead.setCustomFieldValues(customFieldValuesMap);


    Lead expectedLead = getLead(leadId,5,3L);
    Map<String, Object> customFieldValuesMap1 = new HashMap<>();
    customFieldValuesMap1.put("multiPick", Arrays.asList(10L,12L));
    expectedLead.setCustomFieldValues(customFieldValuesMap1);


    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(userId);
    given(leadCustomFieldCrudService.get(leadId)).willReturn(lead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(expectedLead, lead);
    //then

    verify(coreEventEmitter,times(0)).emit(any(),any());
  }


  @Test
  public void givenLeadToDelete_shouldDelete() {
    //given
    long leadId = 99L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setCreatedBy(1L);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    lead.setTenantId(1L);
    lead.setOwnerId(1L);
    given(userFacade.getUserId()).willReturn(1L);
    given(userFacade.hasDeletePermissionOnEntity(LEAD)).willReturn(true);
    //when
    Lead deletedLead = spiedUnit.delete(lead);
    //then
    Assertions.assertThat(deletedLead.getId()).isEqualTo(99L);
    Assertions.assertThat(deletedLead.getFirstName()).isEqualTo("Tony");
    Assertions.assertThat(deletedLead.getLastName()).isEqualTo("Stark");
  }

  @Test
  public void givenLeadDeleteRequest_notHavingDeletePermissionOnContact_shouldThrow() {
    //given
    long leadId = 99L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setOwnerId(1L);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    given(userFacade.getUserId()).willReturn(1L);

    //then
    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> spiedUnit.delete(lead))
        .withMessage("lead.cannot.be.deleted");
  }

  @Test
  public void givenLead_havingIncorrectOwnerId_shouldThrow() {
    //given
    long leadId = 99L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setOwnerId(10L);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    given(userFacade.getUserId()).willReturn(1L);
    given(userFacade.hasDeletePermissionOnEntity(LEAD)).willReturn(true);

    //then
    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> spiedUnit.delete(lead))
        .withMessage("lead.cannot.be.deleted");
  }

  @Test
  public void givenLead_withDeleteAllPermission_shouldDelete() {
    //given
    long leadId = 99L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setOwnerId(1L);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    given(userFacade.getUserId()).willReturn(1L);
    given(userFacade.hasDeleteAllPermissionOnEntity(LEAD)).willReturn(true);

    //when
    Lead deletedLead = spiedUnit.delete(lead);
    //then
    Assertions.assertThat(deletedLead.getId()).isEqualTo(99L);
    Assertions.assertThat(deletedLead.getFirstName()).isEqualTo("Tony");
    Assertions.assertThat(deletedLead.getLastName()).isEqualTo("Stark");
  }

  @Test
  @Transactional
  public void givenLeadToCreate_shouldAppendMetaData() throws InterruptedException, ExecutionException {
    //given
    given(userFacade.getUserId()).willReturn(34L);
    long leadId = 10L;
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    lead.setTenantId(12L);
    lead.setVersion(1);
    lead.setOwnerId(34L);
    lead.setCreatedBy(34L);
    lead.setUpdatedBy(34L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("Tony Stark");
    expectedLead.setTenantId(12L);
    expectedLead.setVersion(1);
    expectedLead.setId(leadId);
    expectedLead.setOwnerId(34L);

    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    given(userFacade.hasUpdatePermissionOnEntity(LEAD)).willReturn(true);
    //when
    spiedUnit.create(lead);
    //then
    verify(salesIdNameResolver, times(1)).buildIdNameStoreMetadata(any());
  }

  @Test
  public void givenLeadWithOwnerAndWithoutUpdatePermission_tryToCreate_shouldThrow() {
    //given
    long ownerId = 30L;
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(ownerId);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(false);
    //when & //then
    assertThatThrownBy(() -> spiedUnit.create(lead))
        .isInstanceOf(SalesException.class)
        .hasMessage("common.record.permission.error");
  }

  @Test
  public void givenLeadWithInactiveOwner_tryToCreate_shouldThrow() {
    //given
    long ownerId = 30L;
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(ownerId);
    given(userFacade.hasLeadUpdateAllPermission()).willReturn(true);
    UserResponse userResponse = givenUserWithPermission(30L, true, false);
    given(ownershipChangeService.getUser(30L)).willReturn(userResponse);
    //when & //then
    assertThatThrownBy(() -> spiedUnit.create(lead))
        .isInstanceOf(SalesException.class)
        .hasMessage("user.not.active");
  }

  @Test
  public void givenLeadWithCreatedByNotPresent_tryToCreate_shouldThrow() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(30L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(30L);
    lead.setCreatedBy(30L);
    lead.setUpdatedBy(15L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasLeadUpdatePermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(30L, true, true);
    given(ownershipChangeService.getUser(30L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    given(ownershipChangeService.getUser(15L)).willThrow(new RuntimeException());
    //when
    //when & //then
    assertThatThrownBy(() -> spiedUnit.create(lead))
        .isInstanceOf(SalesException.class)
        .hasMessage("updatedBy.not.present");
  }

  @Test
  public void givenLeadWithUpdatedByNotPresent_tryToCreate_shouldThrow() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(30L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(30L);
    lead.setCreatedBy(15L);
    lead.setUpdatedBy(30L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasLeadUpdatePermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(30L, true, true);
    given(ownershipChangeService.getUser(30L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    given(ownershipChangeService.getUser(15L)).willThrow(new RuntimeException());
    //when
    //when & //then
    assertThatThrownBy(() -> spiedUnit.create(lead))
        .isInstanceOf(SalesException.class)
        .hasMessage("createdBy.not.present");
  }

  @Test
  public void givenLeadWithActiveOwnerAndHasNoReadPermission_tryToCreate_shouldThrow() {
    //given
    long ownerId = 30L;
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(ownerId);
    given(userFacade.hasLeadUpdateAllPermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(tenantId);
    UserResponse userResponse = givenUserWithPermission(30L, false, true);
    given(ownershipChangeService.getUser(30L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(false);
    //when & //then
    assertThatThrownBy(() -> spiedUnit.create(lead))
        .isInstanceOf(SalesException.class)
        .hasMessage("common.read.permission.error");
  }


  @Test
  public void givenLeadWithOwnerHasReadPermission_shouldCreateAndRaiseCreatedEvent() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(30L);
    lead.setCreatedBy(30L);
    lead.setUpdatedBy(30L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(30L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasLeadUpdatePermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(30L, true, true);
    given(ownershipChangeService.getUser(30L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    //when
    Lead createdLead = spiedUnit.create(lead);
    //then
    ArgumentCaptor<LeadEvent> leadEventArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    verify(coreEventEmitter, times(1)).emit(any(CoreEvent.class), leadEventArgumentCaptor.capture());
    LeadEvent capturedLeadEvent = leadEventArgumentCaptor.getValue();
    assertThat(capturedLeadEvent.getOwnerId()).isEqualTo(30L);
    assertThat(createdLead).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenOwnerWithoutUpdatePermission_tryToUpdateExistingLead_shouldThrow() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(2L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    given(leadCustomFieldCrudService.get(entityId)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(lead);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(false);
    //when && then
    assertThatThrownBy(() -> spiedUnit.update(lead, oldLead))
        .isInstanceOf(SalesException.class)
        .hasMessage("common.record.permission.error");
  }

  @Test
  public void givenUserWithoutReassignPermission_tryToUpdateOthersExistingLeadOwner_shouldThrow() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(2L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("lead");
    permissionDTO.setAction(new com.sell.sales.core.domain.Action().read(true).reassign(false));
    given(userFacade.getLoggedInUser()).willReturn(new User(2L, 1000L, "wayne", asSet(permissionDTO)));
    given(leadCustomFieldCrudService.get(entityId)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(lead);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(userFacade.hasLeadUpdateAllPermission()).willReturn(false);
    //when && then
    assertThatThrownBy(() -> spiedUnit.update(lead, oldLead))
        .isInstanceOf(SalesException.class)
        .hasMessage("common.record.permission.error");
  }

  @Test
  public void givenUserWithReassignPermission_tryToUpdateOthersExistingLeadOwner_shouldUpdate() {
    // given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(31L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("lead");
    permissionDTO.setAction(new com.sell.sales.core.domain.Action().read(true).reassign(true));
    UserResponse userResponse = givenUserWithPermission(31L, true, true);
    given(userFacade.getLoggedInUser()).willReturn(new User(31L, 1000L, "wayne", asSet(permissionDTO)));
    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(30L);
    given(ownershipChangeService.getUser(31L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, EntityType.LEAD)).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(31L);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    // when
    Lead updatedLead = spiedUnit.update(lead, oldLead);
    // then
    verify(ownershipChangeService, times(1)).sendLeadOwnerChangeEvent(any(Lead.class), eq(30L), anyList(), eq(true));
    ArgumentCaptor<LeadEvent> leadArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    verify(coreEventEmitter, times(1)).emit(any(CoreEvent.class), leadArgumentCaptor.capture());
    LeadEvent capturedLeadEvent = leadArgumentCaptor.getValue();
    assertThat(capturedLeadEvent).isNotNull();
    assertThat(capturedLeadEvent.getOwnerId()).isEqualTo(31L);
    assertThat(updatedLead).isEqualToComparingFieldByField(expectedLead);
  }


  @Test
  public void givenInactiveOwner_tryToUpdateExistingLead_shouldThrow() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(2L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    given(leadCustomFieldCrudService.get(entityId)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(lead);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(ownershipChangeService.getUser(2L)).willReturn(givenUserWithPermission(2L, true, false));
    given(userFacade.getUserId()).willReturn(30L);
    given(userFacade.hasLeadUpdateAllPermission()).willReturn(true);
    //when && then
    assertThatThrownBy(() -> spiedUnit.update(lead, oldLead))
        .isInstanceOf(SalesException.class)
        .hasMessage("user.not.active");
  }

  @Test
  public void givenActiveOwnerAndProducts_tryToUpdateExistingLead_shouldRaiseOwnerChangedAndLeadUpdatedEvents() throws UnsupportedEncodingException {
    // given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Product product1 = new Product();
    product1.setName("product 1");
    product1.setId(23L);
    lead.setProducts(asList(product1));
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(31L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    Product updatedProduct = new Product();
    updatedProduct.setName("product 1 updated");
    updatedProduct.setId(23L);

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setProducts(asList(updatedProduct));
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(31L);

    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    UserResponse userResponse = givenUserWithPermission(31L, true, true);
    given(userFacade.getUserId()).willReturn(30L);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(ownershipChangeService.getUser(anyLong())).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, EntityType.LEAD)).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(30L, 1000L, "James", Collections.emptySet()));
    given(productRepository.findProductByIdAndTenantId(product1.getId(), oldLead.getTenantId()))
        .willReturn(Optional.of(product1));
    // when
    Lead updatedLead = spiedUnit.updateWithProducts(lead, oldLead);

    // then
    verify(ownershipChangeService, times(1)).sendLeadOwnerChangeEvent(any(Lead.class), eq(30L), anyList(), eq(true));
    ArgumentCaptor<LeadEvent> leadArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    verify(coreEventEmitter, times(1)).emit(any(CoreEvent.class), leadArgumentCaptor.capture());
    LeadEvent capturedLeadEvent = leadArgumentCaptor.getValue();
    assertThat(capturedLeadEvent).isNotNull();
    assertThat(capturedLeadEvent.getOwnerId()).isEqualTo(31L);
    assertThat(updatedLead).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenActiveOwnerWithoutProducts_tryToUpdateExistingLead_shouldRaiseOwnerChangedAndLeadUpdatedEvents() {
    // given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(31L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    UserResponse userResponse = givenUserWithPermission(31L, true, true);
    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(userFacade.getUserId()).willReturn(30L);
    given(ownershipChangeService.getUser(31L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, EntityType.LEAD)).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(30L, 1000L, "James", Collections.emptySet()));

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(31L);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    // when
    Lead updatedLead = spiedUnit.update(lead, oldLead);

    // then
    verify(ownershipChangeService, times(1)).sendLeadOwnerChangeEvent(any(Lead.class), eq(30L), anyList(), eq(true));
    ArgumentCaptor<LeadEvent> leadArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    verify(coreEventEmitter, times(1)).emit(any(CoreEvent.class), leadArgumentCaptor.capture());
    LeadEvent capturedLeadEvent = leadArgumentCaptor.getValue();
    assertThat(capturedLeadEvent).isNotNull();
    assertThat(capturedLeadEvent.getOwnerId()).isEqualTo(31L);
    assertThat(updatedLead).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenExistingLeadWithoutOwner_shouldUpdate_andShouldNotRaiseLeadOwnerUpdatedEvent() {
    // given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Product product1 = new Product();
    product1.setName("product 1");
    product1.setId(23L);
    lead.setProducts(asList(product1));
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(30L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "James", Collections.emptySet()));

    Product updatedProduct = new Product();
    updatedProduct.setName("product 1 updated");
    updatedProduct.setId(23L);

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setProducts(asList(updatedProduct));
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(30L);

    given(productRepository.findProductByIdAndTenantId(product1.getId(), lead.getTenantId()))
        .willReturn(Optional.of(product1));
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);

    // when
    Lead updatedLead = spiedUnit.update(lead, oldLead);

    // then
    verifyZeroInteractions(ownershipChangeService);
    ArgumentCaptor<LeadEvent> leadArgumentCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    verify(coreEventEmitter, times(1)).emit(any(CoreEvent.class), leadArgumentCaptor.capture());
    LeadEvent capturedLeadEvent = leadArgumentCaptor.getValue();
    assertThat(capturedLeadEvent).isNotNull();
    assertThat(capturedLeadEvent.getOwnerId()).isEqualTo(30L);
    assertThat(updatedLead).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenLeadWithoutOwner_shouldNotValidatePermissions() {
    //given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(30L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(30L);

    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getUserId()).willReturn(2L);
    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(lead, oldLead);
    //then
    verify(userFacade, times(0)).hasUpdatePermissionOnEntity(any());
    verify(ownershipChangeService, times(0)).hasReadPermissionOnEntity(any(), any());
    verify(ownershipChangeService, times(0)).getUser(anyLong());

  }

  @Test
  public void givenLeadWithSameOwner_shouldNotValidatePermissions() {
    //given
    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(1000L);
    lead.setVersion(1);
    lead.setId(90L);
    lead.setOwnerId(30L);

    Lead oldLead = new Lead();
    oldLead.setLastName("wayne");
    oldLead.setMetaData(idNameMetadata);
    oldLead.setTenantId(1000L);
    oldLead.setVersion(1);
    oldLead.setId(90L);
    oldLead.setOwnerId(30L);

    Lead expectedLead = new Lead();
    expectedLead.setId(90L);
    expectedLead.setTenantId(1000L);
    expectedLead.setVersion(1);
    expectedLead.setLastName("wayne");
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setOwnerId(30L);

    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getUserId()).willReturn(2L);
    given(leadCustomFieldCrudService.get(90L)).willReturn(oldLead);
    given(leadCustomFieldCrudService.update(any(), any())).willReturn(expectedLead);
    //when
    spiedUnit.update(lead, oldLead);
    //then
    verify(userFacade, times(0)).hasUpdatePermissionOnEntity(any());
    verify(ownershipChangeService, times(0)).hasReadPermissionOnEntity(any(), any());
    verify(ownershipChangeService, times(0)).getUser(anyLong());

  }


  @Test
  public void givenLoggedInUserWithUpdatePermissionToCreateLead_withOwnerOtherThanLoggedInUser_shouldRaiseOwnerChangedEvent() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(31L);
    lead.setOldOwnerId(30L);
    lead.setCreatedBy(31L);
    lead.setUpdatedBy(31L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(31L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasLeadUpdatePermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(31L, true, true);
    given(ownershipChangeService.getUser(31L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    //when
    spiedUnit.create(lead);
    //then
    ArgumentCaptor<Lead> leadArgumentCaptor = ArgumentCaptor.forClass(Lead.class);
    verify(ownershipChangeService, times(1)).sendLeadOwnerChangeEvent(leadArgumentCaptor.capture(), eq(30L), eq(Collections.emptyList()), eq(true));
    assertThat(leadArgumentCaptor.getValue()).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenLoggedInUserWithUpdateAllPermissionToCreateLead_withOwnerOtherThanLoggedInUser_shouldRaiseOwnerChangedEvent() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(31L);
    lead.setOldOwnerId(30L);
    lead.setCreatedBy(31L);
    lead.setUpdatedBy(31L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(31L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasLeadUpdateAllPermission()).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(31L, true, true);
    given(ownershipChangeService.getUser(31L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    //when
    spiedUnit.create(lead);
    //then
    ArgumentCaptor<Lead> leadArgumentCaptor = ArgumentCaptor.forClass(Lead.class);
    verify(ownershipChangeService, times(1)).sendLeadOwnerChangeEvent(leadArgumentCaptor.capture(), eq(30L), eq(Collections.emptyList()), eq(true));
    assertThat(leadArgumentCaptor.getValue()).isEqualToComparingFieldByField(expectedLead);
  }

  @Test
  public void givenLeadToCreate_withOwnerAsLoggedInUser_shouldNotRaiseOwnerChangedEvent() {
    //given
    long entityId = 10L;
    long tenantId = 1000L;

    Lead lead = new Lead();
    lead.setLastName("wayne");
    Map<String, Object> idNameMetadata = new HashMap<>();
    idNameMetadata.put("idNameStore", new HashMap<>());
    lead.setMetaData(idNameMetadata);
    lead.setTenantId(tenantId);
    lead.setVersion(1);
    lead.setId(entityId);
    lead.setOwnerId(2000L);
    lead.setOldOwnerId(2000L);
    lead.setCreatedBy(2000L);
    lead.setUpdatedBy(2000L);

    Lead expectedLead = new Lead();
    expectedLead.setLastName("wayne");
    idNameMetadata.put("idNameStore", new HashMap<>());
    expectedLead.setMetaData(idNameMetadata);
    expectedLead.setTenantId(tenantId);
    expectedLead.setVersion(1);
    expectedLead.setId(entityId);
    expectedLead.setOwnerId(2000L);
    expectedLead.setOperation(new Operation(true, true, true));

    given(userFacade.hasUpdatePermissionOnEntity(EntityType.LEAD)).willReturn(true);
    given(userFacade.getTenantId()).willReturn(1000L);
    given(userFacade.getUserId()).willReturn(2000L);
    given(userFacade.getLoggedInUser())
        .willReturn(new User(2000L, 1000L, "Tony", Collections.emptySet()));
    SearchResponseWithMetaData searchResponseWithMetaData = new SearchResponseWithMetaData();
    searchResponseWithMetaData.setMetaData(new ObjectMapper().convertValue(lead, Map.class));
    searchResponseWithMetaData.setTotal(1L);
    given(leadCustomFieldCrudService.create(any())).willReturn(expectedLead);
    given(idNameResolver.getSearchResultWithIdNameStoreMap(any(), any())).willReturn(searchResponseWithMetaData);
    UserResponse userResponse = givenUserWithPermission(2000L, true, true);
    given(ownershipChangeService.getUser(2000L)).willReturn(userResponse);
    given(ownershipChangeService.hasReadPermissionOnEntity(userResponse, LEAD)).willReturn(true);
    given(userFacade.getTenantId()).willReturn(tenantId);
    //when
    spiedUnit.create(lead);
    //then
    Mockito.verifyZeroInteractions(ownershipChangeService);
  }

  private UserResponse givenUserWithPermission(Long userId, boolean hasReadPermission, boolean isActive) {
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("lead");
    permissionDTO.setAction(new com.sell.sales.core.domain.Action().read(hasReadPermission));
    UserResponse userResponse =
        new UserResponse(userId, "Tony", "Stark", "<EMAIL>", isActive, asSet(permissionDTO));
    return userResponse;
  }

  private Lead getLead(long leadId, int version, long ownerId) {
    Lead lead = new Lead();
    lead.setId(leadId);
    lead.setFirstName("Tony");
    lead.setLastName("Stark");
    lead.setTenantId(12L);
    lead.setVersion(version);
    lead.setOwnerId(ownerId);
    return lead;
  }
}
