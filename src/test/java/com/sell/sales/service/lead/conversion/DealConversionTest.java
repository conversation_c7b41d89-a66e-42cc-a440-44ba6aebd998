package com.sell.sales.service.lead.conversion;


import static com.sell.sales.controller.request.lead.ConversionMode.CREATE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;

import com.sell.sales.controller.request.lead.ConversionMode;
import com.sell.sales.controller.request.lead.LeadConversionRequest;
import com.sell.sales.controller.request.lead.LeadConversionRequest.ConversionRequest;
import com.sell.sales.infra.mq.DealCommandPublisher;
import com.sell.sales.infra.mq.command.DealCommand;
import com.sell.sales.infra.mq.command.ResetDealAssociatedContact;
import com.sell.sales.security.User;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.client.deal.DealService;
import com.sell.sales.service.client.deal.request.DealDTO;
import com.sell.sales.service.client.deal.request.Discount.Type;
import com.sell.sales.service.client.deal.request.IdName;
import com.sell.sales.service.client.deal.request.Product;
import com.sell.sales.service.client.deal.response.DealDetail;
import com.sell.sales.service.client.deal.response.DealSummary;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DealConversionTest {

  @Mock
  private UserFacade userFacade;
  @Mock
  private DealCommandPublisher dealCommandPublisher;
  @Mock
  private DealService dealService;
  @Mock
  private User mockUser;

  @InjectMocks
  private DealConversion dealConversion;

  ConversionHandler conversionHandler = new ConversionHandler();
  private final Long tenantId = 12L;

  @Before
  public void setUp() {
    given(userFacade.getLoggedInUser()).willReturn(mockUser);
    given(userFacade.getTenantId()).willReturn(tenantId);
  }

  @Test
  public void tryToConvertWithoutDeal_shouldReturnConversionHandler() {
    //given
    long dealId = 100;
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(null, null, null);
    //when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);
    //then
    assertThat(dealConversionHandler.getId()).isNull();
    assertThat(dealConversionHandler.getName()).isNull();
    dealConversionHandler.rollbackConsumer.accept(dealId);
    verifyZeroInteractions(dealCommandPublisher);
  }

  @Test
  public void givenLead_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "Deal1");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    assertThat(dealDTO.getValue().getName()).isEqualTo("Deal1");
  }

  @Test
  public void givenLeadWithSourceAndCampaign_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    dealDetails.put("source", new IdName(100L, "Google"));
    dealDetails.put("campaign", new IdName(101L, "Organic"));
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    assertThat(dealDTO.getValue().getSource().getId()).isEqualTo(100L);
    assertThat(dealDTO.getValue().getCampaign().getId()).isEqualTo(101L);
    assertThat(dealDTO.getValue().getSource().getName()).isEqualTo("Google");
    assertThat(dealDTO.getValue().getCampaign().getName()).isEqualTo("Organic");
  }

  @Test
  public void givenLeadWithoutSourceAndCampaign_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "deal1");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    assertThat(dealDTO.getValue().getName()).isEqualTo("deal1");
    assertThat(dealDTO.getValue().getSource()).isNull();
    assertThat(dealDTO.getValue().getCampaign()).isNull();
  }

  @Test
  public void givenLeadWithUTMFields_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    dealDetails.put("subSource","Sub Source" );
    dealDetails.put("utmSource","utmSource");
    dealDetails.put("utmCampaign","utmCampaign");
    dealDetails.put("utmMedium","utmMedium");
    dealDetails.put("utmContent","utmContent");
    dealDetails.put("utmTerm","utmTerm");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    assertThat(dealDTO.getValue().getSubSource()).isEqualTo("Sub Source");
    assertThat(dealDTO.getValue().getUtmSource()).isEqualTo("utmSource");
    assertThat(dealDTO.getValue().getUtmCampaign()).isEqualTo("utmCampaign");
    assertThat(dealDTO.getValue().getUtmMedium()).isEqualTo("utmMedium");
    assertThat(dealDTO.getValue().getUtmContent()).isEqualTo("utmContent");
    assertThat(dealDTO.getValue().getUtmTerm()).isEqualTo("utmTerm");
  }

  @Test
  public void givenLead_tryToConvertToNewDeal_shouldConvertAndSetValidRollbackAction() {
    // given
    long dealId = 1L;

    HashMap dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "Deal1");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    dealConversionHandler.getRollbackConsumer().accept(dealId);
    ArgumentCaptor<DealCommand> deleteCompanyCommandArgumentCaptor = ArgumentCaptor.forClass(DealCommand.class);
    verify(dealCommandPublisher, times(1)).sendDealDeleteCommand(deleteCompanyCommandArgumentCaptor.capture());
    DealCommand deleteDealCommand = deleteCompanyCommandArgumentCaptor.getValue();
    assertThat(dealConversionHandler.getName()).isEqualTo("Deal1");
    assertThat(deleteDealCommand.getDealId()).isEqualTo(dealId);
    assertThat(deleteDealCommand.getTenantId()).isEqualTo(tenantId);
  }

  @Test
  public void givenConversionWithAssociatedContact_tryToConvertToNewDeal_shouldConvertWithAssociatedContact() {
    // given
    long dealId = 1L;

    HashMap dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "Deal1");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    conversionHandler.setContactId(123L);
    conversionHandler.setContactName("Tony Stark");
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealArgumentCaptor = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealArgumentCaptor.capture());
    DealDTO dealCreateRequest = dealArgumentCaptor.getValue();
    assertThat(dealCreateRequest.getAssociatedContacts().size()).isEqualTo(1);
    assertThat(dealCreateRequest.getAssociatedContacts().get(0).getId()).isEqualTo(123L);
    assertThat(dealCreateRequest.getAssociatedContacts().get(0).getName()).isEqualTo("Tony Stark");
  }


  @Test
  public void givenConversionRequest_tryToConvertToExistingDeal_shouldValidateDealId() {
    // given
    long dealId = 1L;
    HashMap<String, Object> dealDetails = new HashMap<>();
    dealDetails.put("id", dealId);
    dealDetails.put("name", "Deal 1999");
    ConversionRequest conversionRequest = new ConversionRequest(ConversionMode.EXISTING, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    DealDetail dealDetail = new DealDetail(dealDetails);
    given(dealService.getDealDetail(dealId)).willReturn(dealDetail);
    // when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    verify(dealService, times(1)).getDealDetail(dealId);
    verify(dealService, times(0)).update(anyLong(), anyMap());
    dealConversionHandler.getRollbackConsumer().accept(dealId);
    dealConversionHandler.getName().equals("Deal 1999");
    Mockito.verifyZeroInteractions(dealCommandPublisher);
  }

  @Test
  public void givenConversionRequest_tryToConvertToExistingDealWithAssociatedContact_shouldUpdateDeal() {
    // given
    long dealId = 1L;
    long contactId = 12L;
    String contactName = "Tony Stark";
    HashMap<String, Object> dealDetails = new HashMap<>();
    dealDetails.put("id", "1");
    ConversionRequest conversionRequest = new ConversionRequest(ConversionMode.EXISTING, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    DealDetail dealDetail = new DealDetail(dealDetails);
    given(dealService.getDealDetail(dealId)).willReturn(dealDetail);
    conversionHandler.setContactId(contactId);
    conversionHandler.setContactName(contactName);
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);
    // then
    ArgumentCaptor<Long> longArgumentCaptor = ArgumentCaptor.forClass(Long.class);
    ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
    verify(dealService, times(1)).update(longArgumentCaptor.capture(), mapArgumentCaptor.capture());
    Map<String, Object> value = mapArgumentCaptor.getValue();
    List<IdName> associatedContacts = (List<IdName>) value.get("associatedContacts");
    assertThat(associatedContacts.size()).isEqualTo(1);
    verifyZeroInteractions(dealCommandPublisher);

  }

  @Test
  public void givenConversionRequest_tryToConvertToExistingDealWithAssociatedContact_shouldSetValidRollbackAction() {
    // given
    long dealId = 100L;
    long contactId = 12L;

    String contactName = "Tony Stark";
    HashMap<String, Object> dealDetails = new HashMap<>();
    dealDetails.put("id", dealId);
    dealDetails.put(DealDetail.ASSOCIATED_CONTACT, new ArrayList() {{
      add(new IdName(1L, "Tony"));
      add(new IdName(2L, "Stark"));
    }});
    ConversionRequest conversionRequest = new ConversionRequest(ConversionMode.EXISTING, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    DealDetail dealDetail = new DealDetail(dealDetails);
    given(dealService.getDealDetail(dealId)).willReturn(dealDetail);
    conversionHandler.setContactId(contactId);
    conversionHandler.setContactName(contactName);
    // when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);
    dealConversionHandler.getRollbackConsumer().accept(dealId);
    // then
    ArgumentCaptor<ResetDealAssociatedContact> resetCommandArgumentCaptor = ArgumentCaptor.forClass(ResetDealAssociatedContact.class);
    verify(dealCommandPublisher, times(1)).resetAssociatedContact(resetCommandArgumentCaptor.capture());
    ResetDealAssociatedContact resetCommandRequest = resetCommandArgumentCaptor.getValue();
    assertThat(resetCommandRequest.getDealId()).isEqualTo(dealId);
    assertThat(resetCommandRequest.getTenantId()).isEqualTo(tenantId);
    assertThat(resetCommandRequest.getAssociatedContacts().size()).isEqualTo(2);
    assertThat(resetCommandRequest.getAssociatedContacts().stream().filter(idName -> idName.getId() == 1L).findFirst().get().getName())
        .isEqualTo("Tony");
    assertThat(resetCommandRequest.getAssociatedContacts().stream().filter(idName -> idName.getId() == 2L).findFirst().get().getName())
        .isEqualTo("Stark");

  }

  @Test
  public void givenConversionRequest_tryToConvertToExistingDealWithAssociatedCompany_shouldNotModify() {
    // given
    Long dealId = 100L;
    Long companyId = 12L;

    HashMap<String, Object> dealDetails = new HashMap<>();
    dealDetails.put("id", dealId);
    dealDetails.put("company", new IdName(companyId, "Liverpool FC"));

    ConversionRequest companyRequest = new ConversionRequest(CREATE, new HashMap<>());
    LeadConversionRequest conversionRequest =
        new LeadConversionRequest(new ConversionRequest(ConversionMode.EXISTING, dealDetails), null, companyRequest);
    DealDetail dealDetail = new DealDetail(dealDetails);
    given(dealService.getDealDetail(dealId)).willReturn(dealDetail);
    conversionHandler.setCompanyId(companyId);
    conversionHandler.setCompanyName("Liverpool FC");
    // when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(conversionRequest, conversionHandler);
    dealConversionHandler.getRollbackConsumer().accept(dealId);
    // then
    verify(dealCommandPublisher, never()).sendRemoveCompanyAssociationCommand(any(DealCommand.class));
  }

  @Test
  public void givenConversionRequest_tryToConvertToExistingDealWithAssociatedContactAndCompany_shouldSetValidRollbackAction() {
    // given
    long dealId = 100L;
    long contactId = 12L;
    long companyId = 200L;
//    long tenantId = 55L;
//    given(userFacade.getTenantId()).willReturn(tenantId);
    String contactName = "Tony Stark";
    HashMap<String, Object> dealDetails = new HashMap<>();
    dealDetails.put("id", dealId);
    dealDetails.put(DealDetail.ASSOCIATED_CONTACT, new ArrayList() {{
      add(new IdName(1L, "Tony"));
      add(new IdName(2L, "Stark"));
    }});
    ConversionRequest conversionRequest = new ConversionRequest(ConversionMode.EXISTING, dealDetails);
    ConversionRequest companyRequest = new ConversionRequest(CREATE, new HashMap<>());
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, companyRequest);
    DealDetail dealDetail = new DealDetail(dealDetails);
    given(dealService.getDealDetail(dealId)).willReturn(dealDetail);
    conversionHandler.setContactId(contactId);
    conversionHandler.setContactName(contactName);
    conversionHandler.setCompanyId(companyId);
    conversionHandler.setCompanyName("Liverpool FC");
    // when
    ConversionHandler dealConversionHandler = dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);
    dealConversionHandler.getRollbackConsumer().accept(dealId);
    // then
    ArgumentCaptor<ResetDealAssociatedContact> resetCommandArgumentCaptor = ArgumentCaptor.forClass(ResetDealAssociatedContact.class);
    verify(dealCommandPublisher, times(1)).resetAssociatedContact(resetCommandArgumentCaptor.capture());
    ResetDealAssociatedContact resetCommandRequest = resetCommandArgumentCaptor.getValue();
    assertThat(resetCommandRequest.getDealId()).isEqualTo(dealId);
    assertThat(resetCommandRequest.getTenantId()).isEqualTo(tenantId);
    assertThat(resetCommandRequest.getAssociatedContacts().size()).isEqualTo(2);
    assertThat(resetCommandRequest.getAssociatedContacts().stream().filter(idName -> idName.getId() == 1L).findFirst().get().getName())
        .isEqualTo("Tony");
    assertThat(resetCommandRequest.getAssociatedContacts().stream().filter(idName -> idName.getId() == 2L).findFirst().get().getName())
        .isEqualTo("Stark");

    ArgumentCaptor<DealCommand> dealCommandArgCaptor = ArgumentCaptor.forClass(DealCommand.class);
    verify(dealCommandPublisher, times(1)).sendRemoveCompanyAssociationCommand(dealCommandArgCaptor.capture());
    DealCommand command = dealCommandArgCaptor.getValue();
    assertThat(command.getDealId()).isEqualTo(dealId);
    assertThat(command.getTenantId()).isEqualTo(tenantId);
  }

  @Test
  public void givenConversionRequest_tryToCreateNewDealWithCompany_shouldCreate() {
    // given
    long dealId = 1L;
    HashMap dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "Deal1");
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);

    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    conversionHandler.setCompanyId(123L);
    conversionHandler.setCompanyName("Company 1");
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    conversionHandler.setContactId(123L);
    conversionHandler.setContactName("Tony Stark");
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);
    //then
    ArgumentCaptor<DealDTO> dealDTOArgumentCaptor = ArgumentCaptor.forClass(DealDTO.class);
    ArgumentCaptor<Map> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);

    verify(dealService, times(1)).createDeal(dealDTOArgumentCaptor.capture());
    DealDTO dealRequest = dealDTOArgumentCaptor.getValue();
    assertThat(dealRequest.getCompany().getId()).isEqualTo(123L);
    assertThat(dealRequest.getCompany().getName()).isEqualTo("Company 1");
    verifyZeroInteractions(dealCommandPublisher);
  }

  @Test
  public void givenLead_tryToConvertToNewDealWithCustomFieldValues_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    dealDetails.put("name", "My own Deal");
    Map<String,Object> customFieldValues = new HashMap<>();
    customFieldValues.put("myText","This is my Text");
    customFieldValues.put("myPicklist",new HashMap<String,Object>(){{
      put("id",458);
      put("name","One");
    }});
    dealDetails.put("customFieldValues", customFieldValues);
    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    assertThat(dealDTO.getValue().getName()).isEqualTo("My own Deal");
    assertThat(dealDTO.getValue().getCustomFieldValues()).isEqualTo(customFieldValues);
  }

  @Test
  public void givenLeadWithProducts_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    List<Map<String, Object>> products = new ArrayList<>();

    Map<String, Object> customFieldValues = new HashMap<>();
    Map<String, Object> pickListIdName = new HashMap<String, Object>() {{
      put("id", 897L);
      put("name", "One");
    }};
    customFieldValues.put("myPicklist", pickListIdName);

    Map<String, Object> product = new HashMap<>();
    product.put("id", 12);
    product.put("name", "CRM");
    product.put("quantity", 3.847);
    product.put("countryOfOrigin", new IdName(14L, "India"));
    product.put("hsnSacCode", "765391624");
    product.put("customFieldValues", customFieldValues);
    Map<String, Object> price = new HashMap<>();
    price.put("currencyId", 1);
    price.put("value", 13.50);
    product.put("price", price);
    Map<String, Object> discount = new HashMap<>();
    discount.put("type", "FIXED");
    discount.put("value", "3.5");
    product.put("discount", discount);
    product.put("category", new IdName(345L, "Finance"));

    products.add(product);
    dealDetails.put("products", products);

    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    List<Product> requestedProducts = dealDTO.getValue().getProducts();
    assertThat(requestedProducts).hasSize(1);
    Product actualProduct = requestedProducts.get(0);
    assertThat(actualProduct.getId()).isEqualTo(12);
    assertThat(actualProduct.getName()).isEqualTo("CRM");
    assertThat(actualProduct.getQuantity()).isEqualTo(3.847);
    assertThat(actualProduct.getPrice().getValue()).isEqualTo(13.50);
    assertThat(actualProduct.getPrice().getCurrencyId()).isEqualTo(1);
    assertThat(actualProduct.getDiscount().getType()).isEqualTo(Type.FIXED);
    assertThat(actualProduct.getDiscount().getValue()).isEqualTo(3.5);
    assertThat(actualProduct.getHsnSacCode()).isEqualTo("765391624");
    assertThat(actualProduct.getCountryOfOrigin().getId()).isEqualTo(14);
    assertThat(actualProduct.getCountryOfOrigin().getName()).isEqualTo("India");
    assertThat(actualProduct.getCategory().getId()).isEqualTo(345L);
    assertThat(actualProduct.getCategory().getName()).isEqualTo("Finance");
    assertThat(actualProduct.getCustomFieldValues().toString()).isEqualTo(
        "{myPicklist={name=One, id=897}}");
  }

  @Test
  public void givenLeadWithProductsAndQuantityNull_tryToConvertToNewDeal_shouldConvert() {
    // given
    long dealId = 1L;

    HashMap<String, Object> dealDetails = new HashMap<String, Object>();
    List<Map<String, Object>> products = new ArrayList<>();
    Map<String, Object> product = new HashMap<>();
    product.put("id", 12);
    product.put("name", "CRM");
    product.put("quantity", null);
    Map<String, Object> price = new HashMap<>();
    price.put("currencyId", 1);
    price.put("value", 13.50);
    product.put("price", price);
    Map<String, Object> discount = new HashMap<>();
    discount.put("type", "FIXED");
    discount.put("value", "3.5");
    product.put("discount", discount);

    products.add(product);
    dealDetails.put("products", products);

    ConversionRequest conversionRequest = new ConversionRequest(CREATE, dealDetails);
    LeadConversionRequest leadConversionRequest = new LeadConversionRequest(conversionRequest, null, null);
    ConversionHandler conversionHandler = new ConversionHandler();
    given(dealService.createDeal(any(DealDTO.class))).willReturn(new DealSummary(dealId));
    // when
    dealConversion.createOrChooseExistingDeal(leadConversionRequest, conversionHandler);

    // then
    ArgumentCaptor<DealDTO> dealDTO = ArgumentCaptor.forClass(DealDTO.class);
    verify(dealService, times(1)).createDeal(dealDTO.capture());
    List<Product> requestedProducts = dealDTO.getValue().getProducts();
    assertThat(requestedProducts).hasSize(1);
    Product actualProduct = requestedProducts.get(0);
    assertThat(actualProduct.getId()).isEqualTo(12);
    assertThat(actualProduct.getName()).isEqualTo("CRM");
    assertThat(actualProduct.getQuantity()).isEqualTo(1.0);
    assertThat(actualProduct.getPrice().getValue()).isEqualTo(13.50);
    assertThat(actualProduct.getPrice().getCurrencyId()).isEqualTo(1);
    assertThat(actualProduct.getDiscount().getType()).isEqualTo(Type.FIXED);
    assertThat(actualProduct.getDiscount().getValue()).isEqualTo(3.5);
  }
}