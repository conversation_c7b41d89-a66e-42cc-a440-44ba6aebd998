package com.sell.sales.service.contact;

import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.BDDMockito.given;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sell.sales.domain.layout.Layout;
import com.sell.sales.domain.layout.LayoutItem;
import com.sell.sales.exception.SalesException;
import com.sell.sales.service.client.config.EntityService;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.io.File;
import java.io.IOException;
import java.util.Optional;
import org.apache.commons.io.FileUtils;
import org.json.JSONException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@AutoConfigureEmbeddedDatabase
public class ContactLayoutServiceTest {

  @Autowired
  private ContactService contactService;
  @Autowired
  private ResourceLoader resourceLoader;
  @MockBean
  private EntityService entityService;
  @Autowired
  private ObjectMapper objectMapper;

  @Test
  public void givenLeadConversionLayoutRequest_shouldReturnLayout() throws IOException {
    // given
    String resourceAsString = getResourceAsString("classpath:contracts/contact/response/create-contact-layout.json");
    Layout contactLayout = objectMapper.readValue(resourceAsString, Layout.class);
    given(entityService.getCreateContactLayout()).willReturn(contactLayout);
    //when
    Layout layout = contactService.getLayoutForView("lead-conversion", "new");
    assertThat(layout.getLayoutItems()).hasSize(2);
    assertThat(
        layout.getLayoutItems().get(0).getLayoutItems().stream()
            .map(item -> item.getItem().getInternalName()).collect(toList()))
        .containsExactly("salutation", "firstName", "lastName", "emails", "phoneNumbers");
    assertThat(
        layout.getLayoutItems().get(1).getLayoutItems().stream()
            .map(item -> item.getItem().getInternalName()).collect(toList()))
        .containsExactly("timezone", "designation", "department", "dnd", "stakeholder", "address", "city", "state", "country", "zipcode", "facebook",
            "twitter", "linkedin", "campaign", "source", "subSource", "utmSource", "utmCampaign", "utmMedium", "utmContent", "utmTerm","cfMyName","customField");
  }

  @Test
  public void givenLeadConversionLayout_usingExistingContact_shouldReturnLayout() throws Exception {
    //when
    Layout layout = contactService.getLayoutForView("lead-conversion", "existing");
    //then
    LayoutItem existingContactsList = layout.getLayoutItems().stream()
        .flatMap(layoutItem -> layoutItem.getLayoutItems().stream())
        .filter(layoutItem -> "contact".equalsIgnoreCase(layoutItem.getItem().getInternalName()))
        .findAny().get();
    String layoutResponse = new ObjectMapper().writeValueAsString(existingContactsList);
    String expectedResponse = getResourceAsString("classpath:contracts/contact/response/lead-conversion-to-existing-contact-layout-response.json");
    JSONAssert.assertEquals(expectedResponse,layoutResponse, JSONCompareMode.STRICT);
  }

  @Test
  public void givenLeadConversionLayoutRequest_havingRequiredCustomField_shouldReturn() throws IOException {
    // given
    String resourceAsString = getResourceAsString("classpath:contracts/contact/response/required-custom-field-layout.json");
    Layout contactLayout = objectMapper.readValue(resourceAsString, Layout.class);
    given(entityService.getCreateContactLayout()).willReturn(contactLayout);
    //when
    Layout layout = contactService.getLayoutForView("lead-conversion", "new");
    assertThat(layout.getLayoutItems()).hasSize(2);
    assertThat(
        layout.getLayoutItems().get(0).getLayoutItems().stream()
            .map(item -> item.getItem().getInternalName()).collect(toList()))
        .containsExactly("salutation", "firstName", "lastName", "emails", "phoneNumbers", "requiredcustomfield");
    assertThat(
        layout.getLayoutItems().get(1).getLayoutItems().stream()
            .map(item -> item.getItem().getInternalName()).collect(toList()))
        .containsExactly("timezone", "designation", "department", "dnd", "stakeholder", "address", "city", "state", "country", "zipcode", "facebook",
            "twitter", "linkedin");
  }

  @Test
  public void givenLeadConversionLayoutRequest_forExistingContact_shouldReturn() {
    //when
    Layout layout = contactService.getLayoutForView("lead-conversion", "existing");
    assertThat(layout.getLayoutItems()).hasSize(1);
    assertThat(
        layout.getLayoutItems().get(0).getLayoutItems().stream()
            .map(item -> item.getItem().getInternalName()).collect(toList()))
        .containsExactly("contact");
  }

  @Test
  public void givenInvalidLeadConversionLayoutRequest_shouldThrow() {
    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> contactService.getLayoutForView("lead-conversion", "invalidMode"))
        .withMessage("layout.not.found");
  }

  private String getResourceAsString(String resourcePath) throws IOException {
    Resource resource = resourceLoader.getResource(resourcePath);
    File file = resource.getFile();
    return FileUtils.readFileToString(file, "UTF-8");
  }
}