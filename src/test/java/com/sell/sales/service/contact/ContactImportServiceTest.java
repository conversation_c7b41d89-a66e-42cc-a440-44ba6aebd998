package com.sell.sales.service.contact;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.BDDMockito.any;
import static org.mockito.BDDMockito.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.times;
import static org.mockito.BDDMockito.verify;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;

import com.sell.sales.controller.request.contact.ContactImportRequest;
import com.sell.sales.controller.request.contact.ContactImportRequestData;
import com.sell.sales.controller.request.lead.DuplicateRecordIdsDTO;
import com.sell.sales.controller.request.lead.ImportStrategy;
import com.sell.sales.controller.request.lead.ImportStrategyType;
import com.sell.sales.controller.response.contact.ContactImportResponse;
import com.sell.sales.controller.response.lead.ImportStatus;
import com.sell.sales.core.domain.Action;
import com.sell.sales.core.domain.PermissionDTO;
import com.sell.sales.domain.Contact;
import com.sell.sales.domain.ContactPhoneNumber;
import com.sell.sales.domain.ContactUtm;
import com.sell.sales.dto.LookUp;
import com.sell.sales.dto.mapper.ContactMapper;
import com.sell.sales.entity.model.EntityPreferredCollisionStrategy;
import com.sell.sales.entity.model.EntityUniquenessStrategy;
import com.sell.sales.entity.model.PhoneType;
import com.sell.sales.exception.SalesErrorCodes;
import com.sell.sales.exception.SalesException;
import com.sell.sales.infra.mq.event.Metadata;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.client.company.CompanyService;
import com.sell.sales.service.client.company.response.LookupResponse;
import com.sell.sales.service.client.config.EntityService;
import com.sell.sales.service.client.config.request.EntityUniquenessStrategyDTO;
import com.sell.sales.service.client.iam.IamService;
import com.sell.sales.service.client.iam.web.response.UserResponse;
import com.sell.sales.utils.validator.ContactValidator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ContactImportServiceTest {

  @InjectMocks
  private ContactImportService contactImportService;
  @Mock
  private ContactService contactService;
  @Mock
  private ContactMapper contactMapper;
  @Mock
  private CompanyService companyService;
  @Mock
  private UserFacade userFacade;
  @Mock
  private EntityService entityService;
  @Mock
  private ContactValidator contactValidator;
  @Mock
  private ContactMergeService contactMergeService;
  @Mock
  private IamService iamService;

  @Test
  public void givenContactImportRequest_withCompanyName_shouldCreateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated).isNotNull();
    assertThat(contactToBeCreated.getFirstName()).isEqualTo("firstName");
    assertThat(contactToBeCreated.getLastName()).isEqualTo("lastName");
    assertThat(contactToBeCreated.getCompany()).isEqualTo(12L);
    assertThat(contactToBeCreated.getImportedBy()).isEqualTo(10L);
  }

  @Test
  public void givenContactImportRequest_withUTMFields_shouldCreateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setSubSource("SubSource");
    contactImportRequest.setUtmCampaign("UtmCampaign");
    contactImportRequest.setUtmContent("UtmContent");
    contactImportRequest.setUtmMedium("UtmMedium");
    contactImportRequest.setUtmTerm("UtmTerm");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    ContactUtm contactUtm = new ArrayList<>(contactToBeCreated.getContactUtms()).get(0);
    assertThat(contactToBeCreated).isNotNull();
    assertThat(contactToBeCreated.getFirstName()).isEqualTo("firstName");
    assertThat(contactToBeCreated.getLastName()).isEqualTo("lastName");
    assertThat(contactToBeCreated.getCompany()).isEqualTo(12L);
    assertThat(contactToBeCreated.getImportedBy()).isEqualTo(10L);
    assertThat(contactUtm.getUtmTerm()).isEqualTo("UtmTerm");
    assertThat(contactUtm.getUtmMedium()).isEqualTo("UtmMedium");
    assertThat(contactUtm.getUtmCampaign()).isEqualTo("UtmCampaign");
    assertThat(contactUtm.getUtmContent()).isEqualTo("UtmContent");
    assertThat(contactUtm.getSubSource()).isEqualTo("SubSource");
  }

  @Test
  public void givenContactImportRequest_withoutCompanyName_shouldCreateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated).isNotNull();
    assertThat(contactToBeCreated.getFirstName()).isEqualTo("firstName");
    assertThat(contactToBeCreated.getLastName()).isEqualTo("lastName");
    assertThat(contactToBeCreated.getImportedBy()).isEqualTo(10L);
    verifyNoMoreInteractions(companyService);
  }

  @Test
  public void givenContactImportRequest_withNonExistingCompanyName_shouldThrow() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.emptyList());
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when //then
    assertThatExceptionOfType(SalesException.class).isThrownBy(() -> contactImportService.importContact(contactImportRequestData))
        .withMessage("company.not.found");
  }

  @Test
  public void givenContactImportRequest_withIgnoreDuplicatesImportStrategy_shouldSkipContactCreation() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.IGNORE_DUPLICATE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    DuplicateRecordIdsDTO duplicateRecordIdsDTO = new DuplicateRecordIdsDTO(Arrays.asList(23L, 12L), "duplicate contacts found");
    given(contactValidator.getDuplicateRecordIdsByUniquenessStrategy(any(), eq("EMAIL"))).willReturn(Optional.of(duplicateRecordIdsDTO));
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.SKIPPED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("duplicate contacts found");
    assertThat(contactImportResponse.getContactId()).isEqualTo(23L);
  }

  @Test
  public void givenContactImportRequest_withIgnoreDuplicatesImportStrategyAndNoDuplicatesFound_shouldCreateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.IGNORE_DUPLICATE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    given(contactValidator.getDuplicateRecordIdsByUniquenessStrategy(any(), eq("EMAIL"))).willReturn(Optional.empty());
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
  }

  @Test
  public void givenContactImportRequest_withMergeDuplicateByReplacingDataStrategy_shouldReplaceAndUpdateContact() {
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_BY_REPLACING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    given(contactValidator.getDuplicateRecord(any(), eq("EMAIL"))).willReturn(Optional.of(contact));
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(contactMergeService.replaceData(any(), any())).willReturn(contact);
    given(contactService.update(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.UPDATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Updated with replacing data");
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).update(contactArgumentCaptor.capture());
    Contact actualContact = contactArgumentCaptor.getValue();
    assertThat(actualContact.getCompany()).isEqualTo(12L);
  }

  @Test
  public void givenContactImportRequest_withMergeDuplicateByReplacingDataStrategyAndNoDuplicateFound_shouldCreateContact() {
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_BY_REPLACING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    given(contactValidator.getDuplicateRecord(any(), eq("EMAIL"))).willReturn(Optional.empty());
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
  }

  @Test
  public void givenContactImportRequest_withMergeDuplicateByMissingDataDataStrategy_shouldMergeAndUpdateContact() {
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_WITH_MISSING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    given(contactValidator.getDuplicateRecord(any(), eq("EMAIL"))).willReturn(Optional.of(contact));
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(contactMergeService.addMissingData(any(), any())).willReturn(contact);
    given(contactService.update(any(Contact.class), any(Metadata.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.UPDATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Updated with add missing data");
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).update(contactArgumentCaptor.capture(), anyObject());
    Contact actualContact = contactArgumentCaptor.getValue();
    assertThat(actualContact.getCompany()).isEqualTo(12L);
  }

  @Test
  public void givenContactImportRequest_withMergeDuplicateByMissingDataStrategyAndNoDuplicateFound_shouldCreateContact() {
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_WITH_MISSING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.EMAIL, EntityPreferredCollisionStrategy.EMAIL));
    given(contactValidator.getDuplicateRecord(any(), eq("EMAIL"))).willReturn(Optional.empty());
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
  }

  @Test
  public void givenContactImportRequest_withOwnerEmail_shouldCreateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
  }

  @Test
  public void givenContactImportRequest_withInvalidOwnerEmail_shouldCreateContactWithoutExplicitOwner() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.isExplicitOwnerId()).isFalse();
    verifyZeroInteractions(iamService);
  }

  @Test
  public void givenContactImportRequest_withOwnerEmailWhichDoesNotExists_shouldThrow() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willThrow(new SalesException(SalesErrorCodes.INVALID_OWNER_EMAIL));
    //when //then
    assertThatExceptionOfType(SalesException.class).isThrownBy(() -> contactImportService.importContact(contactImportRequestData))
        .withMessage("import.invalid.owner.email");
  }

  @Test
  public void givenContactImportRequest_withInactiveOwnerEmail_shouldThrow() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", false, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when //then
    assertThatExceptionOfType(SalesException.class).isThrownBy(() -> contactImportService.importContact(contactImportRequestData))
        .withMessage("user.is.inactive");
  }

  @Test
  public void givenContactImportRequest_withOwnerEmailAndHasNoReadPermissionOnContact_shouldThrow() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(false);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when //then
    assertThatExceptionOfType(SalesException.class).isThrownBy(() -> contactImportService.importContact(contactImportRequestData))
            .withMessage("user.cannot.read.contact");
  }

  @Test
  public void givenContactToImportWithCreatedByEmail_shouldCreate() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    contactImportRequest.setCreatedByEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.getCreatedBy()).isEqualTo(14L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
  }

  @Test
  public void givenContactToImportWithUpdatedByEmail_shouldCreate() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    contactImportRequest.setUpdatedByEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.getUpdatedBy()).isEqualTo(14L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
  }

  @Test
  public void givenContactToImportWithSameCreatedByAndUpdatedByEmail_shouldGetUserOnly1() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    contactImportRequest.setCreatedByEmail("<EMAIL>");
    contactImportRequest.setUpdatedByEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    verify(iamService, times(1)).getUserWithPermissionByEmailId("<EMAIL>");
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.getCreatedBy()).isEqualTo(14L);
    assertThat(contactToBeCreated.getUpdatedBy()).isEqualTo(14L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
  }

  @Test
  public void givenLeadWithCreatedByInActiveAndWithoutUpdatedBy_shouldSetLoginUserAsUpdatedBy() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    contactImportRequest.setCreatedByEmail("<EMAIL>");
    contactImportRequest.setUpdatedByEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    UserResponse createdByUser = new UserResponse(16L, "tony", "stark", "<EMAIL>", false, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(createdByUser);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.getUpdatedBy()).isEqualTo(16L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
    assertThat(contactToBeCreated.getCreatedBy()).isEqualTo(14L);
  }

  @Test
  public void givenLeadWithUpdatedByInActiveAndWithoutCreatedBy_shouldSetLoginUserAsCreatedBy() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    contactImportRequest.setOwnerEmail("<EMAIL>");
    contactImportRequest.setUpdatedByEmail("<EMAIL>");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    PermissionDTO permissionDTO = new PermissionDTO();
    permissionDTO.setName("contact");
    Action action = new Action().read(true);
    permissionDTO.setAction(action);
    UserResponse userResponse = new UserResponse(14L, "tony", "stark", "<EMAIL>", true, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(userResponse);
    UserResponse updatedByUser = new UserResponse(16L, "tony", "stark", "<EMAIL>", false, Collections.singleton(permissionDTO));
    given(iamService.getUserWithPermissionByEmailId("<EMAIL>")).willReturn(updatedByUser);
    //when
    contactImportService.importContact(contactImportRequestData);
    //then
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated.getOwnerId()).isEqualTo(14L);
    assertThat(contactToBeCreated.getCreatedBy()).isEqualTo(10L);
    assertThat(contactToBeCreated.getUpdatedBy()).isEqualTo(16L);
    assertThat(contactToBeCreated.isExplicitOwnerId()).isTrue();
  }

  @Test
  public void givenContactImportRequest_withPhoneNumbers_shouldCreateContactWithContactPhoneNumbers() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.NONE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    ContactPhoneNumber contactPhoneNumber1 = new ContactPhoneNumber();
    contactPhoneNumber1.setId(17L);
    contactPhoneNumber1.setType(PhoneType.MOBILE);
    contactPhoneNumber1.setCode("IN");
    contactPhoneNumber1.setValue("9876543210");
    contactPhoneNumber1.setDialCode("+91");
    contactPhoneNumber1.setContact(contact);
    contactPhoneNumber1.setPrimary(true);
    ContactPhoneNumber contactPhoneNumber2 = new ContactPhoneNumber();
    contactPhoneNumber2.setId(18L);
    contactPhoneNumber2.setType(PhoneType.MOBILE);
    contactPhoneNumber2.setCode("IN");
    contactPhoneNumber2.setValue("9876543213");
    contactPhoneNumber2.setDialCode("+91");
    contactPhoneNumber2.setContact(contact);
    contactPhoneNumber2.setPrimary(false);
    Set<ContactPhoneNumber> contactPhoneNumbers = new HashSet<>();
    contactPhoneNumbers.add(contactPhoneNumber1);
    contactPhoneNumbers.add(contactPhoneNumber2);
    contact.setContactPhoneNumbers(contactPhoneNumbers);
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.CREATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Created new contact");
    ArgumentCaptor<Contact> contactArgumentCaptor = ArgumentCaptor.forClass(Contact.class);
    verify(contactService, times(1)).create(contactArgumentCaptor.capture());
    Contact contactToBeCreated = contactArgumentCaptor.getValue();
    assertThat(contactToBeCreated).isNotNull();
    assertThat(contactToBeCreated.getFirstName()).isEqualTo("firstName");
    assertThat(contactToBeCreated.getLastName()).isEqualTo("lastName");
    assertThat(contactToBeCreated.getCompany()).isEqualTo(12L);
    assertThat(contactToBeCreated.getImportedBy()).isEqualTo(10L);
    assertThat(contactToBeCreated.getContactPhoneNumbers()).isNotEmpty();
    assertThat(
        contactToBeCreated.getContactPhoneNumbers().stream().map(phone -> phone.getValue()).collect(Collectors.toList())).containsExactlyInAnyOrder(
        "9876543210", "9876543213");
  }

  @Test
  public void givenContactImportRequest_withPhoneNumbersAndIgnoreDuplicateStrategy_shouldIgnoreCreate() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.IGNORE_DUPLICATE);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    ContactPhoneNumber contactPhoneNumber1 = new ContactPhoneNumber();
    contactPhoneNumber1.setId(17L);
    contactPhoneNumber1.setType(PhoneType.MOBILE);
    contactPhoneNumber1.setCode("IN");
    contactPhoneNumber1.setValue("9876543210");
    contactPhoneNumber1.setDialCode("+91");
    contactPhoneNumber1.setContact(contact);
    contactPhoneNumber1.setPrimary(true);
    ContactPhoneNumber contactPhoneNumber2 = new ContactPhoneNumber();
    contactPhoneNumber2.setId(18L);
    contactPhoneNumber2.setType(PhoneType.MOBILE);
    contactPhoneNumber2.setCode("IN");
    contactPhoneNumber2.setValue("9876543213");
    contactPhoneNumber2.setDialCode("+91");
    contactPhoneNumber2.setContact(contact);
    contactPhoneNumber2.setPrimary(false);
    Set<ContactPhoneNumber> contactPhoneNumbers = new HashSet<>();
    contactPhoneNumbers.add(contactPhoneNumber1);
    contactPhoneNumbers.add(contactPhoneNumber2);
    contact.setContactPhoneNumbers(contactPhoneNumbers);
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.create(any(Contact.class))).willReturn(contact);
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.PHONE, EntityPreferredCollisionStrategy.PHONE));
    DuplicateRecordIdsDTO duplicateRecordIdsDTO = new DuplicateRecordIdsDTO(Arrays.asList(23L, 12L), "duplicate contacts found");
    given(contactValidator.getDuplicateRecordIdsByUniquenessStrategy(any(), eq("PHONE"))).willReturn(Optional.of(duplicateRecordIdsDTO));
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(23L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.SKIPPED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("duplicate contacts found");
  }

  @Test
  public void givenContactImportRequest_withPhoneNumbersAndMergeDuplicateStrategy_shouldMergeAndUpdateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_WITH_MISSING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    ContactPhoneNumber contactPhoneNumber1 = new ContactPhoneNumber();
    contactPhoneNumber1.setId(17L);
    contactPhoneNumber1.setType(PhoneType.MOBILE);
    contactPhoneNumber1.setCode("IN");
    contactPhoneNumber1.setValue("9876543210");
    contactPhoneNumber1.setDialCode("+91");
    contactPhoneNumber1.setContact(contact);
    contactPhoneNumber1.setPrimary(true);
    ContactPhoneNumber contactPhoneNumber2 = new ContactPhoneNumber();
    contactPhoneNumber2.setId(18L);
    contactPhoneNumber2.setType(PhoneType.MOBILE);
    contactPhoneNumber2.setCode("IN");
    contactPhoneNumber2.setValue("9876543213");
    contactPhoneNumber2.setDialCode("+91");
    contactPhoneNumber2.setContact(contact);
    contactPhoneNumber2.setPrimary(false);
    Set<ContactPhoneNumber> contactPhoneNumbers = new HashSet<>();
    contactPhoneNumbers.add(contactPhoneNumber1);
    contactPhoneNumbers.add(contactPhoneNumber2);
    contact.setContactPhoneNumbers(contactPhoneNumbers);
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.update(any(Contact.class), any(Metadata.class))).willReturn(contact);
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.PHONE, EntityPreferredCollisionStrategy.PHONE));
    DuplicateRecordIdsDTO duplicateRecordIdsDTO = new DuplicateRecordIdsDTO(Arrays.asList(23L, 12L), "duplicate contacts found");
    given(contactValidator.getDuplicateRecord(any(), eq("PHONE"))).willReturn(Optional.of(contact));
    given(contactMergeService.addMissingData(any(), any())).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.UPDATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Updated with add missing data");
  }

  @Test
  public void givenContactImportRequest_withPhoneNumbersAndMergeDuplicateReplaceDateStrategy_shouldReplaceAndUpdateContact() {
    //given
    ImportStrategy importStrategy = new ImportStrategy(ImportStrategyType.MERGE_DUPLICATE_BY_REPLACING_DATA);
    ContactImportRequest contactImportRequest = new ContactImportRequest();
    contactImportRequest.setCompanyName("company");
    contactImportRequest.setFirstName("firstName");
    contactImportRequest.setLastName("lastName");
    ContactImportRequestData contactImportRequestData = new ContactImportRequestData(1L, contactImportRequest, importStrategy);
    Contact contact = new Contact();
    contact.setId(1L);
    contact.setFirstName("firstName");
    contact.setLastName("lastName");
    ContactPhoneNumber contactPhoneNumber1 = new ContactPhoneNumber();
    contactPhoneNumber1.setId(17L);
    contactPhoneNumber1.setType(PhoneType.MOBILE);
    contactPhoneNumber1.setCode("IN");
    contactPhoneNumber1.setValue("9876543210");
    contactPhoneNumber1.setDialCode("+91");
    contactPhoneNumber1.setContact(contact);
    contactPhoneNumber1.setPrimary(true);
    ContactPhoneNumber contactPhoneNumber2 = new ContactPhoneNumber();
    contactPhoneNumber2.setId(18L);
    contactPhoneNumber2.setType(PhoneType.MOBILE);
    contactPhoneNumber2.setCode("IN");
    contactPhoneNumber2.setValue("9876543213");
    contactPhoneNumber2.setDialCode("+91");
    contactPhoneNumber2.setContact(contact);
    contactPhoneNumber2.setPrimary(false);
    Set<ContactPhoneNumber> contactPhoneNumbers = new HashSet<>();
    contactPhoneNumbers.add(contactPhoneNumber1);
    contactPhoneNumbers.add(contactPhoneNumber2);
    contact.setContactPhoneNumbers(contactPhoneNumbers);
    given(contactMapper.fromContactImportRequestToContact(contactImportRequest)).willReturn(contact);
    LookupResponse lookupResponse = new LookupResponse(Collections.singletonList(new LookUp(12L, "company")));
    given(companyService.getCompanyIdsByName("contact-import", "name:company"))
        .willReturn(lookupResponse);
    given(userFacade.getUserId()).willReturn(10L);
    given(contactService.update(any(Contact.class))).willReturn(contact);
    given(entityService.getUniquenessConfiguration(eq("CONTACT")))
        .willReturn(new EntityUniquenessStrategyDTO(EntityUniquenessStrategy.PHONE, EntityPreferredCollisionStrategy.PHONE));
    DuplicateRecordIdsDTO duplicateRecordIdsDTO = new DuplicateRecordIdsDTO(Arrays.asList(23L, 12L), "duplicate contacts found");
    given(contactValidator.getDuplicateRecord(any(), eq("PHONE"))).willReturn(Optional.of(contact));
    given(contactMergeService.replaceData(any(), any())).willReturn(contact);
    //when
    ContactImportResponse contactImportResponse = contactImportService.importContact(contactImportRequestData);
    //then
    assertThat(contactImportResponse).isNotNull();
    assertThat(contactImportResponse.getContactId()).isEqualTo(1L);
    assertThat(contactImportResponse.getImportStatus()).isEqualTo(ImportStatus.UPDATED);
    assertThat(contactImportResponse.getImportMessage()).isEqualTo("Updated with replacing data");
  }
}