package com.sell.sales.pipeline.service;

import static com.sell.sales.domain.ForecastingType.CLOSED_WON;
import static com.sell.sales.domain.ForecastingType.OPEN;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.sell.sales.controller.response.pipeline.PipelineResponse;
import com.sell.sales.controller.response.pipeline.PipelineStageResponse;
import com.sell.sales.core.domain.EntityType;
import com.sell.sales.core.domain.Source;
import com.sell.sales.core.event.CoreEvent;
import com.sell.sales.domain.ForecastingType;
import com.sell.sales.domain.Lead;
import com.sell.sales.domain.MetaInfo;
import com.sell.sales.entity.service.IdNameSearchService;
import com.sell.sales.entity.service.SalesIdNameResolver;
import com.sell.sales.event.SalesEventEmitter;
import com.sell.sales.event.internal.PipelineEvent;
import com.sell.sales.event.internal.PipelinePayload;
import com.sell.sales.event.internal.PipelineStagePayload;
import com.sell.sales.exception.SalesException;
import com.sell.sales.infra.mq.event.LeadEvent;
import com.sell.sales.pipeline.domain.LeadPipeline;
import com.sell.sales.pipeline.domain.LeadPipelineStage;
import com.sell.sales.pipeline.repository.LeadPipelineRepository;
import com.sell.sales.pipeline.repository.LeadPipelineStageRepository;
import com.sell.sales.pipeline.web.PipelineStageStatus;
import com.sell.sales.pipeline.web.response.LeadPipelineResponse;
import com.sell.sales.repository.LeadRepository;
import com.sell.sales.security.UserFacade;
import com.sell.sales.service.PipelineService;
import com.sell.sales.service.client.config.EntityService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.BDDMockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LeadPipelineServiceTest {

  @InjectMocks
  private LeadPipelineService leadPipelineService;
  @Mock
  private LeadRepository leadRepository;
  @Mock
  private UserFacade userFacade;
  @Mock
  private PipelineService pipelineService;
  @Mock
  private LeadPipelineRepository leadPipelineRepository;
  @Mock
  private LeadPipelineStageRepository leadPipelineStageRepository;
  @Mock
  private SalesEventEmitter salesEventEmitter;
  @Mock private EntityService entityService;
  @Mock private IdNameSearchService idNameSearchService;
  @Mock
  private SalesIdNameResolver salesIdNameResolver;


  @Test
  public void givenExistingLeadWithNoAttachedPipeline_whenTryingToSetStage_shouldThrow() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long stageId = 66L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    Lead lead = aLead(null, null);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> leadPipelineService.setActiveStage(leadId, null, stageId, null))
        .withMessage("pipeline.not.attached");
  }

  @Test
  public void givenExistingLead_whenTryingToSetNonExistingStage_shouldThrow() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    Lead lead = aLead(pipelineId, 15L);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    List<PipelineStageResponse> pipelineStages = pipelineStagesForPipeline(15L, 17L);
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    // when
    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> leadPipelineService.setActiveStage(leadId, pipelineId, 19L, null))
        .withMessage("pipeline.stage.not.exists");
  }

  @Test
  public void givenExistingLead_whenTryingToSetInActivePipeline_shouldThrow() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    Lead lead = aLead(pipelineId, 15L);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    List<PipelineStageResponse> pipelineStages = pipelineStagesForPipeline(15L, 17L);
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    pipeline.setActive(false);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    // when
    assertThatExceptionOfType(SalesException.class)
        .isThrownBy(() -> leadPipelineService.setActiveStage(leadId, pipelineId, 19L, null))
        .withMessage("pipeline.not.active");
  }

  @Test
  public void givenExistingLead_shouldSetTheGivenStageAsActive() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;
    long userId = 54;

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(userId);
    Lead lead = aLead(pipelineId, 15L);
    lead.setId(11L);
    lead.setOwnerId(tenantId);
    lead.setTenantId(11L);
    lead.setVersion(4);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    long stageIdToActivate = 17L;
    List<PipelineStageResponse> pipelineStages = pipelineStagesForPipeline(15L, stageIdToActivate);
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    LeadPipeline updatedLeadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);

    LeadPipeline leadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);
    given(leadPipeline.setActiveStage(stageIdToActivate, null)).willReturn(updatedLeadPipeline);

    given(leadPipelineRepository.findByTenantIdAndLeadIdAndPipelineId(tenantId, leadId, pipelineId))
        .willReturn(Optional.of(leadPipeline));
    given(leadPipelineRepository.save(any(LeadPipeline.class))).willReturn(updatedLeadPipeline);
    given(leadRepository.saveAndFlush(lead)).willReturn(lead);
    ArgumentCaptor<Lead> leadCaptor = ArgumentCaptor.forClass(Lead.class);
    ArgumentCaptor<CoreEvent> coreEventCaptor = ArgumentCaptor.forClass(CoreEvent.class);

    // when
    LeadPipelineResponse leadPipelineResponse =
        leadPipelineService.setActiveStage(leadId, pipelineId, stageIdToActivate, null);

    // then
    assertThat(leadPipelineResponse.getId()).isEqualTo(pipelineId);
    assertThat(leadPipelineResponse.getName()).isEqualTo(pipeline.getName());
    assertThat(leadPipelineResponse.getLostReasons()).isEqualTo(asList(pipeline.getLostReasons()));
    assertThat(leadPipelineResponse.getUnqualifiedReasons())
        .isEqualTo(asList(pipeline.getUnqualifiedReasons()));

    verify(leadPipelineRepository, times(1)).save(any(LeadPipeline.class));
    verify(salesEventEmitter, times(1)).emit(coreEventCaptor.capture(), leadCaptor.capture());
    verify(leadRepository, atMost(1)).
        updatePipelineInfoToLead(leadId, tenantId, pipelineId, stageIdToActivate, null, OPEN, null, new Date(), userId);
    CoreEvent event = coreEventCaptor.getValue();
    assertThat(event.getEventName()).isEqualTo("sales.lead.updated");
    assertThat(event.getDescription()).isEqualTo("lead updated");
    assertThat(event.getMessage()).isEqualTo(Lead.class);

  }

  @Test
  public void givenExistingLead_tryToClosePipeline_shouldSetActualClosureDate() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;
    Long userId = 55L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(userId);
    Lead lead = aLead(pipelineId, 15L);
    lead.setId(11L);
    lead.setOwnerId(tenantId);
    lead.setTenantId(11L);
    lead.setVersion(4);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    long stageIdToActivate = 17L;
    List<PipelineStageResponse> pipelineStages = Collections.singletonList(aPipelineStageResponse(17L, 2, CLOSED_WON));
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    LeadPipeline updatedLeadPipeline = mock(LeadPipeline.class);
    LeadPipelineStage closedStage = aLeadPipelineStage(17L, CLOSED_WON);
    Date completedAt = new Date();
    given(closedStage.getCompletedAt()).willReturn(completedAt);
    given(updatedLeadPipeline.getStages()).willReturn(Collections.singletonList(closedStage));
    LeadPipeline leadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);
    given(leadPipeline.setActiveStage(stageIdToActivate, null)).willReturn(updatedLeadPipeline);

    given(leadPipelineRepository.findByTenantIdAndLeadIdAndPipelineId(tenantId, leadId, pipelineId))
        .willReturn(Optional.of(leadPipeline));
    given(leadPipelineRepository.save(any(LeadPipeline.class))).willReturn(updatedLeadPipeline);
    given(leadRepository.saveAndFlush(lead)).willReturn(lead);
    ArgumentCaptor<CoreEvent> coreEventCaptor = ArgumentCaptor.forClass(CoreEvent.class);

    // when
    leadPipelineService.setActiveStage(leadId, pipelineId, stageIdToActivate, null);

    // then
    verify(leadRepository, atMost(1)).
        updatePipelineInfoToLead(leadId, tenantId, pipelineId, stageIdToActivate, null, CLOSED_WON, completedAt, new Date(), userId);
  }

  @Test
  public void givenExistingLead_tryToOpenTheClosedPipeline_shouldSetActualClosureDateToNull() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;
    long userId = 54L;
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(userId);
    Lead lead = aLead(pipelineId, 15L);
    lead.setId(11L);
    lead.setOwnerId(tenantId);
    lead.setTenantId(11L);
    lead.setVersion(4);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    long stageIdToActivate = 17L;
    List<PipelineStageResponse> pipelineStages = Collections.singletonList(aPipelineStageResponse(17L, 2, CLOSED_WON));
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    LeadPipeline updatedLeadPipeline = mock(LeadPipeline.class);
    LeadPipelineStage openStage = aLeadPipelineStage(17L, OPEN);
    given(updatedLeadPipeline.getStages()).willReturn(Collections.singletonList(openStage));
    LeadPipeline leadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);
    given(leadPipeline.setActiveStage(stageIdToActivate, null)).willReturn(updatedLeadPipeline);

    given(leadPipelineRepository.findByTenantIdAndLeadIdAndPipelineId(tenantId, leadId, pipelineId))
        .willReturn(Optional.of(leadPipeline));
    given(leadPipelineRepository.save(any(LeadPipeline.class))).willReturn(updatedLeadPipeline);
    given(leadRepository.saveAndFlush(lead)).willReturn(lead);
    // when
    leadPipelineService.setActiveStage(leadId, pipelineId, stageIdToActivate, null);

    // then
    verify(leadRepository, atMost(1)).
        updatePipelineInfoToLead(leadId, tenantId, pipelineId, stageIdToActivate, null, OPEN, null, new Date(), userId);
  }

  @Test
  public void givenLeadWithNoPipeline_shouldGetEmptyPipelineStageWhenTryingToClose() {
    // given
    long leadId = 21L;
    long tenantId = 44L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    Lead lead = new Lead();
    lead.setId(leadId);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    //when
    Optional<Long> stageId = leadPipelineService.closePipelineForConvertedLead(leadId, null);

    //then
    assertThat(stageId.isPresent()).isFalse();
  }

  @Test
  public void givenLeadWithPipeline_shouldClosePipelineAndReturnWonStageId() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;
    final long actualWonStageId = 10L;

    given(userFacade.getTenantId()).willReturn(tenantId);
    Lead lead = aLead(pipelineId, 8L);
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    final PipelineResponse pipelineResponse = mock(PipelineResponse.class);
    given(pipelineResponse.isActive()).willReturn(true);
    given(pipelineResponse.getId()).willReturn(pipelineId);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipelineResponse);

    final LeadPipeline leadPipeline = mock(LeadPipeline.class);
    given(leadPipelineRepository.findByTenantIdAndLeadIdAndPipelineId(tenantId, leadId, pipelineId)).willReturn(Optional.of(leadPipeline));
    given(leadPipeline.withStages(any())).willReturn(leadPipeline);

    final LeadPipeline updatedLeadPipeline = mock(LeadPipeline.class);
    given(leadPipeline.closePipelineForConvertedLead()).willReturn(Optional.of(updatedLeadPipeline));

    final LeadPipeline persistedLeadPipeline = mock(LeadPipeline.class);
    given(leadPipelineRepository.save(updatedLeadPipeline)).willReturn(persistedLeadPipeline);
    given(persistedLeadPipeline.findClosedWonStageId()).willReturn(Optional.of(actualWonStageId));

    //when
    Long pipelineStageId = leadPipelineService.closePipelineForConvertedLead(leadId, pipelineId).get();

    //then
    assertThat(pipelineStageId).isEqualTo(actualWonStageId);
  }

  @Test
  public void givenExistingLead_shouldSetTheGivenStageAsActive_andUpdateMetaInfo() {
    // given
    long leadId = 21L;
    long tenantId = 44L;
    long pipelineId = 9L;
    long userId = 58L;
    given(userFacade.getTenantId()).willReturn(tenantId);
    given(userFacade.getUserId()).willReturn(userId);
    Lead lead = aLead(pipelineId, 15L);
    lead.setId(11L);
    lead.setOwnerId(tenantId);
    lead.setTenantId(11L);
    lead.setVersion(4);
    lead.setMetaInfo(MetaInfo.create(lead, new Source()).update(new Date(), new Date(), new Date()));
    lead.setUpdatedAt(new Date());
    given(leadRepository.findByIdAndTenantId(leadId, tenantId)).willReturn(Optional.of(lead));

    long stageIdToActivate = 17L;
    List<PipelineStageResponse> pipelineStages = pipelineStagesForPipeline(15L, stageIdToActivate);
    PipelineResponse pipeline = aPipeline(pipelineId, pipelineStages);
    given(pipelineService.getPipeline(pipelineId)).willReturn(pipeline);

    LeadPipeline updatedLeadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);

    LeadPipeline leadPipeline = aMockedLeadPipelineWithStages(pipelineId, 17L);
    given(leadPipeline.setActiveStage(stageIdToActivate, null)).willReturn(updatedLeadPipeline);

    given(leadPipelineRepository.findByTenantIdAndLeadIdAndPipelineId(tenantId, leadId, pipelineId))
        .willReturn(Optional.of(leadPipeline));
    given(leadPipelineRepository.save(any(LeadPipeline.class))).willReturn(updatedLeadPipeline);
    given(leadRepository.saveAndFlush(lead)).willReturn(lead);
    ArgumentCaptor<LeadEvent> leadCaptor = ArgumentCaptor.forClass(LeadEvent.class);
    ArgumentCaptor<CoreEvent> coreEventCaptor = ArgumentCaptor.forClass(CoreEvent.class);

    // when
    leadPipelineService.setActiveStage(leadId, pipelineId, stageIdToActivate, null);

    // then
    verify(leadPipelineRepository, times(1)).save(any(LeadPipeline.class));
    verify(salesEventEmitter, times(1)).emit(coreEventCaptor.capture(), leadCaptor.capture());
    verify(leadRepository, atMost(1)).
        updatePipelineInfoToLead(leadId, tenantId, pipelineId, stageIdToActivate, null, OPEN, null, new Date(), userId);
    CoreEvent event = coreEventCaptor.getValue();
    assertThat(event.getEventName()).isEqualTo("sales.lead.updated");
    assertThat(event.getDescription()).isEqualTo("lead updated");
    assertThat(event.getMessage()).isEqualTo(Lead.class);

    LeadEvent updatedLead = leadCaptor.getValue();
    assertThat(updatedLead.getId()).isEqualTo(lead.getId());
    assertThat(updatedLead.getIsNew()).isFalse();
    assertThat(updatedLead.getLatestActivityCreatedAt()).isBeforeOrEqualTo(new Date());
    assertThat(updatedLead.getMeetingScheduledOn()).isEqualTo(lead.getMetaInfo().getMeetingScheduledOn());
    assertThat(updatedLead.getTaskDueOn()).isEqualTo(lead.getMetaInfo().getTaskDueOn());
  }

  @Test
  public void givenPipelineEvent_whenNewStageAdded_shouldUpdateExistingLeadForSamePipeline() {
    //given
    long updatedBy = 22L;
    List<PipelineStagePayload> stages = new ArrayList<>();
    PipelineStagePayload existingStage = new PipelineStagePayload(1L, 12L, "Stage 1", "Descrption 1", OPEN, 1, 12);
    PipelineStagePayload newStage1 = new PipelineStagePayload(2L, 12L, "Stage 2", "Descrption 2", OPEN, 2, 12);
    PipelineStagePayload newStage2 = new PipelineStagePayload(3L, 12L, "Stage 3", "Descrption 3", OPEN, 3, 14);
    stages.add(existingStage);
    stages.add(newStage1);
    stages.add(newStage2);
    PipelinePayload entity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, stages);

    List<PipelineStagePayload> oldStages = new ArrayList<>();
    oldStages.add(existingStage);
    PipelinePayload oldEntity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, oldStages);
    PipelineEvent pipelineEvent = new PipelineEvent(entity, oldEntity);

    BDDMockito.doNothing().when(leadPipelineStageRepository).insertNewPipelineStage(anyLong(),anyInt(),anyString(),anyString(),anyInt(),anyLong(),anyLong());
    //when
    leadPipelineService.processLeadPipelineUpdatedEvent(pipelineEvent);
    //then
    Mockito.verify(leadPipelineStageRepository, times(1)).insertNewPipelineStage(newStage1.getId(),newStage1.getPosition(),newStage1.getName(),newStage1.getDescription(),newStage1.getWinLikelihood(),entity.getId(),entity.getTenantId());
    Mockito.verify(leadPipelineStageRepository, times(1)).insertNewPipelineStage(newStage2.getId(),newStage2.getPosition(),newStage2.getName(),newStage2.getDescription(),newStage2.getWinLikelihood(),entity.getId(),entity.getTenantId());
  }

  @Test
  public void givenDealPipelineEvent_whenNewStageAdded_shouldNotProcessLeadPipelineUpdatedEvent() {
    //given
    long updatedBy = 22L;
    List<PipelineStagePayload> stages = new ArrayList<>();
    PipelineStagePayload existingStage = new PipelineStagePayload(1L, 12L, "Stage 1", "Descrption 1", OPEN, 1, 12);
    PipelineStagePayload newStage = new PipelineStagePayload(2L, 12L, "Stage 2", "Descrption 2", OPEN, 2, 12);
    stages.add(existingStage);
    stages.add(newStage);
    PipelinePayload entity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.DEAL, new String[1], new String[1], true, stages);

    List<PipelineStagePayload> oldStages = new ArrayList<>();
    oldStages.add(existingStage);
    PipelinePayload oldEntity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.DEAL, new String[1], new String[1], true, oldStages);
    PipelineEvent pipelineEvent = new PipelineEvent(entity, oldEntity);
    List<LeadPipeline> leadPipelines = new ArrayList<>();
    LeadPipeline leadPipelineMock1 = mock(LeadPipeline.class);
    LeadPipeline leadPipelineMock2 = mock(LeadPipeline.class);
    leadPipelines.add(leadPipelineMock1);
    leadPipelines.add(leadPipelineMock2);

    //when
    leadPipelineService.processLeadPipelineUpdatedEvent(pipelineEvent);
    //then
    Mockito.verifyZeroInteractions(leadPipelineRepository);
    Mockito.verifyZeroInteractions(leadPipelineStageRepository);
  }

  @Test
  public void givenPipelineEvent_whenAssociatedWithLeadStageNameChanged_shouldUpdateLeadVersion() {
    //given
    long updatedBy = 22L;
    long tenantId = 12L;
    long pipelineStageId1 = 1L;
    long pipelineStageId2 = 2L;

    List<PipelineStagePayload> stages = new ArrayList<>();
    PipelineStagePayload updatedStage1 = new PipelineStagePayload(pipelineStageId1, tenantId, "Stage 11", "Descrption 1", OPEN, 1, 12);
    PipelineStagePayload updatedStage2 = new PipelineStagePayload(pipelineStageId2, tenantId, "stAge 2", "Descrption 1", OPEN, 1, 12);
    stages.add(updatedStage1);
    stages.add(updatedStage2);
    PipelinePayload updatedEntity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, stages);

    List<PipelineStagePayload> oldStages = new ArrayList<>();
    PipelineStagePayload oldStage1 = new PipelineStagePayload(pipelineStageId1, tenantId, "Stage 1", "Descrption 1", OPEN, 1, 12);
    PipelineStagePayload oldStage2 = new PipelineStagePayload(pipelineStageId2, tenantId, "Stage 2", "Descrption 1", OPEN, 1, 12);
    oldStages.add(oldStage1);
    oldStages.add(oldStage2);

    PipelinePayload oldEntity = new PipelinePayload(12L, tenantId, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, oldStages);
    PipelineEvent pipelineEvent = new PipelineEvent(updatedEntity, oldEntity);

    //when
    leadPipelineService.processLeadPipelineUpdatedEvent(pipelineEvent);
    //then
    Mockito.verify(leadRepository, times(1)).updateLeadVersionByPipelineStage(tenantId,pipelineStageId1);
    Mockito.verify(leadRepository, times(1)).updateLeadVersionByPipelineStage(tenantId,pipelineStageId2);
  }

  @Test
  public void givenPipelineEvent_whenAssociatedWithLeadStageNameIsNotChanged_shouldNotUpdateLeadVersion() {
    //given
    long updatedBy = 22L;
    long tenantId = 12L;
    long pipelineStageId1 = 1L;

    List<PipelineStagePayload> stages = new ArrayList<>();
    PipelineStagePayload updatedStage1 = new PipelineStagePayload(pipelineStageId1, tenantId, "Stage 11", "Descrption 1", OPEN, 1, 12);
    stages.add(updatedStage1);
    PipelinePayload updatedEntity = new PipelinePayload(12L, 12L, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, stages);


    PipelinePayload oldEntity = new PipelinePayload(12L, tenantId, updatedBy,"Pipeline", EntityType.LEAD, new String[1], new String[1], true, stages);
    PipelineEvent pipelineEvent = new PipelineEvent(updatedEntity, oldEntity);

    //when
    leadPipelineService.processLeadPipelineUpdatedEvent(pipelineEvent);
    //then
    Mockito.verifyZeroInteractions(leadRepository);
  }


  // END: Set active stage for lead pipeline
  private PipelineResponse aPipeline(long pipelineId, List<PipelineStageResponse> pipelineStages) {
    PipelineResponse pipeline = new PipelineResponse();
    pipeline.setId(pipelineId);
    pipeline.setName("nurturing pipeline");
    pipeline.setUnqualifiedReasons(new String[]{"Wrong number", "Did not pick phone"});
    pipeline.setLostReasons(new String[]{"Low budget", "Not interested"});
    pipeline.setStages(pipelineStages);
    pipeline.setActive(true);
    return pipeline;
  }

  private LeadPipeline aLeadPipeline(
      long tenantId, Long leadId, long pipelineId, Long... stageIds) {
    PipelineResponse pipelineResponse = new PipelineResponse();
    pipelineResponse.setId(pipelineId);

    List<PipelineStageResponse> pipelineStages =
        Arrays.stream(stageIds)
            .map(
                stageId -> {
                  PipelineStageResponse pipelineStageResponse = new PipelineStageResponse();
                  pipelineStageResponse.setId(stageId);
                  pipelineStageResponse.setPosition(stageId.intValue());
                  pipelineStageResponse.setName(stageId + "-name");
                  pipelineStageResponse.setForecastingType(OPEN);
                  pipelineStageResponse.setWinLikelihood(100);
                  return pipelineStageResponse;
                })
            .collect(toList());

    pipelineResponse.setStages(pipelineStages);
    Lead lead = new Lead();
    lead.setId(leadId);
    return LeadPipeline.create(tenantId, lead, pipelineResponse);
  }

  private List<PipelineStageResponse> pipelineStagesForPipeline(Long... stageIds) {
    return Arrays.stream(stageIds)
        .map((stageId) -> aPipelineStageResponse(stageId, stageId.intValue(), ForecastingType.OPEN))
        .collect(toList());
  }

  private PipelineStageResponse aPipelineStageResponse(Long stageId, int position, ForecastingType forecastingType) {
    PipelineStageResponse response = new PipelineStageResponse();
    response.setId(stageId);
    response.setName(stageId + "-name");
    response.setDescription(stageId + "-description");
    response.setPosition(position);
    response.setForecastingType(forecastingType);
    response.setWinLikelihood(100);
    return response;
  }

  private Lead aLead(Long pipelineId, Long pipelineStageId) {
    Lead lead = new Lead();
    lead.setPipeline(pipelineId);
    lead.setPipelineStage(pipelineStageId);
    lead.setVersion(0);
    return lead;
  }

  private LeadPipeline aMockedLeadPipeline(long pipelineId) {
    LeadPipeline pipeline = mock(LeadPipeline.class);
    given(pipeline.getId()).willReturn(1L);
    given(pipeline.getStages()).willReturn(emptyList());
    given(pipeline.getPipelineId()).willReturn(pipelineId);
    return pipeline;
  }

  private LeadPipeline aMockedLeadPipelineWithStages(long pipelineId, long stageId) {
    LeadPipelineStage pipelineStage = mock(LeadPipelineStage.class);
    given(pipelineStage.getPipelineStageId()).willReturn(stageId);
    given(pipelineStage.getForecastingType()).willReturn(OPEN);
    LeadPipeline pipeline = mock(LeadPipeline.class);
    given(pipeline.getId()).willReturn(1L);
    given(pipeline.getStages()).willReturn(asList(pipelineStage));
    given(pipeline.getPipelineId()).willReturn(pipelineId);
    return pipeline;
  }

  private LeadPipelineStage aLeadPipelineStage(long stageId, ForecastingType forecastingType) {
    LeadPipelineStage pipelineStage = mock(LeadPipelineStage.class);
    given(pipelineStage.getPipelineStageId()).willReturn(stageId);
    given(pipelineStage.getForecastingType()).willReturn(forecastingType);
    given(pipelineStage.getStatus()).willReturn(PipelineStageStatus.COMPLETED);
    return pipelineStage;
  }
}
